// Imports
const supabaseInstance = require('./supabaseClient').supabase;
const logger = require('./logger');

async function getCustomerFromRfid(rfid) {
  logger.info('[getCustomerFromRfid] Called', { rfid });
  try {
    const { data, error } = await supabaseInstance
      .from('Customer')
      .select('customerAuthUID, rfid, photo, mobile, email, customerName')
      .eq('rfid', rfid)
      .single();

    if (error) {
      logger.error(`[getCustomerFromRfid] Error fetching customer auth UID for RFID ${rfid}:`, error);
      return null;
    }
    logger.info('[getCustomerFromRfid] Query result', { data });
    return data ? data : null;
  } catch (err) {
    logger.error(`[getCustomerFromRfid] Unexpected error fetching customer auth UID for RFID ${rfid}:`, err);
    return null;
  }
}

module.exports = {
    getCustomerFromRfid
};