let express = require("express");
let router = express.Router();
let supabaseInstance = require("../../services/supabaseClient").supabase;
const schedule = require("node-schedule");
const { sendMessNotification } = require("../firebase");
const logger = require("../../services/logger");
require("dotenv").config();

let breakfastNotifications = [
  "🌅 Good morning! Don’t miss out on breakfast – RSVP now and start your day right!",
	"🍳 Breakfast is calling! RSVP now to fuel up for the day ahead.",
	"🥐 Ready for a delicious morning? Don’t forget to RSVP for your breakfast!",
	"☕ Wake up and smell the coffee! RSVP for breakfast before it’s too late.",
	"🥓 Your breakfast awaits! RSVP now and make your morning a tasty one.",
];

let lunchNotifications = [
  "🍽 It’s lunchtime soon! Hurry up and RSVP for your midday feast.",
	"🥗 Lunch break loading… Have you RSVP’d yet? Don’t miss your meal!",
	"🍔 The clock’s ticking! RSVP for your lunch before the deadline.",
	"🥪 Your lunch needs you! RSVP now and get ready for a delicious meal.",
	"🌯 Time to make lunch plans! RSVP before your spot is gone.",
];

let highTeaNotifications = [
  "☕ Tea time is near! RSVP for a delightful High Tea experience.",
  "🎂 High Tea awaits! RSVP now for a perfect mid-afternoon break.",
  "🧁 Don’t miss out on sweet treats! RSVP for High Tea before it’s too late.",
  "🍵 Time for a tea break! RSVP and enjoy a relaxing afternoon with treats.",
  "🍩 Your High Tea moment is here! RSVP now and treat yourself to something delicious.",
];

let dinnerNotifications = [
  "🍛 Dinner’s around the corner! Don’t forget to RSVP for your evening meal.",
	"🍲 End your day on a high note – RSVP for dinner before the deadline!",
	"🍽 Your dinner’s waiting – RSVP now to enjoy a relaxing meal.",
	"🍝 Make dinner plans! RSVP before time runs out.",
	"🍕 Hungry for dinner? RSVP now to secure your spot at the table.",
];


const scheduleDailyNotificationJobs = async () => {
  try {
    // Step 1: Fetch all meal timings (rsvpDeadline) for each mess which has turned on notifications
    const { data: outlets, error: outletError } = await supabaseInstance
      .from("Outlet")
      .select("outletId, outletName")
      .eq("hasMess", true)
      .eq("messNotifications", true);

    if (outletError) throw outletError;

    const outletIdList = outlets.map((outlet) => outlet.outletId);
    const outletNameMap = Object.fromEntries(
      outlets.map((outlet) => [outlet.outletId, outlet.outletName])
    );
    const { data: mealTimings, error: mealTimeError } = await supabaseInstance
      .from("Meal_Timings")
      .select("mealTimingId, rsvpDeadline, mealTypeId, outletId, enabled")
      .in("outletId", outletIdList)
      .eq("enabled", true);

    if (mealTimeError) throw mealTimeError;

    mealTimings?.forEach((meal) => {
      const { rsvpDeadline, mealTypeId, outletId } = meal;
      const outletName = outletNameMap[outletId];

      // Get the time part of the rsvpDeadline (ignoring the date)
      const deadlineTime = new Date(rsvpDeadline);
      const deadlineHour = deadlineTime.getHours();
      const deadlineMinute = deadlineTime.getMinutes();

      // Calculate notification time (15 minutes before deadline)
      const notificationMinute = (deadlineMinute - 15 + 60) % 60; // Ensure positive value
      const notificationHour =
        (deadlineHour + Math.floor((deadlineMinute - 15) / 60)) % 24;

      // Create a RecurrenceRule for daily notification
      const rule = new schedule.RecurrenceRule();
      rule.hour = notificationHour;
      rule.minute = notificationMinute;

      // Schedule the job to run daily at the calculated notification time with name as outletId-mealTypeId
      const jobName = `${outletId}-${mealTypeId}`;
      schedule.scheduleJob(jobName, rule, () => {
        sendRSVPNotification(mealTypeId, outletId, outletName);
      });
    });
  } catch (err) {
    logger.error("Error scheduling notification jobs:", err);
  }
};
function getNotificationMessage(mealTypeId) {
  const notifications = {
    1: breakfastNotifications,
    2: lunchNotifications,
    3: highTeaNotifications,
    4: dinnerNotifications
  };
  const messages = notifications[mealTypeId] || dinnerNotifications;
  return messages[Math.floor(Math.random() * messages.length)];
}

// Function to send RSVP notifications to users who have not responded
const sendRSVPNotification = async (mealTypeId, outletId, outletName) => {
  try {
    // Step 2: Fetch whitelisted users who have NOT RSVP'd (either yes or no)
    const currentDate = new Date().toISOString().split("T")[0]; // Today's date
    let { data: whitelistUsers, error } = await supabaseInstance
      .from("Mess_Whitelist")
      .select("customerAuthUID")
      .eq("outletId", outletId);

    if (error) throw(error);

    // get the rsvp status of users for current meal
    const { data: rsvpStatus, error: rsvpError } = await supabaseInstance
      .from("RSVP")
      .select("customerAuthUID")
      .eq("outletId", outletId)
      .eq("rsvpDate", currentDate)
      .eq("mealTypeId", mealTypeId);

    if (rsvpError) throw(rsvpError);

    const nonRSVPUsers = whitelistUsers.filter((user) => {
      return !rsvpStatus.some((rsvp) => rsvp.customerAuthUID === user.customerAuthUID);
    });


    // Step 3: Fetch the FCM tokens of the users from the customers table
    const customerUIDs = nonRSVPUsers.map((user) => user.customerAuthUID);

    const { data: fcmTokens, error: tokenError } = await supabaseInstance
      .from("FCM_Tokens")
      .select("fcmToken")
      .in("customerAuthUID", customerUIDs);

    if (tokenError) throw tokenError;

    const tokens = fcmTokens.map((token) => token.fcmToken).filter(Boolean);

    // Send notifications
    for (const token of tokens) {
      let title;
      if(mealTypeId === 1) {title = "Breakfast RSVP Reminder"}else
      if(mealTypeId === 2) {title = "Lunch RSVP Reminder"}else
      if(mealTypeId === 3) {title = "High Tea RSVP Reminder"}else
      if(mealTypeId === 4) {title = "Dinner RSVP Reminder"}; 
      await sendMessNotification(title, getNotificationMessage(mealTypeId),token, outletName, outletId);
    }
  } catch (error) {
    logger.error("Error sending RSVP notifications:", error);
  }
};

// Function to schedule new notification jobs for a meal at an outlet
const scheduleNewNotificationJobs = async (outletId, mealTypeId, rsvpDeadline, outletName, enabled=true) => {

  try {
    // Generate the job name using outletId and mealTypeId
    const jobName = `${outletId}-${mealTypeId}`;

    // Cancel the existing job if it exists
    const existingJob = schedule.scheduledJobs[jobName];
    if (existingJob) {
      existingJob.cancel();
    }

    // If the meal is not enabled, do not schedule a new job
    if (!enabled) {
      return;
    }

    // Get the time part of the rsvpDeadline (ignoring the date)
    const deadlineTime = new Date(rsvpDeadline);
    const deadlineHour = deadlineTime.getHours();
    const deadlineMinute = deadlineTime.getMinutes();

    // Calculate notification time (15 minutes before deadline)
    const notificationMinute = (deadlineMinute - 15 + 60) % 60; // Ensure positive value
    const notificationHour = (deadlineHour + Math.floor((deadlineMinute - 15) / 60)) % 24;

    // Create a RecurrenceRule for daily notification
    const rule = new schedule.RecurrenceRule();
    rule.hour = notificationHour;
    rule.minute = notificationMinute;

    // Schedule the new job to run daily
    schedule.scheduleJob(jobName, rule, () => {
      sendRSVPNotification(mealTypeId, outletId, outletName);
    });

  } catch (err) {
    logger.error(`Error scheduling new notification job for outlet ${outletId}, meal ${mealTypeId}:`, err);
  }
};


// Call the function to schedule the daily notification jobs when the app starts
if(process.env.ENV !== "dev") scheduleDailyNotificationJobs();



module.exports = router;
module.exports.scheduleNewNotificationJobs = scheduleNewNotificationJobs;