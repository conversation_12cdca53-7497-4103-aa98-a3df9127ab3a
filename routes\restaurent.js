var express = require("express");
var router = express.Router();
var supabaseInstance = require("../services/supabaseClient").supabase;
var {outletSelectString} = require("../services/supabaseCommonValues").value;
const multer = require("multer");
const upload = multer();

router.get("/", function (req, res, next) {
  res.send({ success: true, message: "respond send from restaurent.js" });
});

/**
 * @description This route is used to get all restaurant data.
 * @route GET /restaurent/getRestaurant
 * @return {object} - Returns a success message and the restaurant data if successful.
 */
router.get("/getRestaurant", async (req, res) => {
  try {
    const { data, error } = await supabaseInstance
      .from("Restaurant")
      .select(
        "*, bankDetailsId(*), restaurantAdminId(*), Restaurant_category!left(*, categoryId(*)), Tax!left(taxid, taxname, tax)"
      );
    console.log(data);
    console.log(error);
    if (data) {
      res.status(200).json({
        success: true,
        message: "Data fetch succesfully",
        data: data,
      });
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

/**
 * @description This route is used to get restaurant data by restaurantId.
 * @route GET /restaurent/getRestaurant/:restaurantId
 * @param {string} restaurantId - The ID of the restaurant to fetch.
 * @return {object} - Returns a success message and the restaurant data if successful.
 */
router.get("/getRestaurant/:restaurantId", async (req, res) => {
  const {restaurantId} = req.params;
  try {
    const { data, error } = await supabaseInstance
      .from("Restaurant")
      .select("*")
      .eq("restaurantId",restaurantId)
   
    if (data) {
      res.status(200).json({
        success: true,
        message: "Data fetch succesfully",
        data: data,
      });
    }else{
      throw error;
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

/**
 * @description This route is used to get a paginated list of restaurants with optional search functionality.
 * @route GET /restaurent/getRestaurantList
 * @param {number} page - The page number for pagination (default is 1).
 * @param {number} perPage - The number of items per page for pagination (default is 10).
 * @param {string} searchText - Optional search text to filter restaurants by address or restaurant name.
 * @return {object} - Returns a success message, restaurant data, and pagination metadata if successful.
 */
router.get("/getRestaurantList", async (req, res) => {
  const { page, perPage, searchText} = req.query; // Extract query parameters
  const pageNumber = parseInt(page) || 1;
  const itemsPerPage = parseInt(perPage) || 10;

  try {
    let query =  supabaseInstance
      .from("Restaurant")
      .select("*, bankDetailsId(*), campusId(*),restaurantAdminId(*), Restaurant_category!left(*, categoryId(*)), Timing!left(*), Tax!left(taxid, taxname, tax)", { count: "exact" })
      .range((pageNumber - 1) * itemsPerPage, pageNumber * itemsPerPage - 1)
      .order("restaurantAdminId", { ascending: true });
    if(searchText) {
      query = query.or(`address.ilike.%${searchText}%,restaurantName.ilike.%${searchText}%`);
      // query = query.ilike('outletName', `%${searchText}%`);
    }
    const { data, error, count } = await query;
    if (data) {
      const totalPages = Math.ceil(count / itemsPerPage);
      res.status(200).json({
        success: true,
        data,
        meta: {
          page: pageNumber,
          perPage: itemsPerPage,
          totalPages,
          totalCount: count,
        },
      });
    } else {
      throw error;
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error?.message || error });
  }
});

/**
 * @description This route is used to create a new restaurant.
 * @route POST /restaurent/createRestaurant
 * @body {string} email - The email of the restaurant admin.
 * @body {string} password - The password for the restaurant admin.
 * @body {object} bankDetailsId - The bank details of the restaurant.
 * @body {object} restaurantAdminId - The details of the restaurant admin.
 * @body {string} restaurantName - The name of the restaurant.
 * @body {string} mobile - The mobile number of the restaurant admin.
 * @body {string} GSTIN - The GSTIN of the restaurant.
 * @body {string} FSSAI_License - The FSSAI license of the restaurant.
 * @body {string} campusId - The ID of the campus where the restaurant is located.
 * @body {string} address - The address of the restaurant.
 * @body {boolean} isVeg - Whether the restaurant serves vegetarian food.
 * @body {boolean} isNonVeg - Whether the restaurant serves non-vegetarian food.
 * @body {string} openTime - The opening time of the restaurant.
 * @body {string} closeTime - The closing time of the restaurant.
 * @body {Array} Restaurant_category - An array of category IDs for the restaurant.
 * @body {Array} Timing - An array of timing objects for the restaurant.
 */
router.post("/createRestaurant", async (req, res) => {
  const {
    email,
    password,
    bankDetailsId,
    restaurantAdminId,
    restaurantName,
    mobile,
    GSTIN,
    FSSAI_License,
    campusId,
    address,
    isVeg,
    isNonVeg,
    openTime,
    closeTime,
    Restaurant_category,
    Timing
  } = req.body;
  try {
    const { data, error } = await supabaseInstance.auth.signUp(
      {
        email: email,
        password: password,
        options: {
          data: {
            isRestaurant: true,
            isOutlet: false,
          }
        }
      })
    if (data?.user) {
      const restaurantId = data.user.id;

      const bankDetails = await supabaseInstance.from("BankDetails").insert({ accountNumber: bankDetailsId.accountNumber || null, BankName: bankDetailsId.BankName, IFSCCode: bankDetailsId.IFSCCode }).select().maybeSingle();
      const _bankDetailsId = bankDetails.data.bankDetailsId;

      const restaurantAdminDetails = await supabaseInstance.from("Restaurant_Admin").insert({ name: restaurantAdminId?.name, mobile: restaurantAdminId?.mobile || null, email: restaurantAdminId?.email, address: restaurantAdminId?.address, pancard: restaurantAdminId?.pancard }).select().maybeSingle();
      const _restaurantAdminId = restaurantAdminDetails.data.restaurantAdminId;

      let objectData = {
        restaurantId,
        restaurantName,
        email,
        mobile,
        GSTIN,
        FSSAI_License,
        bankDetailsId: _bankDetailsId,
        restaurantAdminId: _restaurantAdminId,
        campusId,
        address,
        isVeg,
        isNonVeg,
        openTime,
        closeTime
      }

      if (openTime) {
        objectData.openTime = openTime;
      }
      if (closeTime) {
        objectData.closeTime = closeTime;
      }
      const inserRestaurentNewkDetails = await supabaseInstance.from("Restaurant").insert(objectData).select("*").maybeSingle();

      const taxPostBody = [
        {restaurantId, taxname: "CGST"},
        {restaurantId, taxname: "SGST"}
      ]
      const taxResponse = await supabaseInstance.from("Tax").insert(taxPostBody).select();

      for (let categoryItem of Restaurant_category) {
        const restaurentCategoryResponse = await supabaseInstance
          .from("Restaurant_category")
          .insert({ restaurantId, categoryId: categoryItem })
          .select("*")
          .maybeSingle();
        console.log("restaurentCategoryResponse", restaurentCategoryResponse);
      }

      for (let data of Timing) {
        const restaurentTimeResponse = await supabaseInstance
          .from("Timing")
          .insert({ restaurantId, dayId: data.dayId, openTime: data.openTime || null, closeTime: data.closeTime || null })
          .select("*")
          .maybeSingle();
        console.log("restaurentTimeResponse", restaurentTimeResponse);
      }

      if (inserRestaurentNewkDetails.data) {
        res.send({
          success: true,
          message: "Restaurant created successfully",
          data: inserRestaurentNewkDetails.data,

        });
      } else {
        throw inserRestaurentNewkDetails.error
      }
    } else {
      throw error;
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
})

/**
 * @description This route is used to add or update the FSSAI license photo for a restaurant.
 * @route POST /restaurent/upsertFssaiLicensePhoto
 * @param {string} restaurantId - The ID of the restaurant for which the FSSAI license photo is being uploaded.
 * @param {file} file - The FSSAI license photo file to be uploaded.
 * @return {object} - Returns a success message and the updated restaurant data if successful. 
 */
router.post("/upsertFssaiLicensePhoto",upload.single('file'), async (req, res) => {
  const { restaurantId } = req.body;
  try {
    const { data, error } = await supabaseInstance
      .storage
      .from('fssai-license')
      .upload(restaurantId + ".webp", req.file.buffer, {
        cacheControl: '3600',
        upsert: true,
        contentType: 'image/webp'
      })

    if (data?.path) {
      const publickUrlresponse = await supabaseInstance.storage.from('fssai-license').getPublicUrl(data?.path);
      if (publickUrlresponse?.data?.publicUrl) {
        const publicUrl = publickUrlresponse?.data?.publicUrl;
        const restaurantData = await supabaseInstance.from("Restaurant").update({ FSSAI_License: `${publicUrl}?${Date.now}` }).eq("restaurantId", restaurantId).select("*, bankDetailsId(*), campusId(*),restaurantAdminId(*), Restaurant_category!left(*, categoryId(*)), Tax!left(taxid, taxname, tax)").maybeSingle();
        res.status(200).json({
          success: true,
          data: restaurantData.data,
        });
      } else {
        throw publickUrlresponse.error || "Getting Error in PublicUrl"
      }
    } else {
      throw error
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error });
  }
})

const {
  sendMobileOtp,
  verifyMobileOtp,
  sendEmail,
  sendMobileSMS,
  generateOTP,
} = require("../services/msf91Service");
var msg91config = require("../configs/msg91Config");

/**
 * @description This route is used to get the MFA (Multi-Factor Authentication) status for a restaurant based on the provided email.
 * @route POST /restaurent/getMfaStatus
 * @param {string} email - The email of the restaurant to check the MFA status.
 * @return {object} - Returns a success message and the MFA status if successful.
 */
router.post("/getMfaStatus", async (req, res) => {
  const {email}= req.body;

  if(!email || email == null){
    return res.status(400).json({ success: false, error: "Email is required" });
  }
  try {
    const { data, error } = await supabaseInstance
      .from("Outlet")
      .select("mfaEnabled")
      .eq("email", email)
      .maybeSingle();

      if(error && error.code !== "PGRST116") throw error;
    if (data) {
      return res.status(200).json({
        success: true,
        mfaEnabled: data.mfaEnabled,
      });
    } else {
      //check if it is outlet staff
      const { data, error } = await supabaseInstance
        .from("Outlet_Staff")
        .select("email, Outlet(outletId, mfaEnabled)")
        .eq("email", email)
        .maybeSingle();
      
      if (error) throw error;

      if (data) {
        return res.status(200).json({
          success: true,
          mfaEnabled: false,
        });
      }

      return res.status(200).json({
        success:false,
        message : "No outlet found on this email."
      })
    }
  } catch (error) {
    console.log(error)
    return res.status(500).json({ success: false, error: error });
  }
});

/**
 * @description This route is used to send a login OTP for a restaurant based on the provided email.
 * @route POST /restaurent/sendLoginOtpForRestaurant
 * @param {string} email - The email of the restaurant to send the OTP to.
 * @return {object} - Returns a success message and the OTP response if successful.
 */
router.post("/sendLoginOtpForRestaurant", async (req, res) => {
  const { email } = req.body;

  if (!email) {
    return res.status(400).json({ success: false, error: "Email is required" });
  }

  try {
    const { data, error } = await supabaseInstance
      .from("Outlet")
      .select("mobile, outletName")
      .eq("email", email)
      .maybeSingle();

    if (error) throw error;

    if (!data) {
      return res.status(404).json({ success: false, error: "Outlet not found" });
    }

    const mobile = "91" + data.mobile;
    const name = data.outletName;

    const responseData = await sendMobileOtp(mobile, msg91config.config.otp_template_id, name);

    res.status(200).json({
      success: true,
      data: responseData,
      mobile: mobile,
    });

  } catch (error) {
    console.error("Error in sendLoginOtpForRestaurant:", error);
    res.status(500).json({ 
      success: false, 
      error: error.message || "An unexpected error occurred" 
    });
  }
});

/**
 * @description This route is used to login a restaurant based on the provided email and OTP.
 * @route POST /restaurent/restaurentLogin1
 * @param {string} email - The email of the restaurant to login.
 * @param {string} password - The password of the restaurant to login.
 * @param {string} mobile - The mobile number of the restaurant to login.
 * @param {string} otp - The OTP of the restaurant to login.
 * @return {object} - Returns a success message and the login data if successful.
 */
router.post("/restaurentLogin1", async (req, res) => {
  let { email, password, mobile, otp } = req.body;
  try {
    if(mobile === "919999999999") { 
      response = { api_success: true }; // Mock success for testing
    } else {
      console.log('mobile otp', mobile, otp);
      response = await verifyMobileOtp(mobile, otp);
    }

    if (response.api_success === true || response.message === "Mobile no. already verified") {
      // Proceed with login regardless of whether the number was just verified or was already verified
      const { data, error } = await supabaseInstance.auth.signInWithPassword({
        email: email,
        password: password,
      });

      if (data?.user) {
        const id = data.user.id;
        console.log("id", id);
        console.log("data?.user?.user_metadata----->", data?.user?.user_metadata);

        let responseData = {};

        if (data?.user?.user_metadata?.isRestaurant === true) {
          const restaurantData = await supabaseInstance
            .from("Restaurant")
            .select(outletSelectString)
            .eq("restaurantId", id)
            .maybeSingle();
          responseData.outletData = restaurantData.data;
        } else if (data?.user?.user_metadata?.isOutlet === true) {
          const outletData = await supabaseInstance
            .from("Outlet")
            .select(outletSelectString)
            .eq("outletId", id)
            .maybeSingle();

            if(outletData.data.isDelete){
              return res.status(400).json({ success: false, error: "Account is deleted" });
            }
          responseData.outletData = outletData.data;
        } else if (data?.user?.user_metadata?.isOutletStaff === true) {
          const outletStaffData = await supabaseInstance
            .from("Outlet_Staff")
            .select("*, roleId(*)")
            .eq("outletStaffAuthUId", id)
            .maybeSingle();
          const outletData = await supabaseInstance
            .from("Outlet")
            .select(outletSelectString)
            .eq("outletId", outletStaffData.data.outletId)
            .maybeSingle();

            
            if(outletData.data.isDelete){
              return res.status(400).json({ success: false, error: "Account is deleted" });
            }
          responseData.outletData = outletData.data;
          responseData.outletStaffData = outletStaffData;
        }

        res.status(200).json({
          success: true,
          message: "Login successful",
          data: responseData,
        });
      } else {
        throw new Error("Login failed");
      }
    } else {
      // Handle other error cases
      res.status(400).json({ 
        success: false, 
        error: response.message, 
        message: "OTP verification failed. Please try again." 
      });
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

/**
 * @description This route is used to login a restaurant based on the provided email and OTP.
 * @route POST /restaurent/restaurentLogin
 * @param {string} email - The email of the restaurant to login.
 * @param {string} password - The password of the restaurant to login.
 * @param {string} mobile - The mobile number of the restaurant to login.
 * @param {string} otp - The OTP of the restaurant to login.
 * @return {object} - Returns a success message and the login data if successful.
 */
router.post("/restaurentLogin", async (req, res) => {
  let { email, password, mobile, otp } = req.body;
  try {

    // 
     // Proceed with login regardless of whether the number was just verified or was already verified
      const { data, error } = await supabaseInstance.auth.signInWithPassword({
        email: email,
        password: password,
      });

      if (data?.user) {
        const id = data.user.id;
        console.log("id", id);
        console.log("data?.user?.user_metadata----->", data?.user?.user_metadata);

        let responseData = {};

        if (data?.user?.user_metadata?.isRestaurant === true) {
          const restaurantData = await supabaseInstance
            .from("Restaurant")
            .select(outletSelectString)
            .eq("restaurantId", id)
            .maybeSingle();
          responseData.outletData = restaurantData.data;
        } else if (data?.user?.user_metadata?.isOutlet === true) {
          const outletData = await supabaseInstance
            .from("Outlet")
            .select(outletSelectString)
            .eq("outletId", id)
            .maybeSingle();

            if(outletData.data.isDelete){
              return res.status(400).json({ success: false, error: "Account is deleted" });
            }
          responseData.outletData = outletData.data;
        } else if (data?.user?.user_metadata?.isOutletStaff === true) {
          const outletStaffData = await supabaseInstance
            .from("Outlet_Staff")
            .select("*, roleId(*)")
            .eq("outletStaffAuthUId", id)
            .maybeSingle();
          const outletData = await supabaseInstance
            .from("Outlet")
            .select(outletSelectString)
            .eq("outletId", outletStaffData.data.outletId)
            .maybeSingle();
            if(outletData.data.isDelete){
              return res.status(400).json({ success: false, error: "Account is deleted" });
            }
          responseData.outletData = outletData.data;
          responseData.outletStaffData = outletStaffData;
        }

        res.status(200).json({
          success: true,
          message: "Login successful",
          data: responseData,
        });
      } else {
        throw new Error("Login failed");
      }
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

/**
 * @description This route is used to update the packaging charge for a restaurant.
 * @route POST /restaurent/updatePackagingCharge/:restaurantId
 * @param {string} restaurantId - The ID of the restaurant for which the packaging charge is being updated.
 * @param {number} packaging_charge - The new packaging charge to be set for the restaurant.
 * @return {object} - Returns a success message and the updated restaurant data if successful.
 */
router.post("/updatePackagingCharge/:restaurantId", async (req, res) => {
  const { restaurantId } = req.params;
  const {packaging_charge}  = req.body;

  try {
    const { data, error } = await supabaseInstance
      .from("Restaurant")
      .update({packaging_charge})
      .eq("restaurantId",restaurantId)
      .select("*");

    if (data) {
      console.log("data-->",data)
      res.status(200).json({
        success: true,
        data: data,
      });
    } else {
      throw error
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

/**
 * @description This route is used to create a new category for a restaurant.
 * @route POST /restaurent/createCategory
 * @param {string} restaurantId - The ID of the restaurant for which the category is being created.
 * @param {boolean} active - The status of the category.
 * @param {string} categoryname - The name of the category.
 * @return {object} - Returns a success message and the created category data if successful.
 */
router.post("/createCategory", async (req, res) => {
  const { restaurantId, active, categoryname } = req.body;
  try {
    const { data, error } = await supabaseInstance
      .from("Menu_Categories")
      .insert({ restaurantId, active, categoryname })
      .select("*")

    if (data) {
      res.status(200).json({
        success: true,
        data: data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error });
  }
});

/**
 * @description This route is used to update an existing category for a restaurant.
 * @route POST /restaurent/updateCategory/:categoryid
 * @param {string} categoryid - The ID of the category to be updated.
 * @param {object} menuCategoryData - The data to be updated for the category.
 * @return {object} - Returns a success message and the updated category data if successful.
 */
router.post("/updateCategory/:categoryid", async (req, res) => {

  const { categoryid } = req.params;
  const menuCategoryData = req.body;
  ;
  try {
    const { data, error } = await supabaseInstance
      .from("Menu_Categories")
      .update({ ...menuCategoryData })
      .eq("categoryid", categoryid)
      .select("*");

    if (data) {
      res.status(200).json({
        success: true,
        message: "Data updated succesfully",
        data: data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

/**
 * @description This route is used to get a category by its ID.
 * @route GET /restaurent/getCategoryById/:categoryid
 * @param {string} categoryid - The ID of the category to be retrieved.
 * @return {object} - Returns a success message and the category data if successful.
 */
router.get("/getCategoryById/:categoryid", async (req, res) => {
  const {categoryid} = req.params
  try {
    const { data, error } = await supabaseInstance
      .from("Menu_Categories")
      .select("*")
      .eq("categoryid",categoryid)

    if (data) {
      res.status(200).json({
        success: true,
        data: data,
      });
    } else {
      throw error
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

/**
 * @description This route is used to get all categories for a restaurant.
 * @route GET /restaurent/category/:restaurantId
 * @param {string} restaurantId - The ID of the restaurant for which the categories are being retrieved.
 * @return {object} - Returns a success message and the category data if successful.
 */
router.get("/category/:restaurantId", async (req, res) => {
  const { restaurantId } = req.params;
  try {
    const { data, error } = await supabaseInstance
      .from("Menu_Categories")
      .select("*")
      .eq("restaurantId",restaurantId)

    if (data) {
      res.status(200).json({
        success: true,
        data: data,
      });
    } else {
      throw error
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

/**
 * @description This route is used to upsert an image for a category.
 * @route POST /restaurent/upsertCategoryImage
 * @param {string} categoryid - The ID of the category for which the image is being upserted.
 * @return {object} - Returns a success message and the updated category data if successful.
 */
router.post("/upsertCategoryImage", upload.single('file'), async (req, res) => {
  const { categoryid } = req.body;
  console.log("categoryid",categoryid)
  try {
    const { data, error } = await supabaseInstance
      .storage
      .from('category-image')
      .upload(categoryid + ".webp", req.file.buffer, {
        cacheControl: '3600',
        upsert: true,
        contentType: 'image/webp'
      })

    if (data?.path) {
      const publickUrlresponse = await supabaseInstance.storage.from('category-image').getPublicUrl(data?.path);
      console.log("publickUrlresponse",publickUrlresponse)
      if (publickUrlresponse?.data?.publicUrl) {
        const publicUrl = publickUrlresponse?.data?.publicUrl;
        const menuCategoryData = await supabaseInstance.from("Menu_Categories").update({ category_image_url: `${publicUrl}?${Date.now}` }).eq("categoryid", categoryid).select("*").maybeSingle();
        res.status(200).json({
          success: true,
          data: menuCategoryData.data,
        });
      } else {
        throw publickUrlresponse.error || "Getting Error in PublicUrl"
      }
    } else {
      throw error
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error });
  }
})

/**
 * @description This route is used to create a new parent category for a restaurant.
 * @route POST /restaurent/createParentCategory
 * @param {string} restaurantId - The ID of the restaurant for which the parent category is being created.
 * @param {boolean} status - The status of the parent category.
 * @param {string} parentCategoryName - The name of the parent category.
 * @param {array} category - The IDs of the categories to be associated with the parent category.
 * @return {object} - Returns a success message and the created parent category data if successful.
 */
router.post("/createParentCategory", async (req, res) => {
  const { restaurantId, status, parentCategoryName, category } = req.body;
  try {
    const { data, error } = await supabaseInstance
      .from("Menu_Parent_Categories")
      .insert({ restaurantId, status, parentCategoryName })
      .select("*")

    if (data) {
      const  parent_category_id = data[0].parent_category_id;
    
      for(let value of category) {
       const updatedData = await supabaseInstance
        .from("Menu_Categories")
        .update({parent_category_id:parent_category_id})
        .eq("categoryid", value)
        .select("*")
      }
      res.status(200).json({
        success: true,
        data: data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error });
  }
});

/**
 * @description This route is used to update an existing parent category for a restaurant.
 * @route POST /restaurent/updateParentCategory/:parent_category_id
 * @param {string} parent_category_id - The ID of the parent category to be updated.
 * @param {object} menuParentCategoryData - The data to be updated for the parent category.
 * @return {object} - Returns a success message and the updated parent category data if successful.
 */
router.post("/updateParentCategory/:parent_category_id", async (req, res) => {
  const { parent_category_id } = req.params;
  const  { status, parentCategoryName, category} = req.body;
  try {
    const { data, error } = await supabaseInstance
      .from("Menu_Parent_Categories")
      .update({ status, parentCategoryName })
      .eq("parent_category_id",parent_category_id)
      .select("*");

    if (data) {
      
      const parent_category_id =data[0].parent_category_id;
      if(category){

      let updated =  await supabaseInstance
         .from("Menu_Categories")
         .update({parent_category_id:null})
         .eq("parent_category_id", parent_category_id)
         .select("*")

      for(let value of category) {
        const updatedData = await supabaseInstance
         .from("Menu_Categories")
         .update({parent_category_id:parent_category_id})
         .eq("categoryid", value)
         .select("*")
       }
      }
      res.status(200).json({
        success: true,
        data: data,
      });
    } else {
      throw error
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

/**
 * @description This route is used to get all parent categories for a restaurant.
 * @route GET /restaurent/getParentCategory/:restaurantId
 * @param {string} restaurantId - The ID of the restaurant for which the parent categories are being retrieved.
 * @return {object} - Returns a success message and the parent category data if successful.
 */
router.get("/getParentCategory/:restaurantId", async (req, res) => {
  const {restaurantId} = req.params
  try {
    const { data, error } = await supabaseInstance
      .from("Menu_Parent_Categories")
      .select("*")
      .eq("restaurantId",restaurantId)

    if (data) {
      res.status(200).json({
        success: true,
        data: data,
      });
    } else {
      throw error
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

/**
 * @description This route is used to get a parent category by its ID.
 * @route GET /restaurent/getParentCategoryById/:parent_category_id
 * @param {string} parent_category_id - The ID of the parent category to be retrieved.
 * @return {object} - Returns a success message and the parent category data if successful.
 */
router.get("/getParentCategoryById/:parent_category_id", async (req, res) => {
  const {parent_category_id} = req.params;
  try {
    const { data, error } = await supabaseInstance
      .from("Menu_Parent_Categories")
      .select("*,Menu_Categories!left(*)")
      .eq("parent_category_id",parent_category_id)

    if (data) {
      res.status(200).json({
        success: true,
        data: data,
      });
    } else {
      throw error
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

/**
 * @description This route is used to upsert an image for a parent category.
 * @route POST /restaurent/upsertParentCategoryImage
 * @param {string} parent_category_id - The ID of the parent category for which the image is being upserted.
 * @return {object} - Returns a success message and the updated parent category data if successful.
 */
router.post("/upsertParentCategoryImage",upload.single('file'), async (req, res) => {
  const { parent_category_id } = req.body;
  try {
    const { data, error } = await supabaseInstance
      .storage
      .from('category-image')
      .upload(parent_category_id + ".webp", req.file.buffer, {
        cacheControl: '3600',
        upsert: true,
        contentType: 'image/webp'
      })

    if (data?.path) {
      const publickUrlresponse = await supabaseInstance.storage.from('category-image').getPublicUrl(data?.path);
      if (publickUrlresponse?.data?.publicUrl) {
        const publicUrl = publickUrlresponse?.data?.publicUrl;
        const parentCategoryData = await supabaseInstance.from("Menu_Parent_Categories").update({ parent_category_image_url: `${publicUrl}?${Date.now}` }).eq("parent_category_id", parent_category_id).select("*").maybeSingle();
        res.status(200).json({
          success: true,
          data: parentCategoryData.data,
        });
      } else {
        throw publickUrlresponse.error || "Getting Error in PublicUrl"
      }
    } else {
      throw error
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error });
  }
})

/**
 * @description This route is used to update the tax charge for a restaurant.
 * @route POST /restaurent/updateTaxCharge
 * @param {array} tax - The tax charges to be updated.
 * @return {object} - Returns a success message and the updated tax charge data if successful.
 */
router.post("/updateTaxCharge", async (req, res) => {

  const { tax } = req.body;
  try {
    for (let data of tax) {
      taxData = await supabaseInstance
        .from("Tax")
        .update({ tax: data.tax })
        .select("*")
        .eq("taxid", data.taxid)
    }
    if (taxData) {
      res.status(200).json({
        success: true,
        message: "Data updated succesfully",
      });
    } else {
      throw error;
    }
   
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

/**
 * @description This route is used to delete a category by its ID.
 * @route DELETE /restaurent/deleteCategory/:categoryid
 * @param {string} categoryid - The ID of the category to be deleted.
 * @return {object} - Returns a success message and the deleted category data if successful.
 */
router.delete("/deleteCategory/:categoryid", async (req, res) => {
  const { categoryid } = req.params;
  try {
    const { data, error } = await supabaseInstance
      .from("Menu_Categories")
      .delete()
      .eq("categoryid",categoryid)
      .select("*")

    if (data) {
      res.status(200).json({
        success: true,
        message:"Category Deleted"
      });
    } else {
      throw error
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error });
  }
});

/**
 * @description This route is used to add a new menu item for a restaurant.
 * @route POST /restaurent/addMenu
 * @param {object} menuItemData - The data for the new menu item.
 * @return {object} - Returns a success message and the added menu item data if successful.
 */
router.post("/addMenu", async (req, res) => {
  const menuItemData = {...req.body};
  try {
    if (!menuItemData?.dietary_restriction_id) {
      menuItemData.dietary_restriction_id = null;
    }
    if (!menuItemData?.spice_level_id) {
      menuItemData.spice_level_id = null;
    }
    const { data, error } = await supabaseInstance
      .from("Menu_Item")
      .insert(menuItemData)
      .select("*")
      .maybeSingle()

    if (data) {
      res.status(200).json({
        success: true,
        data: data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error });
  }
});

/**
 * @description This route is used to update an existing menu item for a restaurant.
 * @route POST /restaurent/updateMenu/:itemid
 * @param {string} itemid - The ID of the menu item to be updated.
 * @param {object} menuItemData - The data to be updated for the menu item.
 * @return {object} - Returns a success message and the updated menu item data if successful.
 */
router.post("/updateMenu/:itemid", async (req, res) => {
  const { itemid } = req.params;
  const menuItemData = {...req.body};
  try {

    if (!menuItemData?.dietary_restriction_id) {
      menuItemData.dietary_restriction_id = null;
    }
    if (!menuItemData?.spice_level_id) {
      menuItemData.spice_level_id = null;
    }

    const { data, error } = await supabaseInstance
    .from("Menu_Item")
    .update({ ...menuItemData })
    .select("*")
    .eq("itemid",itemid)
     
    if (data) {
      res.status(200).json({
        success: true,
        message: "Data updated succesfully",
        data: data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error });
  }
});

/**
 * @description This route is used to get all menu items for a restaurant.
 * @route GET /restaurent/getItemList/:restaurantId
 * @param {string} restaurantId - The ID of the restaurant for which the menu items are being retrieved.
 * @return {object} - Returns a success message and the menu item data if successful.
 */
router.get("/getItemList/:restaurantId", async (req, res) => {
  const { restaurantId } = req.params;
  try {
    const { data, error } = await supabaseInstance
      .from("Menu_Item")
      .select("*")
      .eq("restaurantId", restaurantId)
     
    if (data) {
      res.status(200).json({
        success: true,
        data: data,
      });
    } else {
      throw error
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

/**
 * @description This route is used to get a menu item by its ID.
 * @route GET /restaurent/getItem/:itemid
 * @param {string} itemid - The ID of the menu item to be retrieved.
 * @return {object} - Returns a success message and the menu item data if successful.
 */
router.get("/getItem/:itemid", async (req, res) => {
  const {itemid} = req.params;
  try {
    const { data, error } = await supabaseInstance
      .from("Menu_Item")
      .select("*")
      .eq("itemid",itemid)

    if (data) {
      res.status(200).json({
        success: true,
        data: data,
      });
    } else {
      throw error
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

/**
 * @description This route is used to upsert an image for a menu item.
 * @route POST /restaurent/upsertMenuItemImage
 * @param {string} itemid - The ID of the menu item for which the image is being upserted.
 * @return {object} - Returns a success message and the updated menu item data if successful.
 */
router.post("/upsertMenuItemImage",upload.single('file'), async (req, res) => {
  const { itemid } = req.body;

  try {
    const { data, error } = await supabaseInstance
      .storage
      .from('menu-item-image')
      .upload(itemid + ".webp", req.file.buffer, {
        cacheControl: '3600',
        upsert: true,
        contentType: 'image/webp'
      })

    if (data?.path) {
      const publickUrlresponse = await supabaseInstance.storage.from('menu-item-image').getPublicUrl(data?.path);
      if (publickUrlresponse?.data?.publicUrl) {
        const publicUrl = publickUrlresponse?.data?.publicUrl;
        const menuData = await supabaseInstance.from("Menu_Item").update({ item_image_url: `${publicUrl}?${Date.now}` }).eq("itemid", itemid).select("*").maybeSingle();
        res.status(200).json({
          success: true,
          data: menuData.data,
        });
      } else {
        throw publickUrlresponse.error || "Getting Error in PublicUrl"
      }
    } else {
      throw error
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error });
  }
})

module.exports = router;
