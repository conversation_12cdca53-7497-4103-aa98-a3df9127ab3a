var express = require("express");
var router = express.Router();
var supabaseInstance = require("../../services/supabaseClient").supabase;
var moment = require('moment')
const { isTimeInRange } = require("../../services/dateTimeService");
const logger  = require("../../services/logger");

router.post("/favoriteMenuItem", async (req, res) => {
  const { customerAuthUID, outletId, itemid, restaurantId } = req.body;
  try {
    const { data, error } = await supabaseInstance
      .from("FavoriteMenuItem")
      .insert({ customerAuthUID, outletId, itemid, restaurantId })
      .select("*")

    if (data) {
      res.status(200).json({
        success: true,
        data: data
      });
    } else {
      throw error;
    }
  } catch (error) {
    logger.error("Error in favoriteMenuItem", error);
    res.status(500).json({ success: false, error: error.message });
  }
});

router.get("/getfavoriteMenuItem/:customerAuthUID", async (req, res) => {
  const { customerAuthUID } = req.params;
  const {tag} = req.query;
  try {
    let { data, error } = await supabaseInstance
      .from("FavoriteMenuItem")
      .select("favoriteMenuItemId,itemid(*,outletId(isOutletOpen,outletId,outletName,address,logo,headerImage,openTime,closeTime,isDineIn,isPickUp,isDelivery,packaging_charge,convenienceFee,Campus(tag),Timing(*,dayId(*))))")
      .eq("customerAuthUID", customerAuthUID)

    const uniqueObjects = {};
    for (const obj of data) {
      const objId = obj.itemid.itemid;
      uniqueObjects[objId] = obj;
    }
    let uniqueObjectsArray = Object.values(uniqueObjects).map(time => {
      time.itemid.outletId.Timing = time?.itemid?.outletId?.Timing
        .map(m => ({
          ...m, Timing: {
            Today: time?.itemid?.outletId?.Timing?.find(f => f?.dayId?.day === moment().tz("Asia/Kolkata").format("dddd")),
            Tomorrow: time?.itemid?.outletId?.Timing?.find(f => f?.dayId?.day === moment().tz("Asia/Kolkata").add(1, 'days').format("dddd")),
            Overmorrow: time?.itemid?.outletId?.Timing?.find(f => f?.dayId?.day === moment().tz("Asia/Kolkata").add(2, 'days').format("dddd"))
          }
        }))

      time.itemid.outletId.Timing = time?.itemid?.outletId?.Timing?.[0]?.Timing

      const openTime = time?.itemid?.outletId?.Timing?.['Today']?.openTime
      const closeTime = time?.itemid?.outletId?.Timing?.['Today']?.closeTime
      
      const TomorrowopenTime = time?.itemid?.outletId?.Timing?.['Tomorrow']?.openTime
      const TomorrowcloseTime = time?.itemid?.outletId?.Timing?.['Tomorrow']?.closeTime

      const OvermorrowopenTime = time?.itemid?.outletId?.Timing?.['Overmorrow']?.openTime
      const OvermorrowcloseTime = time?.itemid?.outletId?.Timing?.['Overmorrow']?.closeTime
      const isOutletOpen = time?.itemid?.outletId?.isOutletOpen;

      let todayflag = false;
      let tomorrowflag = false;
      let overmorrowflag = false;
      if (openTime && closeTime) {
        const time = moment().tz("Asia/Kolkata");
        const beforeTime = moment.tz(openTime, 'HH:mm:ss', 'Asia/Kolkata');
        const afterTime = moment.tz(closeTime, 'HH:mm:ss', 'Asia/Kolkata');

        todayflag = isOutletOpen;
      }
      // if (!todayflag && isTimeExtended) {
      //   todayflag = true;
      // }

      if (TomorrowopenTime && TomorrowcloseTime) {
        const time = moment().tz("Asia/Kolkata");
        const beforeTime = moment.tz(TomorrowopenTime, 'HH:mm:ss', 'Asia/Kolkata');
        const afterTime = moment.tz(TomorrowcloseTime, 'HH:mm:ss', 'Asia/Kolkata');

        tomorrowflag = isTimeInRange(time,beforeTime, afterTime);
      }
      // if (!tomorrowflag && isTimeExtended) {
      //   tomorrowflag = true;
      // }

      if (OvermorrowopenTime && OvermorrowcloseTime) {
        const time = moment().tz("Asia/Kolkata");
        const beforeTime = moment.tz(OvermorrowopenTime, 'HH:mm:ss', 'Asia/Kolkata');
        const afterTime = moment.tz(OvermorrowcloseTime, 'HH:mm:ss', 'Asia/Kolkata');

        overmorrowflag = isTimeInRange(time,beforeTime, afterTime);
      }
      // if (!overmorrowflag && isTimeExtended) {
      //   overmorrowflag = true;
      // }
      time.todayisOutletOpen = todayflag
      time.tomorrowisOutletOpen = tomorrowflag
      time.overmorrowisOutletOpen = overmorrowflag
      return time
    })

    // if tag is present from the query then filter the data based on the tag
    if(tag){
      uniqueObjectsArray = uniqueObjectsArray.filter(f => f?.itemid?.outletId?.Campus?.tag === tag)
    }

    if (data) {
      res.status(200).json({
        success: true,
        data: uniqueObjectsArray,
      });
    } else {
      throw error
    }
  } catch (error) {
    logger.error("Error in getfavoriteMenuItem", error);
    res.status(500).json({ success: false, error: error.message });
  }
});

router.delete("/deletefavoriteMenuItem/:favoriteMenuItemId", async (req, res) => {
  const { favoriteMenuItemId } = req.params;
  try {
    const { data, error } = await supabaseInstance
      .from("FavoriteMenuItem")
      .delete()
      .eq("favoriteMenuItemId", favoriteMenuItemId)
      .select("*")

    if (data) {
      res.status(200).json({
        success: true,
        message: "Menu item Deleted"
      });
    } else {
      throw error
    }
  } catch (error) {
    logger.error("Error in deletefavoriteMenuItem", error);
    res.status(500).json({ success: false, error: error });
  }
});

module.exports = router;