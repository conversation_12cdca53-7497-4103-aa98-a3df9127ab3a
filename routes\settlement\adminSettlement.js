const e = require("express");
var express = require("express");
var router = express.Router();
var supabaseInstance = require("../../services/supabaseClient").supabase;
const logger  = require("../../services/logger");

const devOutlets = [
  "006fe90c-1234-40da-a4f3-f386164236e7", //Mealpe
  "db5b9f32-0b26-4a5c-b0cf-97c6ac844bae", //Mealpe Mess
  "9cc19918-1d47-46d9-8ed1-934615abbc68", //Mealpe Mess 2
  "0821e131-ff9c-4ef1-a6b1-765137ec2fcc", //Hospital Demo
  "7ab4740c-cbd7-47e2-a4f6-0539a03c52f9", //Demo 1
  "d407f358-f1b0-4be8-9500-e82597343aa7", //Demo 2
  "df0ff353-6be6-4361-9603-21c229f63058", //Demo 3
];

router.post("/getAllOutletPayment", async (req, res) => {
  const { startDate, endDate, searchText } = req.body;
  try {
    let query = supabaseInstance.rpc("get_all_outlet_order_payment", {
      start_date: startDate,
      end_date: endDate,
    });

    if (searchText) {
      query = query.ilike("outletname", `%${searchText}%`);
    }

    const { data, error } = await query;

    if (data) {
      res.status(200).json({
        success: true,
        data: data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    logger.error("Error in getAllOutletPayment", error);
    res.status(500).json({ success: false, error: error });
  }
});

router.post("/getOutletDashboard", async (req, res) => {
  const { outletId, startDate, endDate } = req.body;
  try {
    const { data, error } = await supabaseInstance.rpc(
      "get_single_outlet_order_dashboard",
      { outlet_id: outletId, start_date: startDate, end_date: endDate }
    );

    if (data) {
      res.status(200).json({
        success: true,
        data: data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    logger.error("Error in getOutletDashboard", error);
    res.status(500).json({ success: false, error: error });
  }
});

router.post("/getOutletOrderDetails", async (req, res) => {
  const { outletId, startDate, endDate } = req.body;
  try {
    const { data, error } = await supabaseInstance.rpc(
      "get_single_outlet_order_details",
      { outlet_id: outletId, start_date: startDate, end_date: endDate }
    );

    if (data) {
      res.status(200).json({
        success: true,
        data: data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    logger.error("Error in getOutletOrderDetails", error);
    res.status(500).json({ success: false, error: error });
  }
});

router.post("/getOutletOrderDateWise", async (req, res) => {
  const { outletId, targateDate } = req.body;
  try {
    const { data, error } = await supabaseInstance.rpc(
      "get_admin_outlet_order",
      { outlet_id: outletId, target_date: targateDate }
    );

    if (data) {
      res.status(200).json({
        success: true,
        data: data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    logger.error("Error in getOutletOrderDateWise", error);
    res.status(500).json({ success: false, error: error });
  }
});

router.post("/getAdminFinance", async (req, res) => {
  let { start_date, end_date, cities, campuses, outlets, isOnline, isCash } =
    req.body;
  
  // remove the outlets which are in devOutlets array
  outlets = outlets?.filter((outlet) => !devOutlets.includes(outlet));

  try {
    let { data, error } = await supabaseInstance.rpc(
      "get_all_outlet_finance_test",
      {
        start_date,
        end_date,
        cities: cities || null,
        campuses: campuses || null,
        outlets: outlets || null,
        is_online: isOnline,
        is_cash: isCash,
      }
    );

    if (data) {
      const { data: outletData, error: outletError } = await supabaseInstance
        .from("Outlet")
        .select("isGSTCollectedByMealpe, outletId, outletName, GSTIN")
        .in("outletId", outlets);
      if (outletError) {
        throw outletError;
      }

      // add isGSTCollectedByMealpe field to each data object
      // add outletid field which replicates the value of outletId for each data object
      data.forEach((item) => {
        const outlet = outletData.find(
          (outlet) => outlet.outletId === item.outletId
        );
        if (outlet) {
          item.isGSTCollectedByMealpe = outlet.isGSTCollectedByMealpe;
          item.GSTIN = outlet.GSTIN;
        }

        item.outletid = item.outletId;
        item.outletname = item.outletName;
        item.campusname;
      });
      res.status(200).json({
        success: true,
        data: data.filter((item) => item.outletid !== null),
      });
    } else {
      throw error;
    }
  } catch (error) {
    logger.error("Error in getAdminFinance", error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// router.post("/getAdminFinance", async (req, res) => {
//     const { start_date, end_date, cities, campuses, outlets } = req.body;
//     try {
//         const { data, error } = await supabaseInstance.rpc('get_all_outlet_finance', { start_date, end_date, cities: cities || null, campuses: campuses || null, outlets: outlets || null });

//         if (data) {
//             res.status(200).json({
//                 success: true,
//                 data: data.filter(item => item.outletid !== null)
//             });
//         } else {
//             throw error
//         }
//     } catch (error) {
//         logger.error("Error in getAdminFinance", error);
//         res.status(500).json({ success: false, error: error });
//     }
// });

// router.post("/getAdminFinanceDashboard", async (req, res) => {
//   const { start_date, end_date, outlets, isOnline, isCash } = req.body;
//   try {
//     const { data, error } = await supabaseInstance.rpc(
//       "get_super_admin_finance_dashboard_test",
//       {
//         start_date,
//         end_date,
//         outlets: outlets || null,
//         is_online: isOnline,
//         is_cash: isCash,
//       }
//     );

//     if (error) {
//       throw error;
//     }

//     let { data: newData, error: newError } = await supabaseInstance.rpc(
//       "get_all_outlet_finance_test",
//       {
//         start_date,
//         end_date,
//         // cities: cities || null,
//         // campuses: campuses || null,
//         cities: null,
//         campuses: null,
//         outlets: outlets || null,
//         is_online: isOnline,
//         is_cash: isCash,
//       }
//     );

//     if (newError) {
//       throw newError;
//     }

//     /* 
//     add the following fields to the data objects
//     if isOnline is true, then add the following fields
//     1 : ecommGST = SUM(food_gst + 5% of (packagingCharge + deliveryCharge)) (food_gst is getting from newData and packagingCharge and deliveryCharge are getting from Transaction table)
//     */

//     // get the transaction data from the db
//     let { data: transactionData, error: transactionError } =
//       await supabaseInstance
//         .from("Transaction")
//         .select("*")
//         .in("outletId", outlets)
//         .gte("created_at", start_date)
//         .lte("created_at", end_date);

//     if (transactionError) {
//       throw transactionError;
//     }

//     // extract array of txnId from transactionData
//     let transactionIdArray = transactionData.map((item) => item.txnid);

//     // get the order status from isCashOrder column of Order table and add it to the transactionData
//     let { data: orderData, error: orderError } = await supabaseInstance
//       .from("Order")
//       .select("isCashOrder, txnid")
//       .in("txnid", transactionIdArray);

//     if (orderError) {
//       throw orderError;
//     }

//     // merge the orderData with transactionData
//     transactionData = transactionData.map((item) => {
//       let orderData = orderData.find((data) => data.txnid === item.txnid);
//       return {
//         ...item,
//         isCashOrder: orderData.isCashOrder,
//       };
//     });

//     if (isOnline) {
//       // get the food gst data from the newData
//       let foodGstData = newData.map((item) => {
//         return {
//           outletId: item.outletId,
//           food_gst: item.food_gst,
//         };
//       });

//       // get the packaging charge and delivery charge from the transaction data
//       let transactionDataWithPackagingAndDeliveryCharge = transactionData.map(
//         (item) => {
//           return {
//             outletId: item.outletId,
//             packagingCharge: item.packagingCharge,
//             deliveryCharge: item.deliveryCharge,
//           };
//         }
//       );

//       // merge the foodGstData and transactionDataWithPackagingAndDeliveryCharge
//       let mergedData = foodGstData.map((item) => {
//         let transactionData =
//           transactionDataWithPackagingAndDeliveryCharge.find(
//             (data) => data.outletId === item.outletId
//           );
//         return {
//           ...item,
//           ...transactionData,
//         };
//       });

//       // calculate the ecommGST
//       let ecommGST = mergedData.map((item) => {
//         return {
//           outletId: item.outletId,
//           ecommGST:
//             item.food_gst + 0.05 * (item.packagingCharge + item.deliveryCharge),
//         };
//       });

//       // merge the ecommGST with the newData
//       let finalData = newData.map((item) => {
//         let ecommGSTData = ecommGST.find(
//           (data) => data.outletId === item.outletId
//         );
//         return {
//           ...item,
//           ...ecommGSTData,
//         };
//       });

//       // add sum of ecommGST to data
//       let ecommGSTSum = ecommGST.reduce((acc, item) => {
//         return acc + item.ecommGST;
//       }, 0);

//       data.forEach((item) => {
//         item.ecommGST = ecommGSTSum;
//       });
//     }
//     if (data) {
//       res.status(200).json({
//         success: true,
//         data,
//         data2: finalData,
//       });
//     } else {
//       throw error;
//     }
//   } catch (error) {
//     logger.error("Error in getAdminFinanceDashboard", error);
//     res.status(500).json({ success: false, error: error });
//   }
// });

router.post("/getAdminFinanceDashboard", async (req, res) => {
  let { start_date, end_date, outlets } = req.body;
  const {isOnline, isCash} = req.body;

  // remove the outlets which are in devOutlets array
  outlets = outlets?.filter((outlet) => !devOutlets.includes(outlet));

    try {
        const { data, error } = await supabaseInstance.rpc('get_super_admin_finance_dashboard_test_2', { start_date, end_date, outlets: outlets || null, is_online: isOnline || null, is_cash: isCash|| null });

        if (data) {
            res.status(200).json({
                success: true,
                data: data
            });
        } else {
            throw error
        }
    } catch (error) {
        logger.error("Error in getAdminFinanceDashboard", error);
        res.status(500).json({ success: false, error: error });
    }
});

router.post("/getAdminFinanceOrderReport", async (req, res) => {
  const { start_date, end_date, outletId } = req.body;
  try {
    const { data, error } = await supabaseInstance.rpc(
      "get_all_outlet_order_report_level",
      { start_date, end_date, outlet_id: outletId }
    );

    if (data) {
      res.status(200).json({
        success: true,
        data: data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    logger.error("Error in getAdminFinanceOrderReport", error);
    res.status(500).json({ success: false, error: error });
  }
});

router.post("/orderLevelExcelSheet", async (req, res) => {
  const { start_date, end_date, outletId } = req.body;
  try {
    const { data, error } = await supabaseInstance.rpc(
      "order_level_excel_sheet_query",
      { end_date, outlet_id: outletId, start_date }
    );

    if (data) {
      let _data = [
        // ["", "", "", "", "", "", "", "Delete", "(1)", "(2)", "(3)", "(CF)", "(4)", "(5)", "(A)", "(6)", "(7)", "(B)", "(9)", "(10)", "(11)", "(12)", "(13)", "(14)", "(15)", "(16)", "(17)", "(18)", "(19)", "(20)", "(C)", "(D)", "(E)"],
        [
          "",
          "",
          "",
          "",
          "",
          "",
          "",
          "(1)",
          "(2)",
          "(3)",
          "(CF)",
          "(4)",
          "(4) (i)",
          "(5)",
          "(A)",
          "(6)",
          "(7)",
          "(B)",
          "(9)",
          "(10)",
          "(11)",
          "(12)",
          "(13)",
          "(15)",
          "(16)",
          "(17)",
          "(18)",
          "(C)",
          "(D)",
          "(E)",
        ],
        [
          "S.No.",
          "Order ID",
          "Order Date (Timestamp)",
          "Food Outlet Name",
          "Food Outlet ID",
          "Discount Construct [X% off upto INR X (Date Range Applicable)]",
          "Order type (Take Away/Dine In/Delivery)",
          "Subtotal (items base price)",
          "Packaging charge",
          "Delivery charge",
          "Convenience Fee % (Customer)",
          "Convenience Fee Value = CF% of (1)+(2)+(3)",
          "GST on Convenience Fee Value = 0.18 * (4)",
          "Food Outlet discount [Promo]",
          "Net bill value [(1) + (2) + (3) + (4) + (4) (i) - (5)]",
          "Total Food GST collected from customers = 5% of (1) + (2) + (3)",
          "Food GST retained by MealPe - Amount of tax paid by MealPe under section 9(5)",
          "Gross sales [(A) + (6)]",
          "Total Merchant Amount [(B) - (7)-(4)-(4)(i)]",
          "Commissionable amount [(B) - (6) - (4) - (4)(i)]",
          "Commission % (Vendor)",
          "Commission value = Comm% of (10)",
          "Payment mechanism fee",
          "Applicable amount for 9(5) = (1) Food Base Price",
          "Taxes on MealPe Commission [ (12)+(13) ] * 18%",
          "TDS 194O amount. = 1% of [(9)]",
          "Credit note/(Debit Note) adjustment",
          "Net Deductions [(4)+(4)(i)+(12)+(13)+(16)+(17)+(18)]",
          "Net Additions",
          "Order level Payout(B) - (C) + (D) - (7)",
        ],
      ];

      let sumRow = [
        "Total",
        "",
        "",
        "",
        "",
        "",
        "",
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0,
      ];

      data.forEach((element, index) => {
        let _arr = [];

        // Calculate GST on Convenience Fee - new field (4)(i)
        const convFeeGST = +Number(0.18 * Number(element["Convenience Fee Value"])).toFixed(2);

        const A =
          Number(element["Subtotal"]) +
          Number(element["Packaging charge"]) +
          Number(element["Delivery charge"]) +
          Number(element["Convenience Fee Value"]) +
          convFeeGST -
          Number(element["Food Outlet discount"] || element["Restaurant discount"]);

        // Calculate Total Food GST
        const _6 = Number(0.05 * (
          Number(element["Subtotal"]) +
          Number(element["Packaging charge"]) +
          Number(element["Delivery charge"])
        )).toFixed(2);

        // Implement the conditional logic for Food GST retained by MealPe
        let _7 = 0;
        if (Number(element["Commission %"]) === 0 && Number(element["Convenience Fee %"]) === 0) {
          _7 = 0;
        } else {
          _7 = Number(_6);
        }

        const B = Number(A) + Number(_6);

        // Total Merchant Amount
        const _9 = B - Number(_7) - Number(element["Convenience Fee Value"]) - convFeeGST;

        // Commissionable amount
        const _10 = B - Number(_6) - Number(element["Convenience Fee Value"]) - convFeeGST;

        // Commission value
        const commissionValue = Number(element["Commission %"]) * Number(_10) / 100;
        console.log(`Commission Value: ${commissionValue}`);

        // Taxes on MealPe Commission
        const _16 = +Number(
          0.18 * (
            commissionValue +
            Number(element["Payment mechanism fee"] || 0)
          )
        ).toFixed(2);

        const _17 = +Number(0.01 * _9).toFixed(2);

        // Net Deductions
        const C =
          Number(element["Convenience Fee Value"]) +
          convFeeGST +
          commissionValue +
          Number(element["Payment mechanism fee"] || 0) +
          _16 +
          _17 +
          Number(element["Credit note/(Debit Note) adjustment"] || 0);

        const E = B - C + (Number(element["Net Additions"] || 0) - _7);

        _arr.push(index + 1);
        _arr.push(element["Order ID"]);
        _arr.push(element["Order Date"]);
        _arr.push(element["Food Outlet Name"] || element["Restaurant Name"]);
        _arr.push(element["Food Outlet ID"] || element["Restaurant ID"]);
        _arr.push(element["Discount Construct"]);
        _arr.push(element["Order type"]);
        _arr.push(element["Subtotal"]);
        _arr.push(element["Packaging charge"]);
        _arr.push(element["Delivery charge"]);
        _arr.push(element["Convenience Fee %"]);
        _arr.push(element["Convenience Fee Value"]);
        _arr.push(convFeeGST);
        _arr.push(element["Food Outlet discount"] || element["Restaurant discount"]);
        _arr.push(A);
        _arr.push(_6);
        _arr.push(_7);
        _arr.push(B);
        _arr.push(_9);
        _arr.push(_10);
        _arr.push(element["Commission %"]);
        _arr.push(commissionValue);
        _arr.push(element["Payment mechanism fee"] || 0);
        _arr.push(element["Subtotal"]);
        _arr.push(_16);
        _arr.push(_17);
        _arr.push(element["Credit note/(Debit Note) adjustment"] || "");
        _arr.push(C);
        _arr.push(element["Net Additions"] || "");
        _arr.push(E);

        _data.push(_arr);

        // Update sum row
        sumRow[7] += Number(element["Subtotal"]);
        sumRow[8] += Number(element["Packaging charge"]);
        sumRow[9] += Number(element["Delivery charge"]);
        sumRow[11] += Number(element["Convenience Fee Value"]);
        sumRow[12] += Number(convFeeGST);
        sumRow[13] += Number(element["Food Outlet discount"] || element["Restaurant discount"]);
        sumRow[14] += A;
        sumRow[15] += Number(_6);
        sumRow[16] += _7;
        sumRow[17] += B;
        sumRow[18] += _9;
        sumRow[19] += _10 ;
        sumRow[21] += commissionValue;
        sumRow[22] += Number(element["Payment mechanism fee"] || 0);
        sumRow[23] += Number(element["Subtotal"]);
        sumRow[24] += _16;
        sumRow[25] += _17;
        sumRow[27] += C;
        sumRow[29] += E;
      });

      sumRow[10] = ""; // Convenience Fee %
      sumRow[20] = ""; // Commission %
      sumRow[26] = ""; // Credit note/(Debit Note) adjustment
      sumRow[28] = ""; // Net Additions



      // First push an empty row
      _data.push([""]);
      // Push the sum row
      _data.push(sumRow);

      res.status(200).json({
        success: true,
        data: _data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    logger.error("Error in orderLevelExcelSheet", error);
    res.status(500).json({ success: false, error: error });
  }
});

module.exports = router;
