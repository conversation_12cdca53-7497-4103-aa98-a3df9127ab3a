const { createValidator } = require('../middleware/validationErrorMiddleware');
const { query } = require('express-validator');


const validationRules = {
    //=====================  getCartItemsStock ============================
    validateGetCartItemsStock: [
        query('itemId')
            .trim()
            .notEmpty()
            .withMessage('itemId parameter is required').bail()
            .customSanitizer((value, { req }) => {
                const toDigitString = (v) => String(v).trim();
                let normalized = [];

                if (Array.isArray(value)) {
                    normalized = value.map(toDigitString);
                } else if (typeof value === 'string') {
                    try {
                        const parsed = JSON.parse(value);
                        if (Array.isArray(parsed)) {
                            normalized = parsed.map(toDigitString);
                        } else {
                            normalized = [toDigitString(value)];
                        }
                    } catch (_) {
                        if (value.includes(',')) {
                            normalized = value.split(',').map(s => s.trim());
                        } else {
                            normalized = [toDigitString(value)];
                        }
                    }
                } else if (typeof value === 'number') {
                    normalized = [String(value)];
                } else {
                    normalized = [];
                }

                // Save normalized array for controller convenience
                req.query.itemIds = normalized;
                return value;
            })
            .custom((value) => {
                // Helper function to validate if a value contains only digits
                const isValidDigitId = (id) => {
                    // Convert to string and check if it contains only digits
                    const idStr = String(id).trim();
                    return /^\d+$/.test(idStr) && idStr.length > 0;
                };

                // If it's already an array (multiple query params with same name)
                if (Array.isArray(value)) {
                    if (value.length === 0) {
                        throw new Error('itemId array cannot be empty');
                    }
                    // Validate that all items are valid digit-only IDs
                    const invalidItems = value.filter(item => !isValidDigitId(item));
                    if (invalidItems.length > 0) {
                        throw new Error(`Invalid item IDs found: ${invalidItems.join(', ')}. All item IDs must contain only digits`);
                    }
                    return true;
                }

                // If it's a string, validate single value or comma-separated
                if (typeof value === 'string') {
                    try {
                        // Try to parse as JSON array
                        const parsed = JSON.parse(value);
                        if (Array.isArray(parsed)) {
                            if (parsed.length === 0) {
                                throw new Error('itemId array cannot be empty');
                            }
                            // Validate that all items are valid digit-only IDs
                            const invalidItems = parsed.filter(item => !isValidDigitId(item));
                            if (invalidItems.length > 0) {
                                throw new Error(`Invalid item IDs found: ${invalidItems.join(', ')}. All item IDs must contain only digits`);
                            }
                            return true;
                        }
                    } catch (jsonError) {
                        // If JSON parse fails, check if it's comma-separated
                        if (value.includes(',')) {
                            const items = value.split(',').map(id => id.trim());
                            if (items.length === 0) {
                                throw new Error('itemId cannot be empty');
                            }
                            // Validate each comma-separated item
                            const invalidItems = items.filter(item => !isValidDigitId(item));
                            if (invalidItems.length > 0) {
                                throw new Error(`Invalid item IDs found: ${invalidItems.join(', ')}. All item IDs must contain only digits`);
                            }
                            return true;
                        }
                    }

                    // Single string value - validate it contains only digits
                    if (!isValidDigitId(value)) {
                        throw new Error(`Invalid item ID: ${value}. Item ID must contain only digits`);
                    }
                    return true;
                }

                // If it's a number, validate it's a positive integer
                if (typeof value === 'number') {
                    if (!Number.isInteger(value) || value <= 0) {
                        throw new Error(`Invalid item ID: ${value}. Item ID must be a positive integer`);
                    }
                    return true;
                }

                throw new Error('itemId must be a valid digit-only ID, array of digit-only IDs, or comma-separated string of digit-only IDs');
            })
    ],
};

// Build validators dynamically to avoid repetition
const validators = Object.fromEntries(
    Object.entries(validationRules).map(([key, rules]) => [key, createValidator(rules)])
);


module.exports = validators;

