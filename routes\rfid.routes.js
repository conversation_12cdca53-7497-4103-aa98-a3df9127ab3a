const express = require('express');
const router = express.Router();
const { supabase } = require('../services/supabaseClient');
const logger = require('../services/logger');
const { validateUpsertRfidData, validateDeleteRfidData } = require('../validators/rfidValidator');

// Health check endpoint for RFID routes
router.get('/', (req, res) => {
  res.send({ success: true, message: 'Response sent from rfid.js' });
});

/**
 * @route POST /rfid/upsertRfidData
 * @description Upsert RFID data of a user in the Customer table
 * @param {string} rfid - RFID tag of the user
 * @param {string} customerAuthUID - Unique identifier for the customer
 * @return {object} - Returns success message or error
 */
router.post(
  '/upsertRfidData',
  validateUpsertRfidData,
  async (req, res) => {
    const { rfid, customerAuthUID } = req.body;
    try {
      // Check if RFID is already assigned to another customer
      const { data: existingCustomer, error: selectError } = await supabase
        .from('Customer')
        .select('customerAuthUID')
        .eq('rfid', rfid)
        .single();

      if (selectError && selectError.code !== 'PGRST116') { // PGRST116: No rows found
        logger.error('Error checking existing RFID:', JSON.stringify(selectError));
        return res.status(500).json({ success: false, message: 'Failed to check existing RFID.', error: selectError });
      }

      if (existingCustomer && existingCustomer.customerAuthUID !== customerAuthUID) {
        // RFID is already associated with another customer
        return res.status(409).json({ success: false, message: 'RFID is already associated with another customer.' });
      }

      // Proceed to upsert RFID for the customer
      const { error: upsertError } = await supabase
        .from('Customer')
        .upsert({ rfid, customerAuthUID }, { onConflict: ['customerAuthUID'] });

      if (upsertError) {
        logger.error('Error in upsertRfidData:', JSON.stringify(upsertError));
        return res.status(500).json({ success: false, message: 'Failed to upsert RFID data.', error: upsertError });
      }

      return res.status(200).json({ success: true, message: 'RFID data upserted successfully.' });
    } catch (error) {
      logger.error('Exception in upsertRfidData:', error);
      return res.status(500).json({ success: false, message: 'Internal server error.', error });
    }
  }
);

router.delete('/deleteRfidData', validateDeleteRfidData, async (req, res) => { 
    const { customerAuthUID } = req.body;
    try {
      const { error } = await supabase
        .from('Customer')
        .update({ rfid: null }) // Set RFID to null
        .eq('customerAuthUID', customerAuthUID);

      if (error) {
        logger.error('Error in deleteRfidData:', JSON.stringify(error));
        return res.status(500).json({ success: false, message: 'Failed to delete RFID data.', error });
      }

      return res.status(200).json({ success: true, message: 'RFID data deleted successfully.' });
    } catch (error) {
      logger.error('Exception in deleteRfidData:', error);
      return res.status(500).json({ success: false, message: 'Internal server error.', error });
    }
});


module.exports = router;