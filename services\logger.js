// Import dependencies
const winston = require('winston');
require('winston-daily-rotate-file');
const path = require('path');
require('dotenv').config();

// Directory for logs
const logDir = path.join(__dirname, '../logs');

// Sensitive fields to mask
const SENSITIVE_FIELDS = ['password', 'token', 'authorization', 'auth', 'accessToken', 'refreshToken'];

// Helper to mask sensitive data in objects
function maskSensitive(obj) {
  if (!obj || typeof obj !== 'object') return obj;
  const clone = Array.isArray(obj) ? [...obj] : { ...obj };
  for (const key in clone) {
    if (SENSITIVE_FIELDS.includes(key.toLowerCase())) {
      clone[key] = '***MASKED***';
    } else if (typeof clone[key] === 'object') {
      clone[key] = maskSensitive(clone[key]);
    }
  }
  return clone;
}

// JSON formatter with masking
const jsonFormatter = winston.format.combine(
  winston.format.timestamp(),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    let logObj = { timestamp, level, message };
    if (meta) {
      logObj = { ...logObj, ...maskSensitive(meta) };
    }
    return JSON.stringify(logObj);
  })
);

// Create transports
const infoTransport = new winston.transports.DailyRotateFile({
  filename: path.join(logDir, 'info.log'),
  datePattern: 'YYYY-MM-DD',
  level: 'info',
  maxFiles: '7d',
  zippedArchive: true,
  format: jsonFormatter,
});

const errorTransport = new winston.transports.DailyRotateFile({
  filename: path.join(logDir, 'error.log'),
  datePattern: 'YYYY-MM-DD',
  level: 'error',
  maxFiles: '7d',
  zippedArchive: true,
  format: jsonFormatter,
});

const httpTransport = new winston.transports.DailyRotateFile({
  filename: path.join(logDir, 'http.log'),
  datePattern: 'YYYY-MM-DD',
  level: 'http',
  maxFiles: '7d',
  zippedArchive: true,
  format: jsonFormatter,
});

// Console transport for development
const consoleTransport = new winston.transports.Console({
  format: winston.format.combine(
    winston.format.colorize(),
    winston.format.simple()
  ),
});

// Logger instance
const logger = winston.createLogger({
  level: process.env.ENV === 'production' ? 'info' : 'debug',
  transports: [infoTransport, errorTransport, httpTransport],
  exceptionHandlers: [errorTransport],
  exitOnError: false,
});

if (process.env.ENV !== 'production') {
  logger.add(consoleTransport);
}

// HTTP logging middleware
logger.httpLogger = (req, res, next) => {
  const start = Date.now();
  res.on('finish', () => {
    const duration = Date.now() - start;
    logger.http('HTTP Request', {
      method: req.method,
      url: req.originalUrl,
      status: res.statusCode,
      duration,
      ip: req.ip,
      body: req.body,
      query: req.query,
    });
  });
  next();
};


// HTTP stream for middleware
logger.stream = {
  write: (message) => {
    logger.http(message.trim());
  },
};


module.exports = logger; 