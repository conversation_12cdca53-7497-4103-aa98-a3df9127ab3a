var express = require('express');
var router = express.Router();

/* GET home page. */
router.get('/', function(req, res, next) {
  try {
    res.render('index', { title: 'Express' });
  } catch (error) {
    return res.status(500).json({error: error.message});
  }
});

// health check
router.get('/health', function(req, res, next) {
  try {
    res.status(200).json({message: "Server is up and running"});
  } catch (error) {
    return res.status(500).json({error: error.message});
  }
});


// get current time in IST
router.get('/getTime', function(req, res) {
  try {
    const now = new Date();
    const istTime = new Date(now.toLocaleString("en-US", {timeZone: "Asia/Kolkata"}));
    
    // Check if DST is active (India doesn't observe DST, so always false)
    const isDstActive = false;
    
    // Get day of week
    const dayOfWeek = now.toLocaleDateString("en-US", {timeZone: "Asia/Kolkata", weekday: "long"});
    
    // Format date as MM/DD/YYYY
    const month = String(istTime.getMonth() + 1).padStart(2, '0');
    const day = String(istTime.getDate()).padStart(2, '0');
    const year = istTime.getFullYear();
    const formattedDate = `${month}/${day}/${year}`;
    
    // Format time as HH:MM (24-hour format)
    const hours = String(istTime.getHours()).padStart(2, '0');
    const minutes = String(istTime.getMinutes()).padStart(2, '0');
    const formattedTime = `${hours}:${minutes}`;
    
    // Format dateTime as ISO-like string
    const seconds = String(istTime.getSeconds()).padStart(2, '0');
    const milliseconds = istTime.getMilliseconds();
    const formattedDateTime = `${year}-${month}-${day}T${hours}:${minutes}:${seconds}.${milliseconds}964`;
    
    const response = {
      year: istTime.getFullYear(),
      month: istTime.getMonth() + 1,
      day: istTime.getDate(),
      hour: istTime.getHours(),
      minute: istTime.getMinutes(),
      seconds: istTime.getSeconds(),
      milliSeconds: istTime.getMilliseconds(),
      dateTime: formattedDateTime,
      date: formattedDate,
      time: formattedTime,
      timeZone: "Asia/Kolkata",
      dayOfWeek: dayOfWeek,
      dstActive: isDstActive
    };
    
    res.status(200).json(response);
  } catch (error) {
    return res.status(500).json({error: error.message});
  }
});

module.exports = router;


//!      Comment
//*      Comment
//todo   Comment
//?      Comment
//       Comment