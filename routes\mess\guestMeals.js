let express = require("express");
let router = express.Router();
let supabaseInstance = require("../../services/supabaseClient").supabase;
const logger = require("../../services/logger");
const { getMenuId } = require("./dashboard");

/**
 * Guest_Meals

guestMealId	
uuid
string

outletId	
uuid
string

mealTypeId	
bigint
number

menuId	
bigint
number

date	
timestamp with time zone
string	

quantity	
numeric
number

guestName	
text
string
 */



/**
 * @description Route to add a guest meal
 * @route POST /mess/guestMeals
 */
router.post("/", async (req, res) => { 
    const { outletId, mealTypeIds, guestName, quantity } = req.body;
    
    // Validate if all required fields are present
    if(!outletId || !mealTypeIds || !quantity) {
        return res.status(400).json({ success: false, message: "Missing required fields" });
    }

    try {
        // Loop through each mealTypeId to insert guest meals
        for (const mealTypeId of mealTypeIds) {
            const menuId = await getMenuId(mealTypeId, outletId);
            if (!menuId) {
                return res.status(404).json({ error: "Menu not found for the given meal type and outlet" });
            }

            const { error } = await supabaseInstance
                .from("Guest_Meals")
                .insert({
                    outletId,
                    mealTypeId,
                    menuId,
                    date: req.body.date || new Date().toISOString(),
                    quantity,
                    guestName : guestName || "Guest" // Default to "Guest" if no name is provided
                })
                .single();
            
            if (error) {
                logger.error("Error inserting guest meal:", error);
                return res.status(500).json({ success: false, message: "Failed to add guest meal" });
            }
        }

        res.status(200).json({ success: true, message: "Guest meals added successfully" });

    } catch (error) {
        logger.error("Error in /guestMeals route:", error);
        res.status(500).json({ error });
    }
});

module.exports = router;