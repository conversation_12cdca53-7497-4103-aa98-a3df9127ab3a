const { validationResult } = require('express-validator');

/**
 * Middleware to handle validation errors from express-validator
 * Responds with 400 and a structured error message if validation fails
 */
const handleValidationErrorsMiddleware = (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        // Aggregate error messages for clarity
        const aggregatedErrorMsgs = errors.array().map(e => e.msg).join('; ');
        return res.status(400).json({
            success: false,
            message: 'Validation failed',
            errors: errors.array(),
            error: aggregatedErrorMsgs
        });
    }
    next();
};


// Helper to build validators with appended error-handling middleware
const createValidator = (rules) => [
    ...rules,
    handleValidationErrorsMiddleware
];



// Export the middleware
module.exports = {
    handleValidationErrorsMiddleware,
    createValidator
};