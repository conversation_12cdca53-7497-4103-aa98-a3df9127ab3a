const invite = (deepLink) => `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MealPe Mess Manager Invitation</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: Inter, Arial;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #FFF3E0;
        }
        .container {
            background-color: #ffffff;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #E65100;
            border-bottom: 2px solid #FF9800;
            padding-bottom: 10px;
            display: flex;
            align-items: center;
            font-weight: 700;
        }
        h1 img {
            height: 1.2em;
            margin-right: 10px;
        }
        h3 {
            color: #F57C00;
        }
        ul {
            background-color: #FFE0B2;
            padding: 20px 20px 20px 40px;
            border-radius: 8px;
        }
        li {
            margin-bottom: 10px;
        }
        .cta-button {
            display: inline-block;
            background-color: #FF9800;
            color: #ffffff;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 5px;
            font-weight: bold;
            margin-top: 20px;
        }
        .cta-button:hover {
            background-color: #F57C00;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 0.9em;
            color: #E65100;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="font-weight: 900;">
            <img src="https://zyxwtaeuvipslarwyrbe.supabase.co/storage/v1/object/public/desktop/mealpelogo.png" alt="MealPe Logo"> Mess Manager
        </h1>
        
        <p>Hey there,</p>
        
        <p>Get ready to transform your mealtime with MealPe Mess Manager! Whether it's breakfast, lunch, or dinner, you can now view mess menus, RSVP for your meals in advance, and enjoy a seamless dining experience—all at your fingertips!</p>
        
        <h3>What's in it for you?</h3>
        <ul>
            <li><strong>View Mess Menus:</strong> Know what's cooking every day!</li>
            <li><strong>RSVP in Advance:</strong> Skip the wait and secure your meal ahead of time.</li>
            <li><strong>Quick Plate Pickup:</strong> Scan your QR code, grab your plate, and enjoy!</li>
        </ul>
        
        <p>Join now and start enjoying hassle-free meals:</p>
        <a href="${deepLink}" class="cta-button">Get Started with MealPe</a>
        
        <p>We look forward to making your meals smooth and convenient! Bon Appétit!</p>
        
        <p>Best regards,<br>
        The MealPe Team</p>
        <br>
    </div>
    
    <div class="footer">
        <p>© 2024 MealPe Online Food Ordering POS Private Limited All rights reserved.</p>
    </div>
</body>
</html>`;

module.exports = invite;