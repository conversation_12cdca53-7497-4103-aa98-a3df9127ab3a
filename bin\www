#!/usr/bin/env node

/**
 * Module dependencies.
 */

const socketIo = require('socket.io');
const app = require("../app");
const debug = require("debug")("mealpe-backend:server");
const https = require("https");
const { readFileSync } = require("fs");
const http = require("http");
require("dotenv").config();
const path = require("path");

/**
 * Get port from environment and store in Express.
 */

var port = normalizePort(process.env.PORT || "8055");
app.set("port", port);

/**
 * Create HTTPs server.
 */
console.log("ENV: ", process.env.ENV);
let server;
if (process.env.ENV === "dev") {
  server = http.createServer(app);
} else {
  const certDir = path.join(__dirname, "..", "certs");
  let httpsOptions = {
    cert: readFileSync(path.join(certDir, "fullchain.pem")), // fs.readFileSync('./ssl/example.crt');
    key: readFileSync(path.join(certDir, "privkey.pem")), // fs.readFileSync('./ssl/example.key');
  };

// let httpsOptions = {
//   cert: readFileSync('/home/<USER>/apps/backend/certs/fullchain.pem'), // fs.readFileSync('./ssl/example.crt');
//   key: readFileSync('/home/<USER>/apps/backend/certs/privkey.pem') // fs.readFileSync('./ssl/example.key');
// };


// var server = http.createServer(app);
server = https.createServer(httpsOptions, app);

}

// Initialize Socket.IO
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

// Make io available to the app
app.set('io', io);

// Initialize socket service
require('../services/socketService')(io);

/**
 * Listen on provided port, on all network interfaces.
 */

server.listen(port);
server.on("error", onError);
server.on("listening", onListening);

/**
 * Normalize a port into a number, string, or false.
 */

function normalizePort(val) {
  var port = parseInt(val, 10);

  if (isNaN(port)) {
    // named pipe
    return val;
  }

  if (port >= 0) {
    // port number
    return port;
  }

  return false;
}

/**
 * Event listener for HTTP server "error" event.
 */

function onError(error) {
  if (error.syscall !== "listen") {
    throw error;
  }

  var bind = typeof port === "string" ? "Pipe " + port : "Port " + port;

  // handle specific listen errors with friendly messages
  switch (error.code) {
    case "EACCES":
      console.error(bind + " requires elevated privileges");
      process.exit(1);
      break;
    case "EADDRINUSE":
      console.error(bind + " is already in use");
      process.exit(1);
      break;
    default:
      throw error;
  }
}

/**
 * Event listener for HTTP server "listening" event.
 */

function onListening() {
  console.log("Server is running on port " + port);
  var addr = server.address();
  var bind = typeof addr === "string" ? "pipe " + addr : "port " + addr.port;
  debug("Listening on " + bind);
}
