var express = require("express");
var router = express.Router();
var easebuzzConfig = require("../../configs/easebuzzConfig").config;
var supabaseInstance = require("../../services/supabaseClient").supabase;
const SHA512 = require("crypto-js").SHA512;
const uniqid = require("uniqid");
const sha256 = require("sha256");
const razorpayConfig = require("../../configs/razorpayConfig").config;
const Razorpay = require("razorpay");
const { getMenuId } = require("../mess/dashboard");

const razorpay = new Razorpay({
  key_id: razorpayConfig.key_id,
  key_secret: razorpayConfig.key_secret,
});

const axios = require("axios").default;
const { URLSearchParams } = require("url");
const { randomUUID } = require("crypto");

async function getOutletVendorTransferAmount(paidAmount) { 
  // Calculate fees and taxes
  const razorpayFees = paidAmount * (2 / 100);
  const gstOnRazorpayFees = razorpayFees * (18 / 100);
  const transferFees = paidAmount * (0.25 / 100);
  const gstOnTransferFees = transferFees * (18 / 100);

  // Calculate the transfer amount after deducting fees and taxes
  const transferAmount = paidAmount - razorpayFees - gstOnRazorpayFees - transferFees - gstOnTransferFees;

  // Return the transfer amount in decimal format with a precision of 2 points
  return Number(transferAmount.toFixed(2) * 100);

}


router.get("/", (req, res, next) => {
  res.send({
    success: true,
    message: "Response from payment/customerPayment.js",
  });
});

// console.log(new RegExp(/^[a-zA-Z0-9_\|\-\/]{1,40}$/).test(''))

router.post("/initiate-payment", async (req, res) => {
  const {
    itemTotalPrice,
    productinfo,
    firstname,
    phone,
    email,
    customerAuthUID,
    outletId,
    isDineIn,
    isPickUp,
    isDelivery,
  } = req.body;

  if (!itemTotalPrice || !productinfo || !firstname || !phone || !email || !customerAuthUID || !outletId) {
    return res.status(400).json({
      success: false,
      message: "Missing required fields"
    });
  }

  try {
    const getPriceBreakdownResponse = await getPriceBreakdown(
      outletId,
      itemTotalPrice,
      isDineIn,
      isPickUp,
      isDelivery
    );

    if (!getPriceBreakdownResponse || !getPriceBreakdownResponse.outletBankLabel) {
      throw new Error("Invalid price breakdown response");
    }

    // Create transaction record
    const transactionResponse = await supabaseInstance
      .from("Transaction")
      .insert({
        firstname,
        phone,
        email,
        customerAuthUID,
        outletId,
        amount: getPriceBreakdownResponse?.totalPriceForCustomer,
        itemTotalPrice,
        mealpeVendorAmount: getPriceBreakdownResponse?.mealpeVendorAmount,
        outletVendorAmount: getPriceBreakdownResponse?.outletVendorAmount,
        foodGST: getPriceBreakdownResponse?.foodGST,
        convenienceAmount: getPriceBreakdownResponse?.convenienceAmount,
        convenienceGSTAmount: getPriceBreakdownResponse?.convenienceGSTAmount,
        convenienceTotalAmount: getPriceBreakdownResponse?.convenienceTotalAmount,
        commissionAmount: getPriceBreakdownResponse?.commissionAmount,
        commissionGSTAmount: getPriceBreakdownResponse?.commissionGSTAmount,
        commissionTotalAmount: getPriceBreakdownResponse?.commissionTotalAmount,
        packagingCharge: getPriceBreakdownResponse?.packagingCharge,
        deliveryCharge: getPriceBreakdownResponse?.deliveryCharge,
        tdsAmount: getPriceBreakdownResponse?.TDS,
        tcsAmount: getPriceBreakdownResponse?.TCS,
        isGSTApplied: getPriceBreakdownResponse?.isGstApplied,
        foodBasePrice: getPriceBreakdownResponse?.FoodBasePrice,
        status: "pending",
        paymentType: "online"
      })
      .select("*")
      .maybeSingle();

    if (transactionResponse.error) { 
      console.error("transactionResponse.error => ", transactionResponse.error);
      throw transactionResponse.error;
    }
    
    if (!transactionResponse?.data) {
      throw new Error("Failed to create transaction");
    }

    // Create Razorpay order
    const orderOptions = {
      amount: Math.round(getPriceBreakdownResponse?.totalPriceForCustomer * 100), // Convert to paise and ensure it's an integer
      currency: "INR",
      receipt: transactionResponse.data.txnid,
      payment_capture: 1,
      notes: {
        description: productinfo,
        customerAuthUID,
        transactionId: transactionResponse.data.txnid,
        outletId
      }
    };

    // Add split payment if configured
    let outletVendorTransferAmount = 0;
    if (getPriceBreakdownResponse?.outletBankLabel) {
      outletVendorTransferAmount = await getOutletVendorTransferAmount(getPriceBreakdownResponse?.outletVendorAmount);
      orderOptions.transfers = [{
        account: getPriceBreakdownResponse.outletBankLabel,
        amount: outletVendorTransferAmount, // Already in paise
        currency: "INR",
      }];
    }

    const order = await razorpay.orders.create(orderOptions);

    // Update transaction with Razorpay order details
    const transactionUpdateResponse = await supabaseInstance
      .from("Transaction")
      .update({
        razorpay_order_id: order.id,
        razorpay_order_details: order
      })
      .eq("txnid", transactionResponse.data.txnid)
      .select("*")
      .maybeSingle();

    if (transactionUpdateResponse?.error) throw transactionUpdateResponse.error;

    return res.status(200).json({
      success: true,
      response: {
        orderId: order.id,
        amount: order.amount,
        currency: order.currency
      },
      transactionId: transactionResponse.data.txnid
    });

  } catch (error) {
    console.error("Error in initiate-payment:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to initiate payment",
      error: error.message
    });
  }
});

router.post("/success-payment", async (req, res) => {
  const { razorpay_payment_id, razorpay_order_id, razorpay_signature } = req.body;

  try {
    // Verify payment signature
    const crypto = require('crypto');
    const body = razorpay_order_id + "|" + razorpay_payment_id;
    const expectedSignature = crypto
      .createHmac("sha256", razorpayConfig.key_secret)
      .update(body.toString())
      .digest("hex");

    if (expectedSignature !== razorpay_signature) {
      return res.status(400).json({
        success: false,
        message: "Invalid payment signature"
      });
    }

    // Get transaction details
    const { data: transactionData, error: transactionError } = await supabaseInstance
      .from("Transaction")
      .select("*")
      .eq("razorpay_order_id", razorpay_order_id)
      .maybeSingle();

    if (transactionError) throw transactionError;

    if (!transactionData) {
      return res.status(404).json({
        success: false,
        message: "Transaction not found"
      });
    }

    // Update transaction status
    const { data: updatedTransaction, error: updateError } = await supabaseInstance
      .from("Transaction")
      .update({
        status: "success",
        razorpay_payment_id,
        razorpay_signature
      })
      .eq("txnid", transactionData.txnid)
      .select("*")
      .maybeSingle();

    if (updateError) throw updateError;

    return res.status(200).json({
      success: true,
      message: "Payment successful",
      transaction: updatedTransaction
    });

  } catch (error) {
    console.error("Error in success-payment:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to process payment response",
      error: error.message
    });
  }
});

router.post("/failure-payment", (req, res, next) => {
  const postBody = req.body;
  const query = req.query;
  const params = req.params;

  console.log("f-postBody => ", postBody);
  console.log("f-query =>    ", query);
  console.log("f-params =>   ", params);

  res.send({
    success: true,
    message: "Response from payment/customerPayment.js",
  });
});

router.get("/success-payment", (req, res, next) => {
  const postBody = req.body;
  const query = req.query;
  const params = req.params;

  console.log("s-get-postBody => ", postBody);
  console.log("s-get-query =>    ", query);
  console.log("s-get-params =>   ", params);

  res.send({
    success: true,
    message: "Response from payment/customerPayment.js",
  });
});

router.get("/failure-payment", (req, res, next) => {
  const postBody = req.body;
  const query = req.query;
  const params = req.params;

  console.log("f-get-postBody => ", postBody);
  console.log("f-get-query =>    ", query);
  console.log("f-get-params =>   ", params);

  res.send({
    success: true,
    message: "Response from payment/customerPayment.js",
  });
});

// ==========================Cash Payment Method==========================
router.post("/getTransactionIdForCOD", async (req, res, next) => {
  const {
    itemTotalPrice, //*base price
    productinfo,
    firstname,
    phone,
    email,
    customerAuthUID,
    outletId,
    isDineIn,
    isPickUp,
    isDelivery,
    paymentType,
  } = req.body;

  if (
    itemTotalPrice &&
    productinfo &&
    firstname &&
    phone &&
    email &&
    customerAuthUID &&
    outletId
  ) {
    try {
      const getPriceBreakdownResponse = await getPriceBreakdown(
        outletId,
        itemTotalPrice,
        isDineIn,
        isPickUp,
        isDelivery
      );
      if (getPriceBreakdownResponse) {
        //* -> getPriceBreakdownResponse = is breakdown
        console.log("getPriceBreakdownResponse==>", getPriceBreakdownResponse);
        
        // check if the wallet has enough balance        
        if (paymentType == "wallet") {
          const { data: walletData, error: walletError } =
            await supabaseInstance
              .from("Wallet")
              .select("*")
              .eq("customerAuthUID", customerAuthUID)
              .maybeSingle();

          if (walletError) {
            console.log("walletError => ", walletError);
            throw walletError;
          }

          console.log("walletData => ", walletData);

          if (walletData && walletData.balance < getPriceBreakdownResponse?.totalPriceForCustomer) {
            return res.status(400).json({ success: false, message: "Insufficient balance" });
          }

          const { data: walletTransactionData, error: walletTransactionError } =
            await supabaseInstance
              .from("Wallet_Transaction")
              .insert({
                customerAuthUID,
                amount: getPriceBreakdownResponse?.totalPriceForCustomer,
                bearer: "CUSTOMER",
                transactionType: "DEBIT",
                description: `order payment`,
                outletId,
                walletId: walletData.walletId,
                status: "success",
              })
              .select("*")
              .maybeSingle();

          if (walletTransactionError) {
            throw walletTransactionError;
          }

          if (walletTransactionData) {
            var txnid = walletTransactionData.walletTransactionId;
            const { data: walletUpdateData, error: walletUpdateError } =
              await supabaseInstance
                .from("Wallet")
                .update({
                  balance: walletData.balance - getPriceBreakdownResponse?.totalPriceForCustomer,
                })
                .eq("customerAuthUID", customerAuthUID)
                .select("*")
                .maybeSingle();

            if (walletUpdateError) {
              throw walletUpdateError;
            }

            if (walletUpdateData) {
              console.log("success walletUpdateData => ", walletUpdateData);
            }
          }
        }

        const transactionResponse = await supabaseInstance
          .from("Transaction")
          .insert({
            // if payment type is wallet then txnid will be walletTransactionId else it will be random
            txnid: txnid || randomUUID(),
            firstname,
            phone,
            email,
            customerAuthUID,
            outletId,
            amount: getPriceBreakdownResponse?.totalPriceForCustomer,
            itemTotalPrice,
            mealpeVendorAmount: getPriceBreakdownResponse?.mealpeVendorAmount,
            outletVendorAmount: getPriceBreakdownResponse?.outletVendorAmount,
            foodGST: getPriceBreakdownResponse?.foodGST,
            convenienceAmount: getPriceBreakdownResponse?.convenienceAmount,
            convenienceGSTAmount:
              getPriceBreakdownResponse?.convenienceGSTAmount,
            convenienceTotalAmount:
              getPriceBreakdownResponse?.convenienceTotalAmount,
            commissionAmount: getPriceBreakdownResponse?.commissionAmount,
            commissionGSTAmount: getPriceBreakdownResponse?.commissionGSTAmount,
            commissionTotalAmount:
              getPriceBreakdownResponse?.commissionTotalAmount,
            packagingCharge: getPriceBreakdownResponse?.packagingCharge,
            deliveryCharge: getPriceBreakdownResponse?.deliveryCharge,
            tdsAmount: getPriceBreakdownResponse?.TDS,
            tcsAmount: getPriceBreakdownResponse?.TCS,
            isGSTApplied: getPriceBreakdownResponse?.isGstApplied,
            foodBasePrice: getPriceBreakdownResponse?.FoodBasePrice,
            paymentType: paymentType,
            status : "success"
          })
          .select("*")
          .maybeSingle();
        if (transactionResponse?.data) {
          console.log("transactionResponse=>", transactionResponse);
          return res
            .status(200)
            .json({ success: true, response: transactionResponse?.data });
        } else {
          throw transactionResponse?.error;
        }
      } else {
        throw getPriceBreakdownResponse?.error || getPriceBreakdownResponse;
      }
    } catch (error) {
      console.error("error => ", error);
      res.status(500).json({ success: false, error: error });
    }
  } else {
    res
      .status(500)
      .json({ success: false, error: "Please add email id to your account" });
  }
});

// ==========================/Cash Payment Method==========================

// router.post('/request-refund', async (req, res, next) => {

//     const { amount, refund_amount, phone, email,orderId } = req.body;

//     if (amount && refund_amount && phone && email,orderId) {

//         try {
//             const orderResponse = await supabaseInstance.from("Order").select("*,customerAuthUID(*),outletId(*)").eq("orderId", orderId).maybeSingle();

//             if (orderResponse?.data) {
//                 console.log("orderResponse=>", orderResponse);

//                 var hashstring = easebuzzConfig.key + "|" + orderResponse?.data?.txnid + "|" + amount + "|" + refund_amount + "|" + email + "|" + phone + "|||||||||||" + easebuzzConfig.salt

//                 const _generateHash = generateHash(hashstring);
//                  console.log("_generateHash => ", _generateHash);
//                 // const options = {
//                 //     method: 'POST',
//                 //     url: `${easebuzzConfig.easebuzzBaseUrl}/transaction/v1/refund`,
//                 //     headers: { 'Content-Type': 'application/json', Accept: 'application/json' },
//                 //     data: {
//                 //         key: easebuzzConfig.key,
//                 //         txnid: orderResponse.data.txnid,
//                 //         refund_amount: refund_amount,
//                 //         phone: phone,
//                 //         email: email,
//                 //         amount: amount,
//                 //         hash: _generateHash
//                 //     }
//                 // };

//                 // axios.request(options).then(async (response) => {
//                 //     console.log("refund Response in then =>", response);
//                 //     res.status(200).json({ success: true, response: response?.data })
//                 // }).catch(async (error) => {
//                 //     console.error(error);
//                 //     console.log("resfund Response in error=>", response)
//                 //     res.status(200).json({ success: false, response: error })
//                 // })
//             } else {
//                 throw orderResponse.error
//             }
//         } catch (error) {
//             console.error("error => ", error);
//             res.status(500).json({ success: false, error: error });
//         }
//     } else {
//         res.status(500).json({ success: false, error: "Invalid Post Body" });
//     }
// })

router.post("/get-price-breakdown", async (req, res, next) => {
  const { outletId, itemTotalPrice, isDineIn, isPickUp, isDelivery } = req.body;

  try {
    const getPriceBreakdownResponse = await getPriceBreakdown(
      outletId,
      itemTotalPrice,
      isDineIn,
      isPickUp,
      isDelivery
    );
    console.log("getPriceBreakdownResponse => ", getPriceBreakdownResponse);

    res.status(200).json({ success: true, ...getPriceBreakdownResponse });
  } catch (error) {
    res.status(500).json({ success: false, error: error });
  }
});

router.post("/initiate-payment-with-order", async (req, res, next) => {
  const {
    itemTotalPrice, //*base price
    productinfo,
    firstname,
    phone,
    email,
    customerAuthUID,
    outletId,
    orderObject,
  } = req.body;

  const surl = `https://${req.get("host")}/payment/customer/success-payment`;
  const furl = `https://${req.get("host")}/payment/customer/failure-payment`;

  if (
    itemTotalPrice &&
    productinfo &&
    firstname &&
    phone &&
    email &&
    customerAuthUID &&
    outletId &&
    orderObject
  ) {
    try {
      let encodedParams = new URLSearchParams();

      const getPriceBreakdownResponse = await getPriceBreakdown(
        outletId,
        itemTotalPrice
      );
      if (
        getPriceBreakdownResponse &&
        getPriceBreakdownResponse.outletBankLabel
      ) {
        //* -> getPriceBreakdownResponse = is breakdown
        console.log("getPriceBreakdownResponse==>", getPriceBreakdownResponse);
        const transactionResponse = await supabaseInstance
          .from("Transaction")
          .insert({
            firstname,
            phone,
            email,
            customerAuthUID,
            outletId,
            amount: getPriceBreakdownResponse?.totalPriceForCustomer,
            itemTotalPrice,
            mealpeVendorAmount: getPriceBreakdownResponse?.mealpeVendorAmount,
            outletVendorAmount: getPriceBreakdownResponse?.outletVendorAmount,
            foodGST: getPriceBreakdownResponse?.foodGST,
            convenienceAmount: getPriceBreakdownResponse?.convenienceAmount,
            convenienceGSTAmount:
              getPriceBreakdownResponse?.convenienceGSTAmount,
            convenienceTotalAmount:
              getPriceBreakdownResponse?.convenienceTotalAmount,
            commissionAmount: getPriceBreakdownResponse?.commissionAmount,
            commissionGSTAmount: getPriceBreakdownResponse?.commissionGSTAmount,
            commissionTotalAmount:
              getPriceBreakdownResponse?.commissionTotalAmount,
            packagingCharge: getPriceBreakdownResponse?.packagingCharge,
            deliveryCharge: getPriceBreakdownResponse?.deliveryCharge,
            tdsAmount: getPriceBreakdownResponse?.TDS,
            tcsAmount: getPriceBreakdownResponse?.TCS,
            isGSTApplied: getPriceBreakdownResponse?.isGstApplied,
            foodBasePrice: getPriceBreakdownResponse?.FoodBasePrice,
            orderPostBody: orderObject || null,
          })
          .select("*")
          .maybeSingle();
        if (transactionResponse?.data) {
          console.log("transactionResponse=>", transactionResponse);

          var hashstring =
            easebuzzConfig.key +
            "|" +
            transactionResponse?.data?.txnid +
            "|" +
            getPriceBreakdownResponse?.totalPriceForCustomer +
            "|" +
            productinfo +
            "|" +
            firstname +
            "|" +
            email +
            "|||||||||||" +
            easebuzzConfig.salt;

          const _generateHash = generateHash(hashstring);
          // console.log("_generateHash => ", _generateHash);

          // encodedParams.set('key', easebuzzConfig.key);
          // encodedParams.set('txnid', transactionResponse?.data?.txnid);
          // encodedParams.set('amount', basePrice);
          // encodedParams.set('productinfo', productinfo);
          // encodedParams.set('firstname', firstname);
          // encodedParams.set('phone', phone);
          // encodedParams.set('email', email);
          // encodedParams.set('surl', surl);
          // encodedParams.set('furl', furl);
          // encodedParams.set('hash', _generateHash);
          // encodedParams.set('udf1', '');
          // encodedParams.set('udf2', '');
          // encodedParams.set('udf3', '');
          // encodedParams.set('udf4', '');
          // encodedParams.set('udf5', '');
          // encodedParams.set('udf6', '');
          // encodedParams.set('udf7', '');
          // encodedParams.set('address1', '');
          // encodedParams.set('address2', '');
          // encodedParams.set('city', '');
          // encodedParams.set('state', '');
          // encodedParams.set('country', '');
          // encodedParams.set('zipcode', '');
          // encodedParams.set('show_payment_mode', '');
          // if (getPriceBreakdownResponse?.outletBankLabel) {
          //     encodedParams.set('split_payments', {[easebuzzConfig.mealpe_bank_label] : getPriceBreakdownResponse?.mealpeVendorAmount, [getPriceBreakdownResponse.outletBankLabel]: getPriceBreakdownResponse?.outletVendorAmount});
          // }
          // encodedParams.set('request_flow', '');
          // encodedParams.set('sub_merchant_id', '');
          // encodedParams.set('payment category', '');
          // encodedParams.set('account_no', '');

          let postBody = {
            key: easebuzzConfig.key,
            txnid: transactionResponse?.data?.txnid,
            amount: getPriceBreakdownResponse?.totalPriceForCustomer,
            productinfo: productinfo,
            firstname: firstname,
            phone: phone,
            email: email,
            surl: surl,
            furl: furl,
            hash: _generateHash,
            udf1: "",
            udf2: "",
            udf3: "",
            udf4: "",
            udf5: "",
            udf6: "",
            udf7: "",
            address1: "",
            address2: "",
            city: "",
            state: "",
            country: "",
            zipcode: "",
            // 'show_payment_mode': '',
            // 'request_flow': '',
            // 'sub_merchant_id': '',
            // 'payment :ategory', '',
            // 'account_no': '',
          };
          if (
            getPriceBreakdownResponse?.outletBankLabel &&
            easebuzzConfig.mealpe_bank_label &&
            getPriceBreakdownResponse?.mealpeVendorAmount > 0 &&
            getPriceBreakdownResponse?.outletVendorAmount > 0
          ) {
            postBody.split_payments = {
              [easebuzzConfig.mealpe_bank_label]:
                getPriceBreakdownResponse?.mealpeVendorAmount,
              [getPriceBreakdownResponse.outletBankLabel]:
                getPriceBreakdownResponse?.outletVendorAmount,
            };
          }

          // console.log(encodedParams);

          const options = {
            method: "POST",
            url: `${easebuzzConfig.easebuzzBaseUrl}/payment/initiateLink`,
            headers: {
              "Content-Type": "application/x-www-form-urlencoded",
              Accept: "application/json",
            },
            data: postBody,
          };

          // const encodedParamsbj = encodedParams?.toString()?.split("&")?.map(m => m?.split("="))?.reduce((a, v) => ({ ...a, [v[0]]: decodeURIComponent(v[1])}), {}) ;
          await axios
            .request(options)
            .then(async (initiateLinkResponse) => {
              const transactionUpdateResponse = await supabaseInstance
                .from("Transaction")
                .update({
                  initiateLink_post_body: postBody,
                  initiateLink_response: initiateLinkResponse.data,
                })
                .eq("txnid", transactionResponse?.data?.txnid)
                .select("*")
                .maybeSingle();
              console.log(
                "transactionUpdateResponse in then =>",
                transactionUpdateResponse
              );

              if (transactionUpdateResponse?.data) {
                res.status(200).json({
                  success: true,
                  response: initiateLinkResponse?.data,
                });
              } else {
                res.status(500).json({
                  success: false,
                  response: transactionUpdateResponse.error.message,
                });
              }
            })
            .catch(async (error) => {
              console.error(error);
              const transactionUpdateResponse = await supabaseInstance
                .from("Transaction")
                .update({
                  initiateLink_post_body: postBody,
                  initiateLink_error: error,
                })
                .eq("txnid", transactionResponse?.data?.txnid)
                .select("*")
                .maybeSingle();
              console.log(
                "transactionUpdateResponse in error=>",
                transactionUpdateResponse
              );
              res.status(500).json({ success: false, response: error });
            });
        } else {
          throw transactionResponse.error;
        }
      } else if (!getPriceBreakdownResponse.outletBankLabel) {
        res
          .status(500)
          .json({ success: false, error: "Bank field not found." });
      } else {
        throw getPriceBreakdownResponse?.error || getPriceBreakdownResponse;
      }
    } catch (error) {
      console.error("error => ", error);
      res.status(500).json({ success: false, error: error });
    }
  } else {
    res.status(500).json({ success: false, error: "Invalid Post Body" });
  }
});

function encodedParamsToObject(encodedParams) {
  let obj = {};
  const ent = encodedParams.entries();
  // console.log("encodedParams.size => ", encodedParams.size);

  for (let index = 0; index < encodedParams.size; index++) {
    const element = ent.next();
    obj[element.value[0]] = element.value[1];
  }
  return { ...obj };
}

// function getPriceBreakdown(outletId, basePrice, isDineIn=false, isPickUp=false, isDelivery=false) {
//     basePrice = +basePrice;
//     return new Promise(async (resolve, reject) => {
//         if (outletId && basePrice) {
//             try {
//                 const outletResponse = await supabaseInstance.from("Outlet").select('*').eq("outletId", outletId).maybeSingle();
//                 if (outletResponse?.data) {
//                     const outletData = outletResponse?.data;

//                     let packagingCharge = 0;
//                     if (isPickUp === true || isDelivery === true) {
//                         packagingCharge = outletData.packaging_charge;
//                         basePrice = basePrice + packagingCharge;
//                     }

//                     let foodGST = 0;
//                     if (outletResponse?.data?.isGSTShow) {
//                         foodGST = (5 * basePrice) / 100;
//                     } else {
//                         //todo calculate basePrice and foodGST
//                         foodGST = (5 * basePrice) / 100;
//                         basePrice = basePrice - foodGST;
//                     }

//                     const convenienceAmount = (outletData?.convenienceFee * basePrice) / 100;
//                     const convenienceGSTAmount = (18 * convenienceAmount) / 100;
//                     const convenienceTotalAmount = convenienceAmount + convenienceGSTAmount;

//                     //* total amount customer pay to mealpe
//                     const totalPriceForCustomer = Number(Number(Math.round(basePrice + foodGST + convenienceTotalAmount))?.toFixed(2));

//                     const commissionAmount = (outletData?.commissionFee * basePrice) / 100;
//                     const commissionGSTAmount =  (18 * commissionAmount) / 100;
//                     const commissionTotalAmount =  commissionAmount + commissionGSTAmount;

//                     // const outletVendorAmount = Number((totalPriceForCustomer - commissionTotalAmount)?.toFixed(2));
//                     const outletVendorAmount = Number((basePrice - commissionTotalAmount)?.toFixed(2));

//                     const mealpeVendorAmount = Number((totalPriceForCustomer - outletVendorAmount)?.toFixed(2));
//                     const outletBankLabel = outletData?.bankLabel || null;

//                     resolve({
//                         success: true,

//                         basePrice,
//                         foodGST,
//                         convenienceAmount,
//                         convenienceGSTAmount,
//                         convenienceTotalAmount,
//                         totalPriceForCustomer,
//                         commissionAmount,
//                         commissionGSTAmount,
//                         commissionTotalAmount,
//                         mealpeVendorAmount,
//                         outletVendorAmount,
//                         packagingCharge,

//                         outletBankLabel
//                     })

//                 } else {
//                     reject({success: false, message: "Outlet id is wrong."});
//                 }
//             } catch (error) {
//                 console.log(error);
//                 reject({success: false, error: error});
//             }
//         } else {
//             reject({success: false, message: "Please provide valid values."});
//         }
//     })
// }

const gstExemptedOutlets = ["ab26af06-087f-4ee3-b2f8-78f5d4233ba8","ab26af06-087f-4ee3-b2f8-78f5d4233ba8"]

function getPriceBreakdown(
  outletId,
  itemTotalPrice,
  isDineIn = false,
  isPickUp = false,
  isDelivery = false
) {
  itemTotalPrice = +itemTotalPrice;
  return new Promise(async (resolve, reject) => {
    if (outletId && itemTotalPrice) {
      try {
        const outletResponse = await supabaseInstance
          .from("Outlet")
          .select("*")
          .eq("outletId", outletId)
          .maybeSingle();
        if (outletResponse?.data) {
          const outletData = outletResponse?.data;
          let isGstApplied = false;
          let foodGST = 0;
          let commissionAmount;
          let FoodBasePrice;
          let convenienceAmount;
          let commissionTotalAmount;
          let TDS;
          let TCS;
          let convenienceGSTAmount;
          let totalPriceForCustomer;
          let mealpeVendorAmount;
          let outletVendorAmount;
          let convenienceTotalAmount;
          let commissionGSTAmount;
          let deliveryCharge = 0;
          let packagingCharge = 0;

          if (isPickUp === true) {
            packagingCharge = outletData.packaging_charge;
          }

          if (isDelivery === true) {
            packagingCharge = outletData.packaging_charge;
            deliveryCharge = outletData.deliveryCharge;
          }

          // convenienceAmount = (outletData.convenienceFee * FoodBasePrice) / 100;
          convenienceAmount =
            (outletData.convenienceFee * itemTotalPrice) / 100;
          convenienceGSTAmount = (18 * convenienceAmount) / 100;
          convenienceTotalAmount = Number(
            (convenienceAmount + convenienceGSTAmount)?.toFixed(2)
          );

          // commissionAmount = (outletData.commissionFee * (deliveryCharge + packagingCharge + FoodBasePrice)) / 100;
          commissionAmount =
            (outletData.commissionFee *
              (deliveryCharge + packagingCharge + itemTotalPrice)) /
            100;
          commissionGSTAmount = (18 * commissionAmount) / 100;
          commissionTotalAmount = Number(
            (commissionAmount + commissionGSTAmount)?.toFixed(2)
          );

          // remove gst when 0 commission and convenience fee
          if (outletData.commissionFee === 0 && outletData.convenienceFee === 0) {
            foodGST = 0;
            isGstApplied = false;
            FoodBasePrice = itemTotalPrice;
          } else if (outletData?.isGSTShow === true) {
            isGstApplied = true;
            foodGST = Number(((5 * itemTotalPrice) / 100).toFixed(2));
            FoodBasePrice = itemTotalPrice;
          } else {
            isGstApplied = false;
            // FoodBasePrice = Number(((itemTotalPrice * 100) / 105).toFixed(2));
            // foodGST = Number((itemTotalPrice - FoodBasePrice).toFixed(2));
            foodGST = Number(((5 * itemTotalPrice) / 100).toFixed(2));
            FoodBasePrice = itemTotalPrice - foodGST;
          }

          // TDS = (1 * FoodBasePrice) / 100; // remove gst when 0 commission and convenience fee
          TDS = (commissionTotalAmount === 0 && convenienceTotalAmount === 0) ? 0 : itemTotalPrice / 100;
          // TCS = (packagingCharge + deliveryCharge) / 101;
          TCS = 0;

          if (outletData?.isGSTShow === true) {
            //? Amount Collected from Customer = Base Price + CF + GST = 106
            totalPriceForCustomer = Number(
              (
                itemTotalPrice +
                convenienceTotalAmount +
                foodGST +
                packagingCharge +
                deliveryCharge
              )?.toFixed(2)
            );
          } else {
            //? Amount Collected from Customer = Base Price + CF = 101
            totalPriceForCustomer = Number(
              (
                itemTotalPrice +
                convenienceTotalAmount +
                packagingCharge +
                deliveryCharge
              )?.toFixed(2)
            );
          }
          //? Net Deductions = (GST Amount + Commission + CF + 1% TDS)
          mealpeVendorAmount = Number(
            (
              (foodGST = (outletData.isGSTCollectedByMealpe ? foodGST : 0)) +
              convenienceTotalAmount +
              commissionTotalAmount +
              TDS +
              TCS
            )?.toFixed(2)
          );

          //? Settlement amount => Amount Collected - Net Deductions
          outletVendorAmount = Number(
            (totalPriceForCustomer - mealpeVendorAmount)?.toFixed(2)
          );

          const outletBankLabel = outletData?.bankLabel || null;

          const isAcceptingCash = outletData?.isAcceptingCash || false;
          const isAcceptingWallet = outletData?.isAcceptingWallet || false;
          const isAcceptingOnline = outletData?.isAcceptingOnline || false;
          const allowCustomDeliveryLocation =  outletData?.allowCustomDeliveryLocation || false;

          resolve({
            success: true,
            outletBankLabel,
            itemTotalPrice,
            foodGST,
            commissionAmount,
            FoodBasePrice,
            convenienceAmount,
            commissionTotalAmount,
            TDS,
            TCS,
            convenienceGSTAmount,
            totalPriceForCustomer,
            mealpeVendorAmount,
            outletVendorAmount,
            packagingCharge,
            deliveryCharge,
            isGstApplied,
            convenienceTotalAmount,
            commissionGSTAmount,
            isAcceptingCash,
            isAcceptingWallet,
            isAcceptingOnline,
            allowCustomDeliveryLocation,
          });
        } else {
          reject({ success: false, message: "Outlet id is wrong." });
        }
      } catch (error) {
        console.log(error);
        reject({ success: false, error: error });
      }
    } else {
      reject({ success: false, message: "Please provide valid values." });
    }
  });
}

//===================== phonePe integration =======================

const PHONE_PE_HOST_URL = "https://api-preprod.phonepe.com/apis/pg-sandbox";
const MERCHENT_ID = "PGTESTPAYUAT";
const SALT_INDEX = 1;
const SALT_KEY = "099eb0cd-02cf-4e2a-8aca-3e6c6aff0399";
const payendpoint = "/pg/v1/pay";

//this is api is post TODO: set api to post instead of get in prod
router.get("/pay", async (req, res) => {
  const merchantTranscationId = uniqid();
  const user_id = 123;

  console.log("enter in phone pe ", merchantTranscationId);

  const payload = {
    merchantId: MERCHENT_ID,
    merchantTransactionId: merchantTranscationId,
    merchantUserId: user_id,
    amount: 10000, // amount in paise
    redirectUrl: `http://localhost:8055/redirect-url/${merchantTranscationId}`,
    redirectMode: "REDIRECT",
    mobileNumber: "9999999999", // number of user
    paymentInstrument: {
      type: "PAY_PAGE",
    },
  };

  const bufferObj = Buffer.from(JSON.stringify(payload), "utf8");
  const base64incodedpayload = bufferObj.toString("base64");
  const xVerify = sha256(
    base64incodedpayload + payendpoint + SALT_KEY + "###" + SALT_INDEX
  );

  const options = {
    method: "post",
    url: `${PHONE_PE_HOST_URL}${payendpoint}`,
    headers: {
      accept: "application/json",
      "Content-Type": "application/json",
      "X-VERIFY": xVerify,
    },
    data: {
      request: base64incodedpayload,
    },
  };
  await axios
    .request(options)
    .then(function (response) {
      console.log(response.data);
      const url = response.data.data.instrumentResponse.redirectInfo.url;
      res.redirect(url);
      res.send(response.data);
    })
    .catch(function (error) {
      console.error("error ", error);
    });
});

router.get("/redirect-url/:merchantTranscationId", async (req, res) => {
  const { merchantTranscationId } = req.params;

  if (merchantTranscationId) {
    const xVerify =
      sha256(
        `/pg/v1/status/${MERCHENT_ID}/${merchantTranscationId}` + SALT_KEY
      ) +
      "###" +
      SALT_INDEX;
    const options = {
      method: "get",
      url: `${PHONE_PE_HOST_URL}/pg/v1/status/${MERCHENT_ID}/${merchantTranscationId}`,
      headers: {
        accept: "application/json",
        "Content-Type": "application/json",
        "X-MERCHANT-ID": merchantTranscationId,
        "X-VERIFY": xVerify,
      },
    };
    axios
      .request(options)
      .then(function (response) {
        console.log(response.data);
        // if(response.data.code === "PAYMENT_SUCCESS"){
        //     //redirect user to frontend successs page
        // }else if(response.data.code === "PAYMENT_ERROR"){
        //     //redirect user to frontend error page
        // }else{
        //     //pending page
        // }
        res.send(response.data);
      })
      .catch(function (error) {
        console.error(error);
      });
  } else {
    res.send({ error: "Error" });
  }
});

//====================== Subscription For Outlet ==============================

const generateHash = (hashstring) => {
  return SHA512(hashstring).toString();
};

router.post("/initiate-payment-for-subscription", async (req, res, next) => {
  const {
    subscriptionCode,
    subscriptionInfo,
    firstname,
    phone,
    email,
    outletId,
  } = req.body;

  const subscriptionPrice = ["84960", "53100", "28320", "21240"];

  if (
    subscriptionPrice[subscriptionCode] &&
    subscriptionInfo &&
    phone &&
    email &&
    firstname &&
    outletId
  ) {
    try {
      const { data, error } = await supabaseInstance
        .from("Subscription")
        .select("*")
        .eq("outletId", outletId);

      if (error) {
        throw new Error(`Supabase select error: ${error.message}`);
      }

      console.log("data ", data);

      if (data) {
        try {
          const { data: deleteSubscription, error: deleteSubError } =
            await supabaseInstance
              .from("Subscription")
              .delete()
              .eq("outletId", outletId)
              .eq("isComplete", false)
              .eq("isActive", false);

          if (deleteSubError) {
            // Handle the error if there's an issue with the deletion
            console.error(
              "Error deleting subscription:",
              deleteSubError.message
            );
            throw new Error(
              `Failed to delete subscription: ${deleteSubError.message}`
            );
          } else {
            // Handle the successful deletion
            console.log(
              "Subscription deleted successfully:",
              deleteSubscription
            );
          }
        } catch (error) {
          // Handle any unexpected errors
          console.error("Unexpected error:", error.message);
          throw new Error(`Unexpected error occurred: ${error.message}`);
        }
      }

      const subStartDate = new Date();
      let subEndDate = new Date();
      if (subscriptionCode === 0) {
        subEndDate.setMonth(subEndDate.getMonth() + 12);
      } else if (subscriptionCode === 1) {
        subEndDate.setMonth(subEndDate.getMonth() + 6);
      } else if (subscriptionCode === 2) {
        subEndDate.setMonth(subEndDate.getMonth() + 12);
      } else if (subscriptionCode === 3) {
        subEndDate.setMonth(subEndDate.getMonth() + 6);
      }

      const transactionResponse = await supabaseInstance
        .from("Subscription")
        .insert({
          phone,
          email,
          outletId,
          amount: Number(subscriptionPrice[subscriptionCode]).toFixed(2),
          subStartDate,
          subEndDate,
        })
        .select("*")
        .maybeSingle();

      if (transactionResponse?.data) {
        console.log("transactionResponse=>", transactionResponse);

        // Create Razorpay order
        const orderOptions = {
          amount: Math.round(transactionResponse.data.amount * 100), // Convert to paise
          currency: "INR",
          receipt: transactionResponse.data.subscriptionId,
          payment_capture: 1,
          notes: {
            description: subscriptionInfo,
            customerAuthUID: transactionResponse.data.customerAuthUID,
            subscriptionId: transactionResponse.data.subscriptionId,
            outletId,
          },
        };

        const order = await razorpay.orders.create(orderOptions);

        // Update transaction with Razorpay order details
        const transactionUpdateResponse = await supabaseInstance
          .from("Subscription")
          .update({
            razorpay_order_id: order.id,
            razorpay_order_details: order,
          })
          .eq("subscriptionId", transactionResponse.data.subscriptionId)
          .select("*")
          .maybeSingle();

        if (transactionUpdateResponse?.error) throw transactionUpdateResponse.error;

        res.status(200).json({
          success: true,
          response: {
            orderId: order.id,
            amount: order.amount,
            currency: order.currency,
          },
          subscriptionId: transactionResponse.data.subscriptionId,
        });
      } else {
        throw transactionResponse.error;
      }
    } catch (error) {
      console.error("error => ", error);
      res.status(500).json({ success: false, error: error });
    }
  } else {
    res
      .status(500)
      .json({ success: false, error: "Please add email id to your account" });
  }
});

router.post("/check-subscription-valid", async (req, res) => {
  try {
    const { outletId } = req.body;

    if (!outletId) {
      return res
        .status(400)
        .json({ success: false, message: "outletId is required" });
    }

    const today = new Date().toISOString();

    const { data, error } = await supabaseInstance
      .from("Subscription")
      .select("*")
      .eq("outletId", outletId)
      .filter("subStartDate", "lte", today)
      .filter("subEndDate", "gte", today)
      .eq("isActive", true)
      .eq("isComplete", true)
      .maybeSingle();

    if (error) {
      console.error("Supabase query error:", error);
      return res
        .status(500)
        .json({ success: false, message: "Internal server error" });
    }

    //if there are records other than this date range then set isActive to false
    const { error: updateError } = await supabaseInstance
      .from("Subscription")
      .update({ isActive: false })
      .eq("outletId", outletId)
      .filter("subEndDate", "lt", today)
      .eq("isActive", true);

    if (updateError) {
      console.error("Active flag update error", updateError);
      throw updateError;
    }

    if (data) {
      return res.status(200).json({
        success: true,
        isValid: true,
        message: "Subscription is valid.",
      });
    } else {
      return res.status(200).json({
        success: true,
        isValid: false,
        message: "Subscription has expired.",
      });
    }
  } catch (error) {
    console.error("Server error:", error);
    return res
      .status(500)
      .json({ success: false, message: "Internal server error" });
  }
});

router.post("/cancel-subscription", async (req, res) => {
  try {
    const { outletId } = req.body;
    if (!outletId) {
      return res.status(400).json({ message: "outletId is required" });
    }

    const { data, error } = await supabaseInstance
      .from("Subscription")
      .delete()
      .eq("outletId", outletId)
      .eq("isComplete", false)
      .eq("isActive", false);

    if (error) {
      throw error;
    }

    if (!!data) {
      return res.status(404).json({ message: "Subscription not found" });
    }

    return res
      .status(200)
      .json({ data, message: "Subscription canceled successfully" });
  } catch (error) {
    console.error("Error while canceling subscription:", error);
    res.status(500).json({ message: "Internal server error" });
  }
});

router.post("/changeTxnStatus", async (req, res) => {
  const { razorpay_payment_id, razorpay_order_id, razorpay_signature } = req.body; // Expecting these in the request body

  if (!razorpay_payment_id || !razorpay_order_id || !razorpay_signature) {
    return res.status(400).json({ message: "Payment details are required" });
  }

  try {
    // Verify payment signature
    const crypto = require('crypto');
    const body = razorpay_order_id + "|" + razorpay_payment_id;
    const expectedSignature = crypto
      .createHmac("sha256", razorpayConfig.key_secret)
      .update(body.toString())
      .digest("hex");

    if (expectedSignature !== razorpay_signature) {
      return res.status(400).json({ message: "Invalid payment signature" });
    }

    // Update subscription status
    const { data, error } = await supabaseInstance
      .from("Subscription")
      .update({ isComplete: true, isActive: true, razorpay_payment_id, razorpay_signature })
      .eq("razorpay_order_id", razorpay_order_id)
      .select("*");

    if (error) {
      throw error;
    }

    return res
      .status(200)
      .json({ success: true, data, message: "Subscription status updated successfully" });
  } catch (error) {
    console.error("Error while updating subscription status:", error);
    res.status(500).json({ message: "Internal server error" });
  }
});

router.get("/get-subscription-details/:outletId", async (req, res) => {
  const { outletId } = req.params;

  if (!outletId) { 
    return res.status(400).json({ success: false, message: "outletId is required" });
  }

  try {
    const { data, error } = await supabaseInstance
      .from("Subscription")
      .select("*")
      .eq("outletId", outletId)
      .order("subStartDate", { ascending: false })
      .maybeSingle();

    if (error) {
      throw error;
    }

    if (!data) {
      return res.status(404).json({ success: false, message: "Subscription not found" });
    }

    return res.status(200).json({ success: true, data });
  } catch (error) {
    console.error(error);
    res.status(500).json({ success: false, error: error.message });
  }
});
//====================== /Subscription For Outlet ==============================



//====================== QR code payment ======================================
/**
 * @description Generate QR code payment
 * @param {number} itemTotalPrice
 * @param {string} productinfo
 * @param {string} firstname
 * @param {string} phone
 * @param {string} email
 * @param {string} customerAuthUID
 * @param {string} outletId
 * @param {boolean} isDineIn
 * @param {boolean} isPickUp
 * @param {boolean} isDelivery
 * @returns {Promise<Object>} qr code data
 */
router.post("/generate-qr-code", async (req, res) => {
  const {
    itemTotalPrice,
    productinfo,
    firstname,
    phone,
    email,
    customerAuthUID,
    outletId,
    isDineIn,
    isPickUp,
    isDelivery,
  } = req.body;

  if (!itemTotalPrice || !productinfo || !firstname || !phone || !email || !customerAuthUID || !outletId) {
    return res.status(400).json({
      success: false,
      message: "Missing required fields"
    });
  }

  console.log(JSON.stringify(req.body, null, 2));

  try {
    const getPriceBreakdownResponse = await getPriceBreakdown(
      outletId,
      itemTotalPrice,
      isDineIn,
      isPickUp,
      isDelivery
    );

    if (!getPriceBreakdownResponse || !getPriceBreakdownResponse.outletBankLabel) {
      throw new Error("Invalid price breakdown response");
    }

    // Create transaction record
    const transactionResponse = await supabaseInstance
      .from("Transaction")
      .insert({
        firstname,
        phone,
        email,
        customerAuthUID,
        outletId,
        amount: getPriceBreakdownResponse?.totalPriceForCustomer,
        itemTotalPrice,
        mealpeVendorAmount: getPriceBreakdownResponse?.mealpeVendorAmount,
        outletVendorAmount: getPriceBreakdownResponse?.outletVendorAmount,
        foodGST: getPriceBreakdownResponse?.foodGST,
        convenienceAmount: getPriceBreakdownResponse?.convenienceAmount,
        convenienceGSTAmount: getPriceBreakdownResponse?.convenienceGSTAmount,
        convenienceTotalAmount: getPriceBreakdownResponse?.convenienceTotalAmount,
        commissionAmount: getPriceBreakdownResponse?.commissionAmount,
        commissionGSTAmount: getPriceBreakdownResponse?.commissionGSTAmount,
        commissionTotalAmount: getPriceBreakdownResponse?.commissionTotalAmount,
        packagingCharge: getPriceBreakdownResponse?.packagingCharge,
        deliveryCharge: getPriceBreakdownResponse?.deliveryCharge,
        tdsAmount: getPriceBreakdownResponse?.TDS,
        tcsAmount: getPriceBreakdownResponse?.TCS,
        isGSTApplied: getPriceBreakdownResponse?.isGstApplied,
        foodBasePrice: getPriceBreakdownResponse?.FoodBasePrice,
        status: "pending",
        paymentType: "online"
      })
      .select("*")
      .maybeSingle();

    if (!transactionResponse?.data) {
      throw new Error("Failed to create transaction");
    }

    // Create QR code payment
    const qrOptions = {
      type: "upi_qr",
      name: "MealPe Payment",
      usage: "single_use",
      fixed_amount: true,
      payment_amount: Math.round(getPriceBreakdownResponse?.totalPriceForCustomer * 100), // Convert to paise
      description: productinfo,
      close_by: Math.floor(Date.now() / 1000) + 1800, // QR code expires in 30 minutes
      notes: {
        transactionId: transactionResponse.data.txnid,
        outletId: outletId,
        customerId: customerAuthUID,
        customerName: firstname,
        customerEmail: email,
        customerPhone: phone,
        project:"mealpe"
      },
    };


    const qrCode = await razorpay.qrCode.create(qrOptions);
    // Update transaction with QR code details
    const transactionUpdateResponse = await supabaseInstance
      .from("Transaction")
      .update({
        razorpay_order_id: qrCode.id,
        razorpay_order_details: qrCode
      })
      .eq("txnid", transactionResponse.data.txnid)
      .select("*")
      .maybeSingle();

    if (transactionUpdateResponse?.error) throw transactionUpdateResponse.error;

    // Return the QR code details directly
    return res.status(200).json({
      success: true,
      response: {
        qrCodeId: qrCode.id,
        qrCodeImage: qrCode.image_url, // Return the direct image URL
        amount: qrCode.payment_amount / 100, // Convert back to rupees
        currency: "INR",
        expiresAt: qrCode.close_by
      },
      transactionId: transactionResponse.data.txnid,
      txnData: transactionResponse.data
    });

  } catch (error) {
    console.error("Error in generate-qr-payment:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to generate QR code payment",
      error: error.message
    });
  }
});


/**
 * @description Generate QR code payment for order delivery
 * @param {string} outletId
 */
router.post("/generate-qr-code-for-collection", async (req, res) => {
  const { outletId, itemTotalPrice, productinfo, customerAuthUID, transactionId, firstname, phone } = req.body;

  if (!outletId || !itemTotalPrice || !productinfo || !customerAuthUID || !transactionId || !firstname || !phone) {
    return res.status(400).json({
      success: false,
      message: `Missing fields: ${outletId? "" : "outletId"} ${itemTotalPrice? "" : "itemTotalPrice"} ${productinfo? "" : "productinfo"} ${customerAuthUID? "" : "customerAuthUID"} ${transactionId? "" : "transactionId"} ${firstname? "" : "firstname"} ${phone? "" : "phone"}`
    });
  }

  try {
    // Create QR code payment
    const qrOptions = {
      type: "upi_qr",
      name: "MealPe Payment",
      usage: "single_use",
      fixed_amount: true,
      payment_amount: Math.round(itemTotalPrice * 100), // Convert to paise
      description: productinfo,
      close_by: Math.floor(Date.now() / 1000) + 1800, // QR code expires in 30 minutes
      notes: {
        transactionId: transactionId,
        outletId: outletId,
        customerId: customerAuthUID,
        customerName: firstname,
        customerPhone: phone,
        project:"mealpe"
      },
    };


    const qrCode = await razorpay.qrCode.create(qrOptions);

    // Update transaction with QR code details
    const { error: updateError } = await supabaseInstance
      .from("Transaction")
      .update({ razorpay_order_id: qrCode.id, razorpay_order_details: qrCode, status: "pending" })
      .eq("txnid", transactionId)
      .select("*")
      .maybeSingle();

    if (updateError) throw updateError;


    return res.status(200).json({
      success: true,
      message: "QR code generated successfully",
      qrCodeId: qrCode.id,
      qrCodeImage: qrCode.image_url,
      transactionId: transactionId,
      amount: qrCode.payment_amount / 100, // Convert back to rupees
      currency: "INR",
      expiresAt: qrCode.close_by
    });
  } catch (error) {
    console.error("Error in generate-qr-code-for-collection:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to generate QR code for collection",
      error: error.message
    });
  }
});

/**
 * @description Handles Razorpay webhook events for QR code payments.
 * @param {Object} event - The event object.
 * @param {Object} payload - The payload object.
 * @returns {Promise<Object>} Status of the payment
 */
router.post("/qr-payment-webhook", async (req, res) => {
  const { event, payload } = req.body;

  try {
    //get the project from the notes
    const project = payload?.payment?.entity?.notes?.project;
    // return console.log(project);
    if(!project || !payload?.payment?.entity?.notes?.project) {
      return res.status(200).json({
        success: false,
        message: "Not a qr code payment",
      });
    }else if (project !== "mealpe") {
      return res.status(200).json({
        success: true,
        message: "This is not a MealPe payment",
      });
    }

    // Verify webhook signature
    const crypto = require('crypto');
    const signature = req.headers['x-razorpay-signature'];
    const expectedSignature = crypto
      .createHmac('sha256', razorpayConfig.webhook_secret)
      .update(JSON.stringify(req.body))
      .digest('hex');

    if (signature !== expectedSignature) {
      return res.status(400).json({
        success: false,
        message: "Invalid webhook signature"
      });
    }

    // Handle payment.captured event for successful QR code payments
    if (event === 'payment.captured') {
      const { payment } = payload;
      console.log(`Payment captured event received with payload: ${JSON.stringify(payment, null, 2)}`);

      // Get transaction ID from payment notes
      const transactionId = payment.entity.notes?.transactionId;
      const payment_id = payment.entity.id;

      if (!transactionId) {
        return res.status(400).json({
          success: false,
          message: "Transaction ID not found in payment notes"
        });
      }

      // Get transaction details
      const { data: transactionData, error: transactionError } = await supabaseInstance
        .from("Transaction")
        .select("*")
        .eq("txnid", transactionId)
        .maybeSingle();

      if (transactionError) throw transactionError;

      if (!transactionData) {
        return res.status(404).json({
          success: false,
          message: "Transaction not found"
        });
      }

      // Update transaction status
      const { data: updatedTransaction, error: updateError } = await supabaseInstance
        .from("Transaction")
        .update({
          status: "success",
          razorpay_payment_id: payment_id
        })
        .eq("txnid", transactionId)
        .select("*, Outlet(bankLabel)")
        .maybeSingle();

      if (updateError) throw updateError;

      const outletBankLabel = updatedTransaction.Outlet.bankLabel;
      const outletVendorAmount = updatedTransaction.outletVendorAmount;

      if (outletBankLabel.startsWith("acc_")) {
        // create transfer to the outlet bank account from razorpay
        const transfer = await razorpay.payments.transfer(payment_id, {
          "transfers": [
            {
              "account": outletBankLabel,
              "amount": await getOutletVendorTransferAmount(outletVendorAmount), // convert to paise
              "currency": "INR",
              "notes": {
                "transactionId": updatedTransaction.txnid,
                "outletId": updatedTransaction.outletId,
                "customerId": updatedTransaction.customerAuthUID,
                "customerName": updatedTransaction.firstname,
                "project": "mealpe",
              },
            }
          ]
        });

        if (transfer.error) {
          return res.status(200).json({
            success: false,
            message: "Transfer failed",
            error: transfer.error
          });
        }
      }
      return res.status(200).json({
        success: true,
        message: "Payment processed successfully",
        transaction: updatedTransaction
      });
    }

    // Handle qr_code.closed event for expired QR codes
    if (event === 'qr_code.closed') {
      const { qr_code } = payload;
      const qr_id = qr_code.entity.id;
      console.log(`QR code closed event received with payload: ${JSON.stringify(qr_code, null, 2)}`);

      // Update transaction status for expired QR codes
      const { data: updatedTransaction, error: updateError } = await supabaseInstance
        .from("Transaction")
        .update({
          status: "expired"
        })
        .eq("razorpay_order_id", qr_id)
        .eq("status", "pending")
        .select("*")
        .maybeSingle();

      if (updateError) throw updateError;

      return res.status(200).json({
        success: true,
        message: "QR code expired",
        transaction: updatedTransaction
      });
    }

    return res.status(200).json({
      success: true,
      message: "Webhook received"
    });

  } catch (error) {
    console.error("Error in qr-payment-webhook:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to process webhook",
      error: error.message
    });
  }
});

/**
 * @description Check the status of a QR code payment
 * @param {string} transactionId - The transaction ID
 * @returns {Promise<Object>} Status of the payment
 */
router.get("/check-qr-payment-status/:transactionId", async (req, res) => {
  const { transactionId } = req.params;

  if (!transactionId) {
    return res.status(400).json({
      success: false,
      message: "Transaction ID is required"
    });
  }

  try {
    // Get transaction details
    const { data: transaction, error: transactionError } = await supabaseInstance
      .from("Transaction")
      .select("*")
      .eq("txnid", transactionId)
      .maybeSingle();

    if (transactionError) throw transactionError;

    if (!transaction) {
      return res.status(404).json({
        success: false,
        message: "Transaction not found"
      });
    }

    // Handle existing statuses immediately
    if (transaction.status === "success") {
      return res.status(200).json({
        success: true,
        status: "success",
        message: "Payment completed successfully",
        
      });
    }

    if (transaction.status === "expired") {
      return res.status(200).json({
        success: false,
        status: "expired",
        message: "QR code has expired",
        
      });
    }


    // Default response for pending transactions with no Razorpay order ID
    return res.status(200).json({
      success: false,
      status: "pending",
      message: "Payment pending",
      
    });

  } catch (error) {
    console.error("Error in check-qr-payment-status:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to check payment status",
      error: error.message
    });
  }
});

/**
 * @description Check the status of a QR code payment
 * @param {string} transactionId - The transaction ID
 * @returns {Promise<Object>} Status of the payment
 */
router.get("/check-qr-payment-status-for-collection/:transactionId", async (req, res) => {
  const { transactionId } = req.params;

  if (!transactionId) {
    return res.status(400).json({
      success: false,
      message: "Transaction ID is required"
    });
  }

  try {
    // Get transaction details
    const { data: transaction, error: transactionError } = await supabaseInstance
      .from("Transaction")
      .select("*")
      .eq("txnid", transactionId)
      .maybeSingle();

    if (transactionError) {
      console.error("Error in get-transaction-details:", transactionError);
      throw transactionError;
    }

    if (!transaction) {
      return res.status(404).json({
        success: false,
        message: "Transaction not found"
      });
    }

    // Handle existing statuses immediately
    if (transaction.status === "success") {
      // update the transaction status to success
      const { error: updateError } = await supabaseInstance
        .from("Transaction")
        .update({ paymentType: "online" })
        .eq("txnid", transactionId);

      if (updateError) {
        console.error("Error in update-transaction-record:", updateError);
        throw updateError;
      }

      //update the order record
      const { error: updateOrderError } = await supabaseInstance
        .from("Order")
        .update({ isCashOrder: false, paymentType: "online", orderStatusId: 10 })
        .eq("txnid", transactionId);

      if (updateOrderError) {
        console.error("Error in update-order-record:", updateOrderError);
        throw updateOrderError;
      }

      return res.status(200).json({
        success: true,
        status: "success",
        message: "Payment completed successfully",
        
      });
    }

    if (transaction.status === "expired") {
      return res.status(200).json({
        success: false,
        status: "expired",
        message: "QR code has expired",
        
      });
    }

    if (transaction.status === "pending") {
      return res.status(200).json({
        success: false,
        status: "pending",
        message: "Payment pending",
        
      });
    }

    // Default response for pending transactions with no Razorpay order ID
    return res.status(200).json({
      success: false,
      status: "pending",
      message: "Payment pending",
      
    });

  } catch (error) {
    console.error("Error in check-qr-payment-status-for-collection:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to check payment status",
      error: error.message
    });
  }
});

/**
 * @description Check payment for mess order for cash and qr code payments. If payment is successful, update the order status to 10 and insert the meal record.
 * @param {string} transactionId - The transaction ID
 * @query {string} mtid - The meal type ID
 * @returns {Promise<Object>} Status of the payment
 */
router.get("/payment-for-mess-order/:transactionId", async (req, res) => { 
  const { transactionId } = req.params;
  const mealTypeId = req.query.mtid;
  const paymentType = req.query.paymentType;

  if (!transactionId || !mealTypeId) {
    return res.status(400).json({
      success: false,
      message: "Transaction ID and mealTypeId are required"
    });
  }

  try {
    const served_date = new Date().toISOString().split("T")[0];
    const served_time = new Date().toISOString();
    
    //check if the order delivery date is same as served_date
    const { data: order, error: orderError } = await supabaseInstance
    .from("Order")
    .select("Order_Schedule(scheduleDate)")
    .eq("txnid", transactionId)
    .maybeSingle();
    
    if (orderError) throw orderError;
    
    console.log(`Served date: ${served_date}`);
    console.log(`order =>` , order);
    console.log(`Order delivery date: ${order.Order_Schedule[0].scheduleDate}`);

    if (order.Order_Schedule[0].scheduleDate !== served_date) {
      return res.status(200).json({
        success: false,
        message: "Order delivery date is not same as served date"
      });
    }
    
    // Get transaction details
    const { data: transaction, error: transactionError } = await supabaseInstance
    .from("Transaction")
    .select("*")
    .eq("txnid", transactionId)
    .maybeSingle();
    
    if (transactionError) throw transactionError;
    
    if (!transaction) {
      return res.status(404).json({
        success: false,
        message: "Transaction not found"
      });
    }
    
    const outletId = transaction.outletId;

    if (transaction.status === "success" || paymentType === "cash") {
      // Insert into Meals_Served if not already present
      try {

        const customerAuthUID = transaction.customerAuthUID;
        
        // Get menuId for mealTypeId, outletId, today
        const menuId = await getMenuId(mealTypeId, outletId);
     
        // Check if already served
        const { data: existingServed, error: existingError } = await supabaseInstance
          .from("Meals_Served")
          .select("*")
          .eq("customerAuthUID", customerAuthUID)
          .eq("served_date", served_date)
          .eq("menuId", menuId)
          .eq("outletId", outletId)
          .maybeSingle();
        
        if (existingError) {
          if(existingError.code == "PGRST116") {
            return res.status(200).json({
              success: false,
              status: "success",
              message: "Meal already served",
            });
          }
          throw existingError;
        };


        if (!existingServed) {
          // Insert new Meals_Served record
          const { data: servedData, error: insertError } = await supabaseInstance
            .from("Meals_Served")
            .insert({
              customerAuthUID,
              served_date,
              served_time,
              mealTypeId,
              menuId,
              outletId
            })
            .select("*")
            .maybeSingle();
          
          if (insertError) throw insertError;

          //update the order status to 10
          const { error: updateOrderError } = await supabaseInstance
            .from("Order")
            .update({ orderStatusId: 10, isCashOrder: paymentType == "cash" ? true : false, paymentType: paymentType })
            .eq("txnid", transactionId);
          
          if (updateOrderError) throw updateOrderError;

          // update the transaction paymentType to online
          const { error: updateTransactionError } = await supabaseInstance
            .from("Transaction")
            .update({
              paymentType,
              razorpay_payment_id: paymentType !== "cash" ? transaction.razorpay_payment_id : null,
              razorpay_signature: paymentType !== "cash" ? transaction.razorpay_signature : null,
              razorpay_order_id: paymentType !== "cash" ? transaction.razorpay_order_id : null,
              razorpay_order_details: paymentType !== "cash" ? transaction.razorpay_order_details : null,
              status: paymentType !== "cash" ? "success" : transaction.status
            })
            .eq("txnid", transactionId);
          
          if (updateTransactionError) throw updateTransactionError;
            
          
          return res.status(200).json({
            success: true,
            status: "success",
            servedData: servedData,
            message: "Payment completed successfully and meal served recorded if not already present"
          });
        }

        return res.status(200).json({
          success: false,
          message: "Meal already served"
        });
        
      } catch (mealError) {
        console.error("Error inserting Meals_Served:", mealError);
        // Don't fail the payment status response, just log
      }
    }

    if (transaction.status === "expired") {
      return res.status(200).json({
        success: false,
        status: "expired",
        message: "QR code has expired"
      });
    }

    if (transaction.status === "pending") {
      return res.status(200).json({
        success: false,
        status: "pending",
        message: "Payment pending"
      });
    }

    // Default response for pending transactions with no Razorpay order ID
    return res.status(200).json({
      success: false,
      status: "pending",
      message: "Payment pending"
    });
  } catch (error) {
    console.error("Error in check-qr-payment-status-for-mess:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to check payment status",
      error: error.message
    });
  }
});

/**
 * @description Take cash payment for mess order and serve the meal according our logic
 * @param {string} orderIds - The order IDs
 * @returns {Promise<Object>} Status of the payment
 */
router.post("/cash-payment-for-mess-order", async (req, res) => {
  const { orderIds } = req.body;

  if (!orderIds) {
    return res.status(400).json({
      success: false,
      message: "Order IDs are required"
    });
  }

  try {
    const { data: orders, error: ordersError } = await supabaseInstance
      .from("Order")
      .select("orderId, Order_Schedule(scheduleDate), txnid, isMessOrder, customerAuthUID, paymentType, outletId, Order_Item(Menu_Item(mealTypeId))")
      .in("orderId", orderIds);
    
    if (ordersError) throw ordersError;

    if (orders.length !== orderIds.length) {
      return res.status(200).json({
        success: false,
        message: "Some orders not found"
      });
    }
    
    // get the order objects
    const orderObjs = await Promise.all(orders.map(async (order) => {
      return {
        orderId: order.orderId,
        customerAuthUID: order.customerAuthUID,
        mealTypeId: order.Order_Item[0].Menu_Item.mealTypeId,
        outletId: order.outletId,
        txnid: order.txnid,
        scheduleDate: order.Order_Schedule[0].scheduleDate,
        menuId: await getMenuId(order.Order_Item[0].Menu_Item.mealTypeId, order.outletId)
      }
    }));

    const todayDate = new Date().toISOString().split("T")[0];

    if (!orderObjs.every(order => order.scheduleDate === todayDate)) {
      return res.status(200).json({
        success: false,
        message: "Some orders have different scheduled dates than today"
      });
    }

    // update the transaction status to success and paymentType to cash
    const { error: updateTransactionError } = await supabaseInstance
      .from("Transaction")
      .update({ status: "success", paymentType: "cash" })
      .in("txnid", orderObjs.map(order => order.txnid));
    
    if (updateTransactionError) {
      console.error("Error in update-transaction-record:", updateTransactionError);
      throw updateTransactionError;
    };

    // serve the meal
    const insertData = await Promise.all(orderObjs.map(async (order) => {
      return {
        customerAuthUID: order.customerAuthUID,
        served_date: todayDate,
        served_time: new Date().toISOString(),
        mealTypeId: order.mealTypeId,
        menuId: order.menuId,
        outletId: order.outletId
      }
    }));

    const { error: insertMealError } = await supabaseInstance
      .from("Meals_Served")
      .upsert(insertData, { onConflict: "customerAuthUID,served_date,menuId" })
      .select("*");
    
    if (insertMealError) {
      console.error("Error in insert-meal-record:", insertMealError);
      throw insertMealError;
    }

    // update the order status to 10, isCashOrder to true, paymentType to cash
    const { error: updateOrderError } = await supabaseInstance
      .from("Order")
      .update({ orderStatusId: 10, isCashOrder: true, paymentType: "cash" })
      .in("orderId", orderObjs.map(order => order.orderId));
    
    if (updateOrderError) {
      console.error("Error in update-order-record:", updateOrderError);
      throw updateOrderError;
    }

    return res.status(200).json({
      success: true,
      message: "Cash payment taken successfully"
    });
  } catch (error) {
    console.error("Error in cash-payment-for-mess-order:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to take cash payment for mess order",
      error: error.message
    });
  }
});


//====================== /QR code payment ======================================


module.exports = router;
module.exports.getPriceBreakdown = getPriceBreakdown;
