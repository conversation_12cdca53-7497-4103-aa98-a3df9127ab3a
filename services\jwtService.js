const jwt = require('jsonwebtoken');
require('dotenv').config();
const SECRET = process.env.PASSWORD_RESET_JWT_SECRET || 'supersecret';

/**
 * Sign a password reset token
 * @param {object} payload - Should include outletId, email, tokenId, etc.
 * @param {string} expiresIn - Expiry string (e.g., '10m')
 * @returns {string} JWT
 */
function signPasswordResetToken(payload, expiresIn = '10m') {
  return jwt.sign(payload, SECRET, { expiresIn });
}

/**
 * Verify a password reset token
 * @param {string} token
 * @returns {object} Decoded payload if valid, throws if invalid
 */
function verifyPasswordResetToken(token) {
  return jwt.verify(token, SECRET);
}

module.exports = {
  signPasswordResetToken,
  verifyPasswordResetToken,
}; 