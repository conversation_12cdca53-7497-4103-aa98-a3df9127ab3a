var express = require("express");
var router = express.Router();
const multer = require("multer");
const upload = multer();
var msg91config = require("../configs/msg91Config");
const axios = require("axios");
const {isWhiteListed} = require("./mess/whitelistMiddleWare");
// const moment = require("../services/momentService").momentIndianTimeZone;
const moment = require("moment-timezone");
const {
  sendMobileOtp,
  verifyMobileOtp,
  sendEmail,
  sendMobileSMS,
  generateOTP,
} = require("../services/msf91Service");
const { isTimeInRange } = require("../services/dateTimeService");
const { requestRefund } = require("./Payment/refund");
const {
  saveOrderToPetpooja,
  updateOrderStatus,
} = require("./petpooja/pushMenu");

const {getOutletUserFcmTokens,sendOutletUserNotifications} = require("./firebase");

require("dotenv").config();

var cryptoJs = require("crypto-js");

const otplessConfig = require("../configs/otplessConfig");
var { customerSlectString } = require("../services/supabaseCommonValues").value;
var supabaseInstance = require("../services/supabaseClient").supabase;

const bypassNumbers = [6666666666, ************, 9999999999, ************, 8530088085, ************, 9904029899, ************];


router.get("/", function (req, res, next) {
  res.send({ success: true, message: "respond send from customer.js" });
});

router.post("/signUp", async (req, res) => {
  const { email, mobile, name, campusId, organizationCode } = req.body;

  try {
    // check if the user already exists
    const { data: existingUser, error: existingUserError } =
      await supabaseInstance
        .from("Customer")
        .select("*")
        .eq("email", email)
        .eq("isDelete", false);

    if (existingUserError && (existingUserError.code !== "42P01" || existingUserError.code !== "PGRST116")) {
      throw existingUserError;
    }

    const {data : existingUserMobile, error: existingUserMobileError} = await supabaseInstance
      .from("Customer")
      .select("*")
      .eq("mobile", mobile)
      .eq("isDelete", false);

    if (existingUserMobileError && (existingUserMobileError.code !== "42P01" || existingUserMobileError.code !== "PGRST116")) {
      throw existingUserMobileError
    }

    // console.log('existingUser', existingUser);
    
    if (existingUser.length > 0 || existingUserMobile.length > 0) {
      return res.status(200).json({
        success: false,
        message: `User already exists with ${existingUser.length > 0 ? 'email' : 'mobile'}`,
        existingUser: existingUser,
      });
    }

    // check if the organization code is valid
    if (organizationCode) {
      const { data: organizationData, error: organizationError } = await supabaseInstance
        .from("Organization")
        .select("*")
        .eq("organizationCode", organizationCode)
        .maybeSingle();

      if (organizationError) throw organizationError;

      if (!organizationData) {
        return res.status(401).json({
          success:false,
          message:"Invalid organization code."
        });
      }
    }
    const { data, error } = await supabaseInstance.auth.admin.createUser({
      email,
      phone: mobile,
      phone_confirm: true,
    });

    if (data?.user) {
      const customerAuthUID = data.user.id;
      const customerResponse = await supabaseInstance
        .from("Customer")
        .insert({
          email,
          mobile,
          customerName: name,
          customerAuthUID,
          campusId,
        })
        .select("*")
        .maybeSingle();
      if (customerResponse.data) {
        // add the organization code to the customerResponse.data if present
        if (organizationCode) {
          customerResponse.data.organizationCode = organizationCode;
        }
        res.status(200).json({
          success: true,
          message: "SignUp Successfully",
          data: customerResponse.data,
        });
      } else {
        throw customerResponse.error;
      }
    } else {
      throw error;
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error?.message || error });
  }
});

// router.post("/signUp", async (req, res) => {
//   const { email, mobile, name } = req.body;

//   try {
//     const { data, error } = await supabaseInstance.from("Customer").insert({ email, mobile, customerName: name  }).select("*").maybeSingle()

//     if (data) {
//         res.status(200).json({
//           success: true,
//           message: "SignUp Successfully",
//           data:data
//         });
//       } else {
//         throw error;
//       }
//   } catch (error) {
//     res.status(500).json({ success: false, error: error?.message || error });
//   }
// });

router.post("/sendMobileOTP", async (req, res) => {
  //* if mobile => required[mobile];

  const { mobile, name } = req.body;
  try {
    if (bypassNumbers.includes(mobile)) {
      return res.status(200).json({
        success: true,
        data: { message: "Bypass user" },
      });
    }
    sendMobileOtp(mobile, msg91config.config.otp_template_id, name)
      .then((responseData) => {
        console.log(".then block ran: ", responseData);
        res.status(200).json({
          success: true,
          data: responseData,
        });
      })
      .catch((err) => {
        console.log(".catch block ran: ", err);
        throw err;
      });
  } catch (error) {
    console.log(error);
    res.status(500).json({ success: false, error: error.message });
  }
});

router.post("/verifyMobileOTP", async (req, res) => {
  //* if mobile => required[mobile, otp];
  //* if email  => required[email, token];
  const { mobile, otp, email, token } = req.body;
  try {
    if (bypassNumbers.includes(mobile) || process.env?.IS_SANDBOX == true) {
      return res.status(200).json({
        success: true,
        data: { message: "Bypass user" },
      });
    }

    verifyMobileOtp(mobile, otp)
      .then((responseData) => {
        console.log(".then block ran: ", responseData);
        if (responseData?.api_success) {
          res.status(200).json({
            success: true,
            data: responseData,
          });
        } else {
          throw responseData;
        }
      })
      .catch((err) => {
        console.log(".catch block ran: ", err);
        res.status(500).json({ success: false, error: err });
      });

  } catch (error) {
    console.log(error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// router.post("/sendEmailOTP", async (req, res) => {
//   //* if email => required[email];

//   const { email_to, email_cc, email_bcc } = req.body;
//   try {
//     const token_otp = generateOTP();

//     //* email_to ex. => [{name: 'Recipient1 NAME', email: 'Recipient1 email'}]
//     //* email_cc ex. => [{name: 'Recipient2 NAME', email: 'Recipient2 email'}]
//     //* email_bcc ex. => [{name: 'Recipient3 NAME', email: 'Recipient3 email'}]
//     //* emailVariables ex. => {name: 'Name 1'}
//     //* template_id (string)

//     const _email_to = [{ name: 'Customer', email: email_to }];
//     const _email_cc = []
//     const _email_bcc = []
//     const _template_id = msg91config.config.email_otp_template_id

//     sendEmail(_email_to, _email_cc, _email_bcc, {}, _template_id).then((responseData) => {
//       const hash = cryptoJs.AES.encrypt(token_otp.toString(), "MealPE-OTP").toString();
//       if (responseData?.api_success) {
//         res.status(200).json({
//           success: true,
//           data: responseData,
//           token: hash
//         });
//       } else {
//         throw responseData;
//       }
//     }).catch(err => {
//       //  console.log('.catch block ran: ', err);
//       res.status(500).json({ success: false, error: err });
//     });
//   } catch (error) {
//     // console.log(error)
//     res.status(500).json({ success: false, error: error.message });
//   }
// });

// router.post("/verifyEmailOTP", async (req, res) => {
//   const { otp, token } = req.body;
//   try {
//     const tokenData = cryptoJs.AES.decrypt(token, "MealPE-OTP").toString(cryptoJs.enc.Utf8);
//     if (tokenData === otp) {
//       res.status(200).json({
//         success: true,
//         message: "OTP Verify",
//       });
//     } else {
//       throw error;
//     }
//   } catch (error) {
//     res.status(500).json({ success: false, error: error });
//   }
// });

// router.post("/sendMobileSMS", async (req, res) => {

//   const { mobile, template_id } = req.body;
//   try {
//     sendMobileSMS(mobile, template_id).then((responseData) => {
//       console.log('.then block ran: ', responseData);
//       res.status(200).json({
//         success: true,
//         data: responseData,
//       });
//     }).catch(err => {
//       console.log('.catch block ran: ', err);
//       throw err;
//     });
//   } catch (error) {
//     console.log(error)
//     res.status(500).json({ success: false, error: error.message });
//   }
// });

// router.post("/verifyEmailOTP", async (req, res) => {
//   //* if email  => required[email, token];
//   const { otp, token } = req.body;
//   try {
//       verifyMobileOtp( otp,token).then((responseData) => {
//         console.log('.then block ran: ', responseData);
//         res.status(200).json({
//           success: true,
//           data: responseData,
//         });
//       }).catch(err => {
//         console.log('.catch block ran: ', err);
//         throw err;
//       });
//   } catch (error) {
//     res.status(500).json({ success: false, error: error.message });
//   }
// });

router.post("/userlogin", async (req, res) => {
  const { mobile, email, organizationCode } = req.body;
  try {
    let loginQuery;
    if (mobile) {
      loginQuery = supabaseInstance
        .from("Customer")
        .select(customerSlectString)
        .eq("mobile", mobile)
        .eq("isDelete", false);
    } else if (email) {
      loginQuery = supabaseInstance
        .from("Customer")
        .select(customerSlectString)
        .eq("email", email)
        .eq("isDelete", false);
    }

    let userData = await loginQuery.maybeSingle();
    if (userData?.data) {
      if(organizationCode){
        // check if the organization code is valid
        const { data: organizationData, error: organizationError } = await supabaseInstance
          .from("Organization")
          .select("*")
          .eq("organizationCode", organizationCode)
          .maybeSingle();

        if (organizationError) throw organizationError;

        if (!organizationData) {
          return res.status(401).json({
            success:false,
            message:"Invalid organization code."
          })
        }
        userData.data.organizationCode = organizationData?.organizationCode;
      }
      res.send({
        success: true,
        message: "Login successfully",
        data: userData.data,
      });
    } else if (!userData.data && !userData.error) {
      const err = {
        message: "User not found.",
      };
      throw err;
    } else {
      throw userData.error;
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});


router.post("/getUpdatedProfile", async (req, res) => {
  const { mobile, email, organizationCode } = req.body;
  try {
    let loginQuery;
    if (mobile) {
      loginQuery = supabaseInstance
        .from("Customer")
        .select(customerSlectString)
        .eq("mobile", mobile)
        .eq("isDelete", false);
    } else if (email) {
      loginQuery = supabaseInstance
        .from("Customer")
        .select(customerSlectString)
        .eq("email", email)
        .eq("isDelete", false);
    }

    let userData = await loginQuery.maybeSingle();
    if (userData?.data) {
      if(organizationCode){
        // check if the organization code is valid
        const { data: organizationData, error: organizationError } = await supabaseInstance
          .from("Organization")
          .select("*")
          .eq("organizationCode", organizationCode)
          .maybeSingle();

        if (organizationError) throw organizationError;

        if (!organizationData) {
          return res.status(401).json({
            success:false,
            message:"Invalid organization code."
          })
        }
        userData.data.organizationCode = organizationData?.organizationCode;
      }
      res.send({
        success: true,
        message: "Updated Profile fetched successfully",
        data: userData.data,
      });
    } else if (!userData.data && !userData.error) {
      const err = {
        message: "User not found.",
      };
      throw err;
    } else {
      throw userData.error;
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// updated api with cash and carry orders {prod} 2024-10-29
router.get("/cafeteriaDetails/:outletId/:customerAuthUID", async (req, res) => {
  const startTime = process.hrtime();
  
  try {
    const { outletId, customerAuthUID } = req.params;
    
    // Validate required parameters
    if (!outletId) {
      return res.status(400).json({ 
        success: false, 
        message: "outletId is required" 
      });
    }

    // Get today's date in YYYY-MM-DD format for mess orders
    const today = moment().tz("Asia/Kolkata").format("YYYY-MM-DD");

    // Fetch mess orders for today
    let { data: messOrders, error: messError } = await supabaseInstance
      .from("Order")
      .select(`
        orderId,
        Order_Item!inner(
          itemId,
          Menu_Item!inner(itemid, isMessItem)
        ),
        Order_Schedule!inner(scheduleDate)
      `)
      .eq("outletId", outletId)
      .eq("isMessOrder", true)
      .eq("Order_Schedule.scheduleDate", today)
      .eq("customerAuthUID", customerAuthUID)
      .not("orderStatusId", 'in', '(-2, -1)');

    if (messError) {
      throw new Error(`Failed to fetch mess orders: ${messError}`);
    }

    // Extract purchased mess item IDs
    const purchasedMessItemIds = messOrders 
      ? messOrders.flatMap(order => 
          order.Order_Item
            .filter(item => item.Menu_Item.isMessItem)
            .map(item => item.Menu_Item.itemid)
        )
      : [];

    // Build query string based on customerAuthUID
    let queryString = "*, item_categoryid(*, parent_category_id(*))";
    if (customerAuthUID !== "null") {
      queryString += ", FavoriteMenuItem!left(*)";
    }

    // Fetch menu items
    let query = supabaseInstance
      .from("Menu_Item")
      .select(queryString)
      .eq("isDelete", false)
      .eq("outletId", outletId);

    if (customerAuthUID !== "null") {
      query = query.eq("FavoriteMenuItem.customerAuthUID", customerAuthUID);
    }

    let { data: menuItems, error: menuError } = await query;

    if (menuError) {
      throw new Error(`Failed to fetch menu items: ${menuError}`);
    }

    // Fetch outlet data with related information
    let { data: outletData, error: outletError } = await supabaseInstance
      .from("Outlet")
      .select("*,Menu_Categories(*),isOutletOpen,Timing!left(*, dayId(*))")
      .eq("outletId", outletId)
      .maybeSingle();

    if (outletError) {
      throw new Error(`Failed to fetch outlet data: ${outletError}`);
    }

    // Filter and process mess items
    let messItems = menuItems.filter((m) => m.isMessItem);
    
    // Add mealTypeId to mess items
    messItems = messItems.map(m => {
      const mealTypes = {
        "Breakfast": 1,
        "Lunch": 2,
        "High Tea": 3,
        "Dinner": 4
      };
      return {
        ...m,
        mealTypeId: mealTypes[m.itemname] || 0
      };
    });

    // Fetch meal timings
    if(!outletData.messId){
      const {data, error} = await supabaseInstance
        .from("Outlet")
        .select("outletId")
        .eq("outletId", outletId)
        .single();
      if(error) throw error

      outletData.messId = data.outletId;

      if(!data) {
        return res.status(400).send({ success: false, message: "No Mess found with this name." });
      }
    }
    if(outletData?.messId){
    const { data: mealTimings, error: mealTimingsError } = await supabaseInstance
      .from("Meal_Timings")
      .select("*")
      .eq("outletId", outletData?.messId);

    if (mealTimingsError) {
      throw new Error(`Failed to fetch meal timings: ${mealTimingsError}`);
    }

    // Process mess items with time checks
    messItems = messItems.map(m => {
      const mealTiming = mealTimings.find(t => t.mealTypeId === m.mealTypeId);

      const isMealEnabled = mealTimings.find(t => t.mealTypeId === m.mealTypeId);
      if (isMealEnabled.enabled == false) {
        m.isMealEnabled = false;
      } else {
        m.isMealEnabled = true;
      }
      if (mealTiming) {
        const currentTime = new Date();
        const endTime = new Date(mealTiming.endTime);
        
        // Convert both times to minutes since midnight UTC for comparison
        const currentMinutes = currentTime.getUTCHours() * 60 + currentTime.getUTCMinutes();
        const endMinutes = endTime.getUTCHours() * 60 + endTime.getUTCMinutes();
        
        if (currentMinutes > endMinutes) {
          m.disabledDueToTime = true;
        }
      }
      return m;
    });

    // Update menu items with processed mess items
    menuItems = menuItems.map(m => {
      if (m.isMessItem) {
        const updatedMessItem = messItems.find(item => item.itemid === m.itemid);
        if(updatedMessItem){
          // check for isEnable flag
          if(updatedMessItem.isMealEnabled == false){
            return;
          }
        }
        return {
          ...updatedMessItem,
          isFavoriteMenuItem: m.FavoriteMenuItem?.length > 0,
          disabled: purchasedMessItemIds.includes(m.itemid)
        };
      }
      return {
        ...m,
        isFavoriteMenuItem: m.FavoriteMenuItem?.length > 0,
        disabled: purchasedMessItemIds.includes(m.itemid)
      };
    });

    }

    // if outlet is mess then sort the menu items according to meal type
    if(outletData?.hasMess){
      menuItems = menuItems.sort((a, b) => {
        const mealTypes = {
          "Breakfast": 1,
          "Lunch": 2,
          "High Tea": 3,
          "Dinner": 4
        };
        return mealTypes[a.itemname] - mealTypes[b.itemname];
      });
    }
    // Process outlet details with timing information
    let outletDetails = {};
    if (outletData) {
      // Get current day and next two days
      const currentDay = moment().tz("Asia/Kolkata");
      const tomorrow = moment().tz("Asia/Kolkata").add(1, "days");
      const overmorrow = moment().tz("Asia/Kolkata").add(2, "days");

      // Format timing data
      outletDetails = {
        ...outletData,
        Timing: {
          Today: outletData.Timing?.find(t => t.dayId?.day === currentDay.format("dddd")),
          Tomorrow: outletData.Timing?.find(t => t.dayId?.day === tomorrow.format("dddd")),
          Overmorrow: outletData.Timing?.find(t => t.dayId?.day === overmorrow.format("dddd"))
        }
      };

      // Process outlet open status for each day
      ["Today", "Tomorrow", "Overmorrow"].forEach((day, index) => {
        const timing = outletDetails.Timing[day];
        let isOpen = false;

        if (timing?.openTime && timing?.closeTime) {
          const currentTime = moment().tz("Asia/Kolkata");
          const openTime = moment.tz(timing.openTime, "HH:mm:ss", "Asia/Kolkata");
          const closeTime = moment.tz(timing.closeTime, "HH:mm:ss", "Asia/Kolkata");

          if (day === "Today") {
            isOpen = outletDetails.isOutletOpen;
          } else {
            isOpen = currentTime.isBetween(openTime, closeTime);
          }
        }

        outletDetails[`${day.toLowerCase()}isOutletOpen`] = isOpen;
      });
    }

    // Fetch tax details
    const { data: taxDetails, error: taxError } = await supabaseInstance
      .from("Tax")
      .select("*")
      .eq("outletId", outletId);

    if (taxError) {
      throw new Error(`Failed to fetch tax details: ${taxError}`);
    }

    // Calculate execution time
    const executionTime = process.hrtime(startTime);
    const executionTimeMs = (executionTime[0] * 1000 + executionTime[1] / 1000000).toFixed(2);

    //remove null values from menuItems
    menuItems = menuItems.filter(m => m);

    // Send successful response
    return res.status(200).json({
      success: true,
      message: "Data fetched successfully",
      executionTime: `${executionTimeMs}ms`,
      data: {
        outdetails: outletDetails,
        menuItems: menuItems,
        taxdetails: taxDetails?.data
      }
    });

  } catch (error) {
    console.error("Cafeteria Details API Error:", error);

    // Send error response
    return res.status(500).json({
      success: false,
      message: "Internal server error",
      error
    });
  }
});

router.get("/cafeteriaDetailsForGuest/:outletId", async (req, res) => {
  const startTime = process.hrtime();

  try {
    const { outletId } = req.params;

    // Validate required parameters
    if (!outletId) {
      return res.status(400).json({
        success: false,
        message: "outletId is required"
      });
    }

    // Get today's date in YYYY-MM-DD format for mess orders
    const today = moment().tz("Asia/Kolkata").format("YYYY-MM-DD");

    

    // Build query string based on customerAuthUID
    let queryString = "*, item_categoryid(*, parent_category_id(*))";

    // Fetch menu items
    let query = supabaseInstance
      .from("Menu_Item")
      .select(queryString)
      .eq("isDelete", false)
      .eq("outletId", outletId);


    let { data: menuItems, error: menuError } = await query;

    if (menuError) {
      throw new Error(`Failed to fetch menu items: ${menuError}`);
    }

    // Fetch outlet data with related information
    let { data: outletData, error: outletError } = await supabaseInstance
      .from("Outlet")
      .select("*,Menu_Categories(*),isOutletOpen,Timing!left(*, dayId(*))")
      .eq("outletId", outletId)
      .maybeSingle();

    if (outletError) {
      throw new Error(`Failed to fetch outlet data: ${outletError}`);
    }

    

    // Fetch meal timings
    if (!outletData.messId) {
      const { data, error } = await supabaseInstance
        .from("Outlet")
        .select("outletId")
        .eq("outletId", outletId)
        .single();
      if (error) throw error


      if (!data) {
        return res.status(400).send({ success: false, message: "No Mess found with this name." });
      }
    }
    if (outletData?.messId) {
      const { data: mealTimings, error: mealTimingsError } = await supabaseInstance
        .from("Meal_Timings")
        .select("*")
        .eq("outletId", outletData?.messId);

      if (mealTimingsError) {
        throw new Error(`Failed to fetch meal timings: ${mealTimingsError}`);
      }

    }

    // Process outlet details with timing information
    let outletDetails = {};
    if (outletData) {
      // Get current day and next two days
      const currentDay = moment().tz("Asia/Kolkata");
      const tomorrow = moment().tz("Asia/Kolkata").add(1, "days");
      const overmorrow = moment().tz("Asia/Kolkata").add(2, "days");

      // Format timing data
      outletDetails = {
        ...outletData,
        Timing: {
          Today: outletData.Timing?.find(t => t.dayId?.day === currentDay.format("dddd")),
          Tomorrow: outletData.Timing?.find(t => t.dayId?.day === tomorrow.format("dddd")),
          Overmorrow: outletData.Timing?.find(t => t.dayId?.day === overmorrow.format("dddd"))
        }
      };

      // Process outlet open status for each day
      ["Today", "Tomorrow", "Overmorrow"].forEach((day, index) => {
        const timing = outletDetails.Timing[day];
        let isOpen = false;

        if (timing?.openTime && timing?.closeTime) {
          const currentTime = moment().tz("Asia/Kolkata");
          const openTime = moment.tz(timing.openTime, "HH:mm:ss", "Asia/Kolkata");
          const closeTime = moment.tz(timing.closeTime, "HH:mm:ss", "Asia/Kolkata");

          if (day === "Today") {
            isOpen = outletDetails.isOutletOpen;
          } else {
            isOpen = currentTime.isBetween(openTime, closeTime);
          }
        }

        outletDetails[`${day.toLowerCase()}isOutletOpen`] = isOpen;
      });
    }

    // Fetch tax details
    const { data: taxDetails, error: taxError } = await supabaseInstance
      .from("Tax")
      .select("*")
      .eq("outletId", outletId);

    if (taxError) {
      throw new Error(`Failed to fetch tax details: ${taxError}`);
    }

    // Calculate execution time
    const executionTime = process.hrtime(startTime);
    const executionTimeMs = (executionTime[0] * 1000 + executionTime[1] / 1000000).toFixed(2);

    //remove null values from menuItems
    menuItems = menuItems.filter(m => m);

    // Send successful response
    return res.status(200).json({
      success: true,
      message: "Data fetched successfully",
      executionTime: `${executionTimeMs}ms`,
      data: {
        outdetails: outletDetails,
        menuItems: menuItems,
        taxdetails: taxDetails?.data
      }
    });

  } catch (error) {
    console.error("Cafeteria Details API Error:", error);

    // Send error response
    return res.status(500).json({
      success: false,
      message: "Internal server error",
      error
    });
  }
});

/**without cash and carry orders : 2024-10-29
router.get("/cafeteriaDetails/:outletId/:customerAuthUID", async (req, res) => {
  const { outletId, customerAuthUID } = req.params;
  try {
    let queryString = "*, item_categoryid(*, parent_category_id(*))";

    if (customerAuthUID != "null") queryString += ", FavoriteMenuItem!left(*)";
    let query = supabaseInstance
      .from("Menu_Item")
      .select(queryString)
      .eq("isDelete", false)
      .eq("outletId", outletId);

    if (customerAuthUID != "null") {
      query = query.eq("FavoriteMenuItem.customerAuthUID", customerAuthUID);
    }

    const { data, error } = await query;

    if (data) {
      const outletData = await supabaseInstance
        .from("Outlet")
        .select("*,Menu_Categories(*),isOutletOpen,Timing!left(*, dayId(*))")
        .eq("outletId", outletId)
        .maybeSingle();

      let outletdetails = {};

      if (outletData?.data) {
        outletdetails = {
          ...outletData.data,
          Timing: {
            Today: outletData.data?.Timing?.find(
              (f) => f.dayId?.day === moment().tz("Asia/Kolkata").format("dddd")
            ),
            Tomorrow: outletData.data?.Timing?.find(
              (f) =>
                f.dayId?.day ===
                moment().tz("Asia/Kolkata").add(1, "days").format("dddd")
            ),
            Overmorrow: outletData.data?.Timing?.find(
              (f) =>
                f.dayId?.day ===
                moment().tz("Asia/Kolkata").add(2, "days").format("dddd")
            ),
          },
        };

        let todayflag = false;
        let tomorrowflag = false;
        let Overmorrowflag = false;
        if (
          outletdetails?.Timing?.Today?.openTime &&
          outletdetails?.Timing?.Today?.closeTime
        ) {
          const time = moment().tz("Asia/Kolkata");
          const beforeTime = moment.tz(
            outletdetails?.Timing?.Today?.openTime,
            "HH:mm:ss",
            "Asia/Kolkata"
          );
          const afterTime = moment.tz(
            outletdetails?.Timing?.Today?.closeTime,
            "HH:mm:ss",
            "Asia/Kolkata"
          );

          // todayflag = isTimeInRange(time,beforeTime, afterTime);
          todayflag = outletdetails?.isOutletOpen;
        }

        // if (!todayflag && outletdetails.isTimeExtended) {
        //   todayflag = true;
        // }

        if (
          outletdetails?.Timing?.Tomorrow?.openTime &&
          outletdetails?.Timing?.Tomorrow?.closeTime
        ) {
          const time = moment().tz("Asia/Kolkata");
          const beforeTime = moment.tz(
            outletdetails?.Timing?.Tomorrow?.openTime,
            "HH:mm:ss",
            "Asia/Kolkata"
          );
          const afterTime = moment.tz(
            outletdetails?.Timing?.Tomorrow?.closeTime,
            "HH:mm:ss",
            "Asia/Kolkata"
          );

          tomorrowflag = isTimeInRange(time, beforeTime, afterTime);
        }

        // if (!tomorrowflag && outletdetails.isTimeExtended) {
        //   tomorrowflag = true;
        // }

        if (
          outletdetails?.Timing?.Overmorrow?.openTime &&
          outletdetails?.Timing?.Overmorrow?.closeTime
        ) {
          const time = moment().tz("Asia/Kolkata");
          const beforeTime = moment.tz(
            outletdetails?.Timing?.Overmorrow?.openTime,
            "HH:mm:ss",
            "Asia/Kolkata"
          );
          const afterTime = moment.tz(
            outletdetails?.Timing?.Overmorrow?.closeTime,
            "HH:mm:ss",
            "Asia/Kolkata"
          );

          Overmorrowflag = isTimeInRange(time, beforeTime, afterTime);
        }

        // if (!Overmorrowflag && outletdetails.isTimeExtended) {
        //   Overmorrowflag = true;
        // }

        outletdetails.todayisOutletOpen = todayflag;
        outletdetails.tomorrowisOutletOpen = tomorrowflag;
        outletdetails.OvermorrowisOutletOpen = Overmorrowflag;
      }

      const taxdetails = await supabaseInstance
        .from("Tax")
        .select("*")
        .eq("outletId", outletId);

      res.status(200).json({
        success: true,
        message: "Data fetch succesfully",
        data: {
          outdetails: outletdetails,
          menuItems: data.map((m) => ({
            ...m,
            isFavoriteMenuItem: m.FavoriteMenuItem?.length > 0,
          })),
          taxdetails: taxdetails.data,
        },
      });
    } else {
      throw error;
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

 */
// router.get("/homeData", async (req, res) => {
//   const { categoryId, campusId } = req.query;
//   try {
//     const cafeteriasForYouDataResponse = await supabaseInstance.from("Outlet").select("outletName,isPublished, address,logo,headerImage,outletId,isActive,isDineIn,isPickUp,isDelivery,packaging_charge, isTimeExtended, Timing!left(*,dayId(*)),Review!left(customerAuthUID,star)")
//       // .eq("Timing.dayId.day", moment().tz("Asia/Kolkata").format("dddd"))
//       .eq("campusId", campusId).eq("isPublished", true).eq("isActive", true).limit(5);

//     let PopularCafeteriasResponse = await supabaseInstance.from("Outlet").select("outletName, isPublished, address,logo,headerImage,outletId,isActive,isDineIn,isPickUp,isDelivery,packaging_charge, isTimeExtended, Timing!left(*, dayId(*)),Review!left(customerAuthUID,star)")
//       // .eq("Timing.dayId.day", moment().tz("Asia/Kolkata").format("dddd"))
//       .eq("campusId", campusId).eq("isPublished", true).eq("isActive", true).limit(5);

//     if (cafeteriasForYouDataResponse.data && PopularCafeteriasResponse.data) {

//       let cafeteriasForYouData = cafeteriasForYouDataResponse.data.map(m => ({
//         ...m, Timing: {
//           Today: m?.Timing?.find(f => f?.dayId?.day === moment().tz("Asia/Kolkata").format("dddd")),
//           Tomorrow: m?.Timing?.find(f => f?.dayId?.day === moment().tz("Asia/Kolkata").add(1, 'days').format("dddd")),
//           Overmorrow: m?.Timing?.find(f => f?.dayId?.day === moment().tz("Asia/Kolkata").add(2, 'days').format("dddd"))
//         }, Review: {
//           total_Customer: m?.Review?.length || 0,
//           avrage_Rating: (m?.Review.reduce((a, c) => a + c.star, 0) / m?.Review?.length)?.toFixed(1)
//         }
//       })).map(m => {
//         let flag = false;
//         if (m?.Timing?.Today?.openTime && m?.Timing?.Today?.closeTime) {
//           const time = moment().tz("Asia/Kolkata");
//           const beforeTime = moment(m?.Timing?.Today?.openTime, 'HH:mm:ss');
//           const afterTime = moment(m?.Timing?.Today?.closeTime, 'HH:mm:ss');

//           flag = time.isBetween(beforeTime, afterTime);
//         }

//         if (!flag && m.isTimeExtended) {
//           flag = true;
//         }
//         return {
//           ...m,
//           isOutletOpen: flag
//         }
//       })
//       let PopularCafeterias = PopularCafeteriasResponse.data.map(m => ({
//         ...m, Timing: {
//           Today: m?.Timing?.find(f => f?.dayId?.day === moment().tz("Asia/Kolkata").format("dddd")),
//           Tomorrow: m?.Timing?.find(f => f?.dayId?.day === moment().tz("Asia/Kolkata").add(1, 'days').format("dddd")),
//           Overmorrow: m?.Timing?.find(f => f?.dayId?.day === moment().tz("Asia/Kolkata").add(2, 'days').format("dddd"))
//         }, Review: {
//           total_Customer: m?.Review?.length || 0,
//           avrage_Rating: (m?.Review.reduce((a, c) => a + c.star, 0) / m?.Review?.length)?.toFixed(1)
//         }
//       })).map(m => {
//         let flag = false;
//         if (m?.Timing?.Today?.openTime && m?.Timing?.Today?.closeTime) {
//           const time = moment().tz("Asia/Kolkata");
//           const beforeTime = moment(m?.Timing?.Today?.openTime, 'HH:mm:ss');
//           const afterTime = moment(m?.Timing?.Today?.closeTime, 'HH:mm:ss');

//           flag = time.isBetween(beforeTime, afterTime);
//         }
//         if (!flag && m.isTimeExtended) {
//           flag = true;
//         }
//         return {
//           ...m,
//           isOutletOpen: flag
//         }
//       })

//       res.status(200).json({
//         success: true,
//         message: "Data fetch succesfully",
//         data: {
//           cafeteriasForYouData,
//           PopularCafeterias
//         }
//       });
//     } else {
//       throw PopularCafeterias.error || cafeteriasForYouDataResponse.error;
//     }
//   } catch (error) {
//     console.log(error);
//     res.status(500).json({ success: false, error: error });
//   }
// })

// working
// router.get("/homeData", async (req, res) => {
//   const { campusId } = req.query;
//   try {
//     let today = moment().tz("Asia/Kolkata").format("dddd");
//     let tomorrow = moment().tz("Asia/Kolkata").add(1, "days").format("dddd");
//     let overmorrow = moment().tz("Asia/Kolkata").add(2, "days").format("dddd");
//     let query = supabaseInstance
//       .rpc("get_customer_home_data_test", {
//         campus_id: campusId,
//         today,
//         tomorrow,
//         overmorrow,
//       })
//       .limit(5);

//     const get_customer_home_dataResponse = await query;

//     if (get_customer_home_dataResponse.data) {
//       let home_data = get_customer_home_dataResponse?.data?.map( (m) => {
//         let flag = false;
//         const today_time = m?.time_day?.find((f) => f.Day === today);
//         if (today_time?.openTime && today_time?.closeTime) {
//           const time = moment().tz("Asia/Kolkata");
//           const beforeTime = moment.tz(
//             today_time?.openTime,
//             "HH:mm:ss",
//             "Asia/Kolkata"
//           );
//           const afterTime = moment.tz(
//             today_time?.closeTime,
//             "HH:mm:ss",
//             "Asia/Kolkata"
//           );
//           console.log("time Asia  ==>", time);
//           console.log("beforeTime Asia  ==>", beforeTime);
//           console.log("afterTime Asia  ==>", afterTime);
//           console.log("\n");

//           flag = isTimeInRange(time, beforeTime, afterTime);
//         }
//         if (!flag && m.istimeextended) {
//           flag = true;
//         }
//         return {
//           ...m,
//           isOutletOpen: flag,
//           Timing: {
//             Today: m?.time_day?.find((f) => f.Day === today),
//             Tomorrow: m?.time_day?.find((f) => f.Day === tomorrow),
//             Overmorrow: m?.time_day?.find((f) => f.Day === overmorrow),
//           },
//         };
//       });

//       res.status(200).json({
//         success: true,
//         data: {
//           cafeteriasForYouData: home_data,
//           PopularCafeterias: home_data,
//         },
//       });
//     } else {
//       throw get_customer_home_dataResponse.error;
//     }
//   } catch (error) {
//     res.status(500).json({ success: false, error: error.message });
//   }

//   // console.log("1 ==> ", moment.tz("10:00:00", 'HH:mm:ss', "Asia/Kolkata"));
// });

async function checkSequenceAndOutletHasMess(outletId) {
  try {
    
    const { data, error } = await supabaseInstance
      .from("Outlet")
      .select("hasMess, sequence, isVisibleToAll")
      .eq("outletId", outletId)
      .single();

    
    if (data) {
      let response = {};
        response.hasMess = data.hasMess;
        response.sequence = data.sequence;
        response.isVisibleToAll = data.isVisibleToAll;
      return response;
    } else {
      throw error;
    }
  } catch (error) {
    throw error;
  }
}

// new home data api with hasMess Flag
router.get("/homeData", async (req, res) => {
  const { campusId } = req.query;
  try {
    let today = moment().tz("Asia/Kolkata").format("dddd");
    let tomorrow = moment().tz("Asia/Kolkata").add(1, "days").format("dddd");
    let overmorrow = moment().tz("Asia/Kolkata").add(2, "days").format("dddd");
    let query = supabaseInstance
      .rpc("get_customer_home_data_test", {
        campus_id: campusId,
        today,
        tomorrow,
        overmorrow,
      })
      .limit(5);

    let get_customer_home_dataResponse = await query;

    if (get_customer_home_dataResponse.data) {
      let home_data = await Promise.all(
        get_customer_home_dataResponse.data.map(async (m) => {
          let flag = false;
          m.hasMess = await checkOutletHasMess(m.outletid);
          const today_time = m?.time_day?.find((f) => f.Day === today);
          if (today_time?.openTime && today_time?.closeTime) {
            const time = moment().tz("Asia/Kolkata");
            const beforeTime = moment.tz(
              today_time?.openTime,
              "HH:mm:ss",
              "Asia/Kolkata"
            );
            const afterTime = moment.tz(
              today_time?.closeTime,
              "HH:mm:ss",
              "Asia/Kolkata"
            );

            flag = isTimeInRange(time, beforeTime, afterTime);
          }
          if (!flag && m.istimeextended) {
            flag = true;
          }

          return {
            ...m,
            isOutletOpen: flag,
            Timing: {
              Today: m?.time_day?.find((f) => f.Day === today),
              Tomorrow: m?.time_day?.find((f) => f.Day === tomorrow),
              Overmorrow: m?.time_day?.find((f) => f.Day === overmorrow),
            },
          };
        })
      );

      res.status(200).json({
        success: true,
        data: {
          cafeteriasForYouData: home_data,
          PopularCafeterias: home_data,
        },
      });
    } else {
      throw get_customer_home_dataResponse.error;
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});


// router.get("/getOutletList/:campusId", async (req, res) => {
  //   const { campusId } = req.params;
  //   const { page, perPage, searchText, categoryId } = req.query;
  //   const pageNumber = parseInt(page) || 1;
  //   const itemsPerPage = parseInt(perPage) || 10;
//   try {
//     let today = moment().tz("Asia/Kolkata").format("dddd");
//     let tomorrow = moment().tz("Asia/Kolkata").add(1, "days").format("dddd");
//     let overmorrow = moment().tz("Asia/Kolkata").add(2, "days").format("dddd");
//     console.log("today,tomorrow,overmorrow", today, tomorrow, overmorrow);
//     let query = supabaseInstance
//       .rpc(
//         "get_outlet_list",
//         {
//           category_id: categoryId ? categoryId : null,
//           campus_id: campusId,
//           today,
//           tomorrow,
//           overmorrow,
//         },
//         { count: "exact" }
//       )
//       .eq("is_published", true)
//       .eq("is_active", true)
//       .range((pageNumber - 1) * itemsPerPage, pageNumber * itemsPerPage - 1)
//       .order("outlet_name", { ascending: true });

//     if (searchText) {
//       query = query.or(
//         `address.ilike.%${searchText}%,outlet_name.ilike.%${searchText}%`
//       );
//     }

//     const { data, error, count } = await query;

//     if (data) {
//       // let outletData = data.map(m => ({...m, Timing: m?.Timing?.find(f => f.dayId?.day)})).map(m => {
//       let outletData = data.map((m) => {
//         let flag = false;
//         const today_time = m?.time_day?.find((f) => f.Day === today);
//         if (today_time?.openTime && today_time?.closeTime) {
//           const time = moment().tz("Asia/Kolkata");
//           const beforeTime = moment.tz(
//             today_time?.openTime,
//             "HH:mm:ss",
//             "Asia/Kolkata"
//           );
//           const afterTime = moment.tz(
//             today_time?.closeTime,
//             "HH:mm:ss",
//             "Asia/Kolkata"
//           );
//           console.log("time==>", time);
//           console.log("beforeTime==>", beforeTime);
//           console.log("afterTime==>", afterTime);
//           flag = isTimeInRange(time, beforeTime, afterTime);
//         }

//         if (!flag && m.is_time_extended) {
//           flag = true;
//         }
//         return {
//           ...m,
//           isOutletOpen: flag,
//           Timing: {
//             Today: m?.time_day?.find((f) => f.Day === today),
//             Tomorrow: m?.time_day?.find((f) => f.Day === tomorrow),
//             Overmorrow: m?.time_day?.find((f) => f.Day === overmorrow),
//           },
//         };
//       });

//       const totalPages = Math.ceil(count / itemsPerPage);
//       res.status(200).json({
//         success: true,
//         data: outletData,
//         categoryId,
//         meta: {
//           page: pageNumber,
//           perPage: itemsPerPage,
//           totalPages,
//           totalCount: count,
//         },
//       });
//     } else {
//       throw error;
//     }
//   } catch (error) {
//     res.status(500).json({ success: false, error: error.message });
//   }
// });


// { with hasMess flag }production
router.get("/getOutletList/:campusId", async (req, res) => {
  const { campusId } = req.params;
const customerAuthUID = req.query.cuid;
  const { page, perPage, searchText, categoryId } = req.query;
  const pageNumber = parseInt(page) || 1;
  const itemsPerPage = parseInt(perPage) || 10;
  try {
    let today = moment().tz("Asia/Kolkata").format("dddd");
    let tomorrow = moment().tz("Asia/Kolkata").add(1, "days").format("dddd");
    let overmorrow = moment().tz("Asia/Kolkata").add(2, "days").format("dddd");
    let query = supabaseInstance
      .rpc(
        "get_outlet_list",
        {
          category_id: categoryId ? categoryId : null,
          campus_id: campusId,
          today,
          tomorrow,
          overmorrow,
        },
        { count: "exact" }
      )
      // .eq("is_published", true)
      .eq("is_active", true)
      .range((pageNumber - 1) * itemsPerPage, pageNumber * itemsPerPage - 1)
      .order("outlet_name", { ascending: true });

    if (searchText) {
      query = query.or(
        `address.ilike.%${searchText}%,outlet_name.ilike.%${searchText}%`
      );
    }

    const { data, error, count } = await query;

    if (data) {
      let outletData = data.map(async (m) => {
        let flag = false;
        const today_time = m?.time_day?.find((f) => f.Day === today);
        if (today_time?.openTime && today_time?.closeTime) {
          const time = moment().tz("Asia/Kolkata");
          const beforeTime = moment.tz(
            today_time?.openTime,
            "HH:mm:ss",
            "Asia/Kolkata"
          );
          const afterTime = moment.tz(
            today_time?.closeTime,
            "HH:mm:ss",
            "Asia/Kolkata"
          );
          flag = isTimeInRange(time, beforeTime, afterTime);
        }

        if (!flag && m.is_time_extended) {
          flag = true;
        }
// assigning hasMess flag and sequence to the outlet using checkSequenceAndOutletHasMess which returns (data.hasMess, data.sequence)
        let temp = await checkSequenceAndOutletHasMess(m.outletid);
        m.hasMess = temp.hasMess;
        m.sequence = temp.sequence;
        m.isVisibleToAll = temp.isVisibleToAll;
        m.isWhitelisted = await isWhiteListed(customerAuthUID, m.outletid);
        // todo {to be added for white listing}
        // /*
        if(m.hasMess && !m.isVisibleToAll){
          isMessVisible = await isWhiteListed(customerAuthUID, m.outletid);
          
          if(!isMessVisible){
            // removing the outlet from the list if the user is not white listed
            return;
          }
        }
          // */
        return {
          ...m,
          isOutletOpen: flag,
          Timing: {
            Today: m?.time_day?.find((f) => f.Day === today),
            Tomorrow: m?.time_day?.find((f) => f.Day === tomorrow),
            Overmorrow: m?.time_day?.find((f) => f.Day === overmorrow),
          },
        };
      });

      // Wait for all promises to resolve before sending the response
      outletData = await Promise.all(outletData);

      // removing null values from the outletData array
      outletData = outletData.filter((el) => {
        return el != null;
      });

      // sort the outletData based on the sequence
      outletData.sort((a, b) => a.sequence - b.sequence);

      const totalPages = Math.ceil(count / itemsPerPage);
      res.status(200).json({
        success: true,
        data: outletData,
        categoryId,
        meta: {
          page: pageNumber,
          perPage: itemsPerPage,
          totalPages,
          totalCount: count,
        },
      });
    } else {
      throw error;
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

router.post("/upsertUserImage", upload.single("file"), async (req, res) => {
  const { customerAuthUID } = req.body;
  try {
    const { data, error } = await supabaseInstance.storage
      .from("user-photo")
      .upload(customerAuthUID + ".webp", req.file.buffer, {
        cacheControl: "3600",
        upsert: true,
        contentType: "image/webp",
      });

    if (data?.path) {
      const publickUrlresponse = await supabaseInstance.storage
        .from("user-photo")
        .getPublicUrl(data?.path);
      if (publickUrlresponse?.data?.publicUrl) {
        const publicUrl = publickUrlresponse?.data?.publicUrl;
        const userData = await supabaseInstance
          .from("Customer")
          .update({ photo: `${publicUrl}?${new Date().getTime()}` })
          .eq("customerAuthUID", customerAuthUID)
          .select("*")
          .maybeSingle();
        res.status(200).json({
          success: true,
          data: userData.data,
        });
      } else {
        throw publickUrlresponse.error || "Getting Error in PublicUrl";
      }
    } else {
      throw error;
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error });
  }
});

router.get("/getCustomer/:outletId", async (req, res) => {
  const { outletId } = req.params;
  const { page, perPage, sort, searchText } = req.query;
  const pageNumber = parseInt(page) || 1;
  const itemsPerPage = parseInt(perPage) || 10;
  try {
    let query = supabaseInstance
      .rpc(
        "get_distinct_customer_name",
        { outlet_id: outletId },
        { count: "exact" }
      )
      // .range((pageNumber - 1) * itemsPerPage, pageNumber * itemsPerPage - 1)
      .order("created_at", { ascending: false });

    let sortQuery = supabaseInstance.rpc(
      "get_distinct_customer_name",
      { outlet_id: outletId },
      { count: "exact" }
    );

    if (sort) {
      query = sortQuery.order("customername", {
        ascending: sort == "true" ? true : false,
      });
    }

    if (searchText) {
      query = query.ilike("customername", `%${searchText}%`);
    }

    if (page && perPage) {
      query = query.range(
        (pageNumber - 1) * itemsPerPage,
        pageNumber * itemsPerPage - 1
      );
    }

    const { data, error, count } = await query;

    if (data) {
      const totalPages = Math.ceil(count / itemsPerPage);

      let response = {
        success: true,
        data: data,
      };

      if (page && perPage) {
        response.meta = {
          page: pageNumber,
          perPage: itemsPerPage,
          totalPages,
          totalCount: count,
        };
      }
      res.status(200).json({
        ...response,
      });
    } else {
      throw error;
    }
  } catch (error) {
    console.log(error);
    res.status(500).json({ success: false, error: error.message });
  }
});

router.post("/updateCustomer/:customerAuthUID", async (req, res) => {
  const { customerAuthUID } = req.params;
  const { customerName, email, mobile, dob, genderId, campusId } = req.body;
  try {
    // Check if customer exists and is not deleted
    const { data: customer, error: customerError } = await supabaseInstance
      .from("Customer")
      .select("isDelete")
      .eq("customerAuthUID", customerAuthUID)
      .maybeSingle();

    if (customerError) {
      throw customerError;
    }

    if (!customer) {
      return res.status(404).json({
        success: false,
        message: "Customer not found"
      });
    }

    if (customer.isDelete) {
      return res.status(400).json({
        success: false,
        message: "Cannot update deleted customer"
      });
    }
    /*

    // Check for duplicate email
    if (email) {
      const { data: existingEmail, error: emailError } = await supabaseInstance
        .from("Customer")
        .select("customerAuthUID")
        .eq("email", email)
        .eq("isDelete", false)
        .neq("customerAuthUID", customerAuthUID);
      
      if (emailError) {
        console.error("emailError==>", emailError);
        throw emailError;
      }
      if (existingEmail && existingEmail.length > 0) {
        console.log("existingEmail==>", existingEmail);
        return res.status(400).json({ success: false, message:"Email already in use by another user." });
      }
    }

    // Check for duplicate mobile
    if (mobile) {
      const { data: existingMobile, error: mobileError } = await supabaseInstance
        .from("Customer")
        .select("customerAuthUID")
        .eq("mobile", mobile)
        .eq("isDelete", false)
        .neq("customerAuthUID", customerAuthUID);
      
      if(mobileError){
        console.error("mobileError==>", mobileError);
        throw mobileError;
      }
      if (existingMobile && existingMobile.length > 0) {
        console.log("existingMobile==>", existingMobile);
        return res.status(400).json({ success: false, message: "Mobile number already in use by another user." });
      }
    }
    */
    const { data, error } = await supabaseInstance
      .from("Customer")
      .update({ customerName, dob, genderId, campusId })
      .select(customerSlectString)
      .eq("customerAuthUID", customerAuthUID)
      .eq("isDelete", false);

    if (data && data.length > 0) {
      res.status(200).json({
        success: true,
        message: "Data updated successfully",
        data: data[0],
      });
    } else {
      throw error || new Error("Failed to update customer");
    }
  } catch (error) {
    console.error("Update customer error:", error);
    res.status(500).json({ success: false, error: error.message });
  }
});

router.get("/realtimeCustomerOrders/:orderId", function (req, res) {
  const { orderId } = req.params;
  res.statusCode = 200;
  res.setHeader("Access-Control-Allow-Origin", "*");
  res.setHeader("Cache-Control", "no-cache");
  res.setHeader("connection", "keep-alive");
  res.setHeader("Content-Type", "text/event-stream");

  const channelName = `customer-update-channel-${orderId}-${Date.now()}`;

  supabaseInstance
    .channel(channelName)
    .on(
      "postgres_changes",
      {
        event: "UPDATE",
        schema: "public",
        table: "Order",
        filter: `orderId=eq.${orderId}`,
      },
      async (payload) => {
        const orderData = await supabaseInstance
          .from("Order")
          .select("*,Order_Item(*,Menu_Item(minimumpreparationtime))")
          .eq("orderId", payload.new.orderId)
          .maybeSingle();
        console.log("orderData==>", orderData);

        res.write(
          `data: ${JSON.stringify({
            updateorder:
              {
                ...orderData?.data,
                totalItems: orderData?.data?.Order_Item?.length || 0,
              } || null,
          })}\n\n`
        );
      }
    )
    .on(
      "postgres_changes",
      {
        event: "UPDATE",
        schema: "public",
        table: "Refund",
        filter: `orderId=eq.${orderId}`,
      },
      async (payload) => {
        const refundData = await supabaseInstance
          .from("Refund")
          .select("refund_status")
          .eq("orderId", payload.new.orderId)
          .maybeSingle();
        res.write(`data: ${JSON.stringify(refundData.data)}\n\n`);
      }
    )
    .subscribe((status, error) => {
      if (status === "CHANNEL_ERROR") {
        console.error(`realtimeCustomerOrders/:orderId error => `, error);
      }
      console.log("subscribe status for orderId => ", orderId);
    });

  res.write("retry: 10000\n\n");
  req.on("close", () => {
    supabaseInstance
      .channel(channelName)
      .unsubscribe()
      .then((res) => {
        console.log(".then => ", res);
      })
      .catch((err) => {
        console.log(".catch => ", err);
      })
      .finally(() => {
        console.log(`${channelName} Connection closed`);
      });
  });
});

router.get("/getLiveCustomerOrders/:customerAuthUID", async (req, res) => {
  const { customerAuthUID } = req.params;
  try {
    let currentDate = moment().tz("Asia/Kolkata").format("YYYY-MM-DD");

    let query = supabaseInstance
      .rpc("get_live_customer_orders", {
        customerauthuid: customerAuthUID,
        targate_date: currentDate,
      })
      .gte("orderstatusid", 0)
      .lt("orderstatusid", 10);

    const { data, error } = await query;

    if (data) {
      res.status(200).json({
        success: true,
        data: data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    console.log(error);
    res.status(500).json({ success: false, error: error });
  }
});

router.post("/userGooglelogin", async (req, res) => {
  const { token } = req.body;
  try {
    if (!token) {
      throw new Error("Token is missing in the request.");
    }

    const { data, error } = await supabaseInstance.auth.signInWithIdToken({
      provider: "google",
      token: token,
    });
    console.log("data==>", data);
    console.log("error==>", error);

    if (data?.user?.id) {
      const customerData = await supabaseInstance
        .from("Customer")
        .select(customerSlectString)
        .eq("isDelete", false)
        .eq("customerAuthUID", data.user.id)
        .maybeSingle();
      console.log("customerData=>", customerData);
      if (customerData.data) {
        res.status(200).json({ success: true, data: customerData.data });
      } else {
        // mobile: data?.user?.phone ? +data.user.phone : null,
        const customerResponse = await supabaseInstance
          .from("Customer")
          .insert({
            email: data.user.email,
            customerName: data.user?.user_metadata?.full_name,
            customerAuthUID: data.user.id,
          })
          .select(customerSlectString)
          .maybeSingle();
        if (customerResponse.data) {
          res.status(200).json({ success: true, data: customerResponse.data });
        } else {
          res
            .status(500)
            .json({ success: false, error: customerResponse.error });
        }
      }
    } else {
      throw error;
    }
  } catch (error) {
    console.error("Authentication error:", error);
    res.status(500).json({ success: false, error: error.message });
  }
});

router.post("/iosUserGooglelogin", async (req, res) => {
  const { email, name, photo } = req.body;
  try {
    if (!email) {
      throw new Error("Email is missing in the request.");
    }

    const { data, error } = await supabaseInstance.auth.admin.createUser({
      email: email,
      email_confirm: true,
    });

    if (data?.user?.id) {
      const customerData = await supabaseInstance
        .from("Customer")
        .select(customerSlectString)
        .eq("isDelete", false)
        .eq("customerAuthUID", data.user.id)
        .limit(1)
        .single();
      console.log("customerData=>", customerData);
      if (customerData.data) {
        res.status(200).json({ success: true, data: customerData.data });
      } else {
        // mobile: data?.user?.phone ? +data.user.phone : null,
        const customerResponse = await supabaseInstance
          .from("Customer")
          .insert({
            email: data.user.email,
            customerName: name || null,
            customerAuthUID: data.user.id,
          })
          .select(customerSlectString)
          .limit(1)
          .single();
        if (customerResponse.data) {
          res.status(200).json({ success: true, data: customerResponse.data });
        } else {
          res
            .status(500)
            .json({ success: false, error: customerResponse.error });
        }
      }
    } else if (
      error?.message?.includes("email address has already been registered")
    ) {
      const customerData = await supabaseInstance
        .from("Customer")
        .select(customerSlectString)
        .eq("isDelete", false)
        .eq("email", email)
        .limit(1)
        .single();
      console.log("customerData=>", customerData);
      if (customerData.data) {
        res.status(200).json({ success: true, data: customerData.data });
      } else {
        res
          .status(500)
          .json({ success: false, error: "Something went wrong." });
      }
    } else {
      throw error;
    }
  } catch (error) {
    console.error("Authentication error:", error);
    res.status(500).json({ success: false, error: error.message });
  }
});

router.post("/updateMobile", async (req, res) => {
  const { mobile, customerAuthUID } = req.body;
  console.log({ mobile, customerAuthUID });
  try {
    supabaseInstance.auth.admin
      .updateUserById(customerAuthUID, { phone: mobile })
      .then(async (updateUserByIdResponse) => {
        if (updateUserByIdResponse?.data?.user) {
          const customerResponse = await supabaseInstance
            .from("Customer")
            .update({ mobile: mobile + "" })
            .eq("customerAuthUID", customerAuthUID)
            .select(customerSlectString)
            .maybeSingle();
          if (customerResponse.data) {
            res.status(200).json({
              success: true,
              message: "Mobile added Successfully.",
              data: customerResponse.data,
            });
          } else {
            throw customerResponse.error;
          }
        } else {
          console.error(updateUserByIdResponse.error);
          if (
            updateUserByIdResponse?.error?.message.includes(
              "duplicate key value violates unique constraint"
            )
          ) {
            res.status(500).json({
              success: false,
              message: "Mobile number already use someone.",
            });
          } else {
            res.status(500).json({
              success: false,
              message: updateUserByIdResponse.error.message,
            });
          }
        }
      })
      .catch((updateUserByIdError) => {
        console.error("updateUserByIdError => ", updateUserByIdError);

        res.status(500).json({
          success: false,
          message: "Something went wrong.",
        });
      });
  } catch (error) {
    res.status(500).json({ success: false, error: error?.message || error });
  }
});

router.post("/otplessUser", async (req, res) => {
  const { token } = req.body;
  try {
    if (!token) {
      throw new Error("Token is missing in the request.");
    }
    const options = {
      method: "POST",
      url: otplessConfig.config.client_url,
      headers: {
        clientId: otplessConfig.config.clint_Id,
        clientSecret: otplessConfig.config.client_secret_key,
        "content-Type": "application/json",
      },
      data: {
        token: token,
      },
    };
    const { data, error } = await axios.default.request(options);
    console.log("data==>", data);
    if (data) {
      const customerData = await supabaseInstance
        .from("Customer")
        .select("*")
        .eq("mobile", data.mobile.number)
        .maybeSingle();
      if (customerData.data) {
        console.log("customerData=>", customerData);
        res.status(200).json({ success: true, data: customerData.data });
      } else {
        const { userData, error } =
          await supabaseInstance.auth.admin.createUser({
            email: data?.email?.email,
            phone: data?.mobile?.number,
            phone_confirm: true,
          });
        console.log("User Data=>", userData);
        if (userData?.user) {
          const customerResponse = await supabaseInstance
            .from("Customer")
            .insert({
              email: data?.email?.email,
              mobile: data?.mobile?.number,
              customerName: data?.mobile?.name,
              customerAuthUID: userData.user.id,
            })
            .select("*")
            .maybeSingle();
          if (customerResponse.data) {
            console.log("customerResponse=>", customerResponse);
            res
              .status(200)
              .json({ success: true, data: customerResponse.data });
          } else {
            res
              .status(500)
              .json({ success: false, error: customerResponse.error });
          }
        } else {
          throw error;
        }
      }
    } else {
      throw error;
    }
  } catch (error) {
    console.error("Authentication error:", error);
    res.status(500).json({ success: false, error: error.message });
  }
});

router.post("/userApplelogin", async (req, res) => {
  const { token, nonce } = req.body;
  try {
    if (!token) {
      throw new Error("Token is missing in the request.");
    }

    const { data, error } = await supabaseInstance.auth.signInWithIdToken({
      provider: "apple",
      token: token,
      nonce: nonce,
    });
    console.log("data=>", data);

    if (data?.user?.id) {
      const customerData = await supabaseInstance
        .from("Customer")
        .select(customerSlectString)
        .eq("customerAuthUID", data.user.id)
        .maybeSingle();
      console.log("customerData=>", customerData);
      if (customerData.data) {
        res.status(200).json({ success: true, data: customerData.data });
      } else {
        var emailStr = data.user.email;
        var name = data.user?.user_metadata?.full_name || null;

        if (name) {
          var nameMatch = emailStr.match(/^([^@]*)@/);
          name = nameMatch ? nameMatch[1] : null;
          name = name.replace(/[^A-Za-z]/g, " ");
        }

        const customerResponse = await supabaseInstance
          .from("Customer")
          .insert({
            email: emailStr,
            mobile: data?.user?.phone ? +data.user.phone : null,
            customerName: name,
            customerAuthUID: data.user.id,
          })
          .select(customerSlectString)
          .maybeSingle();
        if (customerResponse.data) {
          res.status(200).json({ success: true, data: customerResponse.data });
        } else {
          res
            .status(500)
            .json({ success: false, error: customerResponse.error });
        }
      }
    } else {
      throw error;
    }
  } catch (error) {
    console.error("Authentication error:", error);
    res.status(500).json({ success: false, error: error.message });
  }
});

router.post("/userDeletion", async (req, res) => {
  const { customerAuthUID } = req.body;

  console.log({ customerAuthUID });

  try {
    const authDeleteResponse = await supabaseInstance.auth.admin.deleteUser(
      customerAuthUID,
      false
    );

    if (authDeleteResponse.data) {
      await supabaseInstance
        .from("Customer")
        .update({ isDelete: true })
        .eq("customerAuthUID", customerAuthUID)
        .maybeSingle();
      res.status(200).json({
        success: true,
        message: "User delete successfully.",
      });
    } else {
      throw customerResponse.error;
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error?.message || error });
  }
});

// router.get("/realtimeOutlets/:outletId", function (req, res) {
//   const { outletId } = req.params;
//   res.statusCode = 200;
//   res.setHeader("Access-Control-Allow-Origin", "*");
//   res.setHeader("Cache-Control", "no-cache");
//   res.setHeader("connection", "keep-alive");
//   res.setHeader("Content-Type", "text/event-stream");

//   const channelName = `outlet-update-channel-${outletId}-${Date.now()}`;

//   supabaseInstance
//     .channel(channelName)
//     .on(
//       "postgres_changes",
//       {
//         event: "UPDATE",
//         schema: "public",
//         table: "Timing",
//         filter: `outletId=eq.${outletId}`,
//       },
//       async (payload) => {
//         const outdetData = await supabaseInstance
//           .from("Outlet")
//           .select(
//             "outletName,address,isPublished,logo,headerImage,outletId,isActive,isPickUp,isDineIn,isDelivery,convenienceFee,isOutletOpen,packaging_charge,Timing(openTime,closeTime,Days(day))"
//           )
//           .eq("outletId", payload.new.outletId)
//           .maybeSingle();
//         console.log("outdetData===>", outdetData);

//         if (outdetData?.data) {
//           outletdetails = {
//             ...outdetData.data,
//             Timing: {
//               Today: outdetData.data?.Timing?.find(
//                 (f) =>
//                   f.Days?.day === moment().tz("Asia/Kolkata").format("dddd")
//               ),
//               Tomorrow: outdetData.data?.Timing?.find(
//                 (f) =>
//                   f.Days?.day ===
//                   moment().tz("Asia/Kolkata").add(1, "days").format("dddd")
//               ),
//               Overmorrow: outdetData.data?.Timing?.find(
//                 (f) =>
//                   f.Days?.day ===
//                   moment().tz("Asia/Kolkata").add(2, "days").format("dddd")
//               ),
//             },
//           };

//           let todayflag = false;
//           let tomorrowflag = false;
//           let Overmorrowflag = false;
//           if (
//             outletdetails?.Timing?.Today?.openTime &&
//             outletdetails?.Timing?.Today?.closeTime
//           ) {
//             const time = moment().tz("Asia/Kolkata");
//             const beforeTime = moment.tz(
//               outletdetails?.Timing?.Today?.openTime,
//               "HH:mm:ss",
//               "Asia/Kolkata"
//             );
//             const afterTime = moment.tz(
//               outletdetails?.Timing?.Today?.closeTime,
//               "HH:mm:ss",
//               "Asia/Kolkata"
//             );

//             todayflag = outletdetails?.isOutletOpen;
//           }

//           // if (!todayflag && outletdetails.isTimeExtended) {
//           //   todayflag = true;
//           // }

//           if (
//             outletdetails?.Timing?.Tomorrow?.openTime &&
//             outletdetails?.Timing?.Tomorrow?.closeTime
//           ) {
//             const time = moment().tz("Asia/Kolkata");
//             const beforeTime = moment.tz(
//               outletdetails?.Timing?.Tomorrow?.openTime,
//               "HH:mm:ss",
//               "Asia/Kolkata"
//             );
//             const afterTime = moment.tz(
//               outletdetails?.Timing?.Tomorrow?.closeTime,
//               "HH:mm:ss",
//               "Asia/Kolkata"
//             );

//             tomorrowflag = isTimeInRange(time, beforeTime, afterTime);
//           }

//           // if (!tomorrowflag && outletdetails.isTimeExtended) {
//           //   tomorrowflag = true;
//           // }

//           if (
//             outletdetails?.Timing?.Overmorrow?.openTime &&
//             outletdetails?.Timing?.Overmorrow?.closeTime
//           ) {
//             const time = moment().tz("Asia/Kolkata");
//             const beforeTime = moment.tz(
//               outletdetails?.Timing?.Overmorrow?.openTime,
//               "HH:mm:ss",
//               "Asia/Kolkata"
//             );
//             const afterTime = moment.tz(
//               outletdetails?.Timing?.Overmorrow?.closeTime,
//               "HH:mm:ss",
//               "Asia/Kolkata"
//             );

//             Overmorrowflag = isTimeInRange(time, beforeTime, afterTime);
//           }

//           // if (!Overmorrowflag && outletdetails.isTimeExtended) {
//           //   Overmorrowflag = true;
//           // }

//           outletdetails.todayisOutletOpen = todayflag;
//           outletdetails.tomorrowisOutletOpen = tomorrowflag;
//           outletdetails.OvermorrowisOutletOpen = Overmorrowflag;
//         }
//         console.log("outletdetails==>", outletdetails);
//         // res.write(`data: ${JSON.stringify(outletdetails)}\n\n`);
//       }
//     )
//     .subscribe(async (status, error) => {
//       console.error("/realtimeOutlets/:outletId - error => ", error);
//       console.log(`outlet-update-channel-${outletId} status => `, status);
//       if (status === "CHANNEL_ERROR") {
//         console.error(`realtimeOutlets/:outletId error => `, error);
//       }
//       if (status === "SUBSCRIBED") {
//         const outdetData = await supabaseInstance
//           .from("Outlet")
//           .select(
//             "outletName,address,isPublished,logo,headerImage,outletId,isActive,isPickUp,isDineIn,isDelivery,convenienceFee,isOutletOpen,packaging_charge,Timing(openTime,closeTime,Days(day))"
//           )
//           .eq("outletId", outletId)
//           .maybeSingle();
//         console.log("outdetData===>", outdetData);

//         if (outdetData?.data) {
//           outletdetails = {
//             ...outdetData.data,
//             Timing: {
//               Today: outdetData.data?.Timing?.find(
//                 (f) =>
//                   f.Days?.day === moment().tz("Asia/Kolkata").format("dddd")
//               ),
//               Tomorrow: outdetData.data?.Timing?.find(
//                 (f) =>
//                   f.Days?.day ===
//                   moment().tz("Asia/Kolkata").add(1, "days").format("dddd")
//               ),
//               Overmorrow: outdetData.data?.Timing?.find(
//                 (f) =>
//                   f.Days?.day ===
//                   moment().tz("Asia/Kolkata").add(2, "days").format("dddd")
//               ),
//             },
//           };

//           let todayflag = false;
//           let tomorrowflag = false;
//           let Overmorrowflag = false;
//           if (
//             outletdetails?.Timing?.Today?.openTime &&
//             outletdetails?.Timing?.Today?.closeTime
//           ) {
//             const time = moment().tz("Asia/Kolkata");
//             const beforeTime = moment.tz(
//               outletdetails?.Timing?.Today?.openTime,
//               "HH:mm:ss",
//               "Asia/Kolkata"
//             );
//             const afterTime = moment.tz(
//               outletdetails?.Timing?.Today?.closeTime,
//               "HH:mm:ss",
//               "Asia/Kolkata"
//             );

//             todayflag = outletdetails?.isOutletOpen;
//           }

//           // if (!todayflag && outletdetails.isTimeExtended) {
//           //   todayflag = true;
//           // }

//           if (
//             outletdetails?.Timing?.Tomorrow?.openTime &&
//             outletdetails?.Timing?.Tomorrow?.closeTime
//           ) {
//             const time = moment().tz("Asia/Kolkata");
//             const beforeTime = moment.tz(
//               outletdetails?.Timing?.Tomorrow?.openTime,
//               "HH:mm:ss",
//               "Asia/Kolkata"
//             );
//             const afterTime = moment.tz(
//               outletdetails?.Timing?.Tomorrow?.closeTime,
//               "HH:mm:ss",
//               "Asia/Kolkata"
//             );

//             tomorrowflag = isTimeInRange(time, beforeTime, afterTime);
//           }

//           // if (!tomorrowflag && outletdetails.isTimeExtended) {
//           //   tomorrowflag = true;
//           // }

//           if (
//             outletdetails?.Timing?.Overmorrow?.openTime &&
//             outletdetails?.Timing?.Overmorrow?.closeTime
//           ) {
//             const time = moment().tz("Asia/Kolkata");
//             const beforeTime = moment.tz(
//               outletdetails?.Timing?.Overmorrow?.openTime,
//               "HH:mm:ss",
//               "Asia/Kolkata"
//             );
//             const afterTime = moment.tz(
//               outletdetails?.Timing?.Overmorrow?.closeTime,
//               "HH:mm:ss",
//               "Asia/Kolkata"
//             );

//             Overmorrowflag = isTimeInRange(time, beforeTime, afterTime);
//           }

//           // if (!Overmorrowflag && outletdetails.isTimeExtended) {
//           //   Overmorrowflag = true;
//           // }

//           outletdetails.todayisOutletOpen = todayflag;
//           outletdetails.tomorrowisOutletOpen = tomorrowflag;
//           outletdetails.OvermorrowisOutletOpen = Overmorrowflag;

//           console.log("outletdetails==>", outletdetails);
//           res.write(`data: ${JSON.stringify({ data: outletdetails || [] })}`);
//           res.write("\n\n");
//         } else {
//           res.write(`data: ${JSON.stringify({ data: [] })}`);
//           res.write("\n\n");
//         }
//       }
//       console.log("subscribe status for outletId => ", outletId);
//     });
//   res.write("retry: 10000\n\n");
//   req.on("close", () => {
//     console.log(`${channelName} /realtimeOutlets/ Go to unsubscribe()`);
//     supabaseInstance
//       .channel(channelName)
//       .unsubscribe()
//       .then((res) => {
//         console.log(
//           `${channelName} /realtimeOutlets/ unsubscribe() success => `,
//           res
//         );
//       })
//       .catch((err) => {
//         console.log(
//           `${channelName} /realtimeOutlets/ unsubscribe() Error => `,
//           err
//         );
//       });
//   });
// });

const processOutletData = (outletData) => {
  if (!outletData) return null;

  const now = moment().tz('Asia/Kolkata');
  const days = ['Today', 'Tomorrow', 'Overmorrow'];

  const timing = days.reduce((acc, day, index) => {
    const dayData = outletData.Timing?.find(
      (f) => f.Days?.day === now.clone().add(index, 'days').format('dddd')
    );
    acc[day] = dayData;
    return acc;
  }, {});

  const outletDetails = {
    ...outletData,
    Timing: timing,
  };

  days.forEach((day) => {
    const { openTime, closeTime } = outletDetails.Timing[day] || {};
    let isOpen = false;

    if (openTime && closeTime) {
      const beforeTime = moment.tz(openTime, 'HH:mm:ss', 'Asia/Kolkata');
      const afterTime = moment.tz(closeTime, 'HH:mm:ss', 'Asia/Kolkata');

      isOpen = day === 'Today' 
        ? outletDetails.isOutletOpen 
        : isTimeInRange(now, beforeTime, afterTime);
    }

    outletDetails[`${day.toLowerCase()}isOutletOpen`] = isOpen;
  });

  return outletDetails;
};

router.get('/realtimeOutlets/:outletId', (req, res) => {
  const { outletId } = req.params;

  res.writeHead(200, {
    'Content-Type': 'text/event-stream',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive',
    'Access-Control-Allow-Origin': '*',
  });

  const channelName = `outlet-update-channel-${outletId}-${Date.now()}`;

  const handleOutletData = async (payload) => {
    try {
      const { data: outletData } = await supabaseInstance
        .from('Outlet')
        .select('outletName,address,isPublished,logo,headerImage,outletId,isActive,isPickUp,isDineIn,isDelivery,convenienceFee,isOutletOpen,packaging_charge,Timing(openTime,closeTime,Days(day))')
        .eq('outletId', payload ? payload.new.outletId : outletId)
        .maybeSingle();

      const processedData = processOutletData(outletData);
      res.write(`data: ${JSON.stringify({ data: processedData || [] })}\n\n`);
    } catch (error) {
      console.error('Error fetching outlet data:', error);
      res.write(`data: ${JSON.stringify({ error: 'Failed to fetch outlet data' })}\n\n`);
    }
  };

  const channel = supabaseInstance
    .channel(channelName)
    .on(
      'postgres_changes',
      {
        event: 'UPDATE',
        schema: 'public',
        table: 'Timing',
        filter: `outletId=eq.${outletId}`,
      },
      handleOutletData
    )
    .subscribe(async (status) => {
      if (status === 'SUBSCRIBED') {
        await handleOutletData();
      }
    });

  res.write('retry: 10000\n\n');

  req.on('close', () => {
    console.log(`${channelName} /realtimeOutlets/ Unsubscribing`);
    channel.unsubscribe()
      .then(() => console.log(`${channelName} /realtimeOutlets/ Unsubscribe success`))
      .catch((err) => console.error(`${channelName} /realtimeOutlets/ Unsubscribe error:`, err));
  });
});

router.post("/cancelOrder/:orderId", async (req, res) => {
  const { orderId } = req.params;
  try {
    // Fetch the order with customer details
    const { data, error } = await supabaseInstance
      .from("Order")
      .update({ orderStatusId: -1 })
      .select("*,customerAuthUID(*)")
      .eq("orderId", orderId)
      .maybeSingle();

    if (!data) {
      return res.status(404).json({ 
        success: false, 
        error: error || "Order not found" 
      });
    }


    // Start parallel operations immediately
    const parallelOperations = [];

    // Mess order processing
    if (data?.isMessOrder) {
      const messOrderPromise = (async () => {
        console.log(`Processing mess order for order ID: ${orderId}`);
        
        // Fetch order items with menu item details
        const { data: orderItems, error: orderError } = await supabaseInstance
          .from('Order_Item')
          .select('itemId, Menu_Item(itemname)')
          .eq('orderId', orderId);

        if (orderError) {
          console.error("Error fetching order items:", orderError);
          throw orderError;
        }


        // Import date service
        const { getWeekInfo } = require("./../services/dateTimeService");
        const weekInfo = getWeekInfo(new Date(data.created_at));
        const rsvpDate = new Date(data.created_at).toISOString().split('T')[0];

        // Meal type mapping
        const mealTypeMap = {
          'Breakfast': 1,
          'Lunch': 2,
          'High Tea': 3,
          'Dinner': 4
        };

        // Batch fetch all weekly menus at once
        const validItems = orderItems.filter(item => mealTypeMap[item.Menu_Item.itemname]);
        const mealTypeIds = [...new Set(validItems.map(item => mealTypeMap[item.Menu_Item.itemname]))];

        const { data: weeklyMenusData, error: weeklyMenuError } = await supabaseInstance
          .from('Weekly_Menu')
          .select('menuId, mealTypeId')
          .eq('outletId', data.outletId)
          .in('mealTypeId', mealTypeIds)
          .eq('dayOfWeek', weekInfo.dayOfWeek)
          .eq('weekNumber', weekInfo.weekNumber);

        if (weeklyMenuError) {
          console.error("Error fetching weekly menus:", weeklyMenuError);
          throw weeklyMenuError;
        }

        // Create lookup map for menuIds
        const menuIdMap = weeklyMenusData.reduce((map, menu) => {
          map[menu.mealTypeId] = menu.menuId;
          return map;
        }, {});

        // Batch delete RSVPs
        const rsvpDeletes = validItems.map(item => {
          const mealTypeId = mealTypeMap[item.Menu_Item.itemname];
          const menuId = menuIdMap[mealTypeId];

          if (!menuId) {
            console.warn(`No menu found for meal type: ${mealTypeId}`);
            return null;
          }

          return {
            customerAuthUID: data.customerAuthUID.customerAuthUID,
            menuId,
            mealTypeId,
            rsvpDate
          };
        }).filter(Boolean);

        // Execute all RSVP deletions in parallel
        await Promise.all(rsvpDeletes.map(async (rsvp) => {
          const { error: rsvpError } = await supabaseInstance
            .from('RSVP')
            .delete()
            .eq('customerAuthUID', rsvp.customerAuthUID)
            .eq('menuId', rsvp.menuId)
            .eq('mealTypeId', rsvp.mealTypeId)
            .eq('rsvpDate', rsvp.rsvpDate)
            .single();

          if (rsvpError) {
            console.error("Error removing RSVP:", rsvpError);
            throw rsvpError;
          }
        }));
      })();

      parallelOperations.push(messOrderPromise);
    }

    // Stock restoration
    const stockRestorePromise = (async () => {
      try {
        const { data: cancelOrderItems, error: cancelItemsError } = await supabaseInstance
          .from('Order_Item')
          .select('itemId, quantity, Menu_Item(itemid, countable, quantity_in_stock, status)')
          .eq('orderId', orderId);

        if (cancelItemsError) throw cancelItemsError;

        // Aggregate quantities per itemId and filter countable items
        const countableUpdates = new Map();
        for (const oi of cancelOrderItems || []) {
          if (!oi.Menu_Item?.countable) continue;

          const qty = Number(oi.quantity || 0);
          const currentStock = Math.max(0, Number(oi.Menu_Item.quantity_in_stock || 0));
          const currentStatus = oi.Menu_Item.status;

          if (countableUpdates.has(oi.itemId)) {
            countableUpdates.get(oi.itemId).quantity += qty;
          } else {
            countableUpdates.set(oi.itemId, {
              itemId: oi.itemId,
              quantity: qty,
              currentStock,
              currentStatus
            });
          }
        }

        // Batch update stock for all countable items
        if (countableUpdates.size > 0) {
          await Promise.all([...countableUpdates.values()].map(async (update) => {
            const newStock = update.currentStock + update.quantity;
            
            // Prepare update object
            const updateData = { quantity_in_stock: newStock };
            
            // If status was previously false, set it to true after restocking
            if (update.currentStatus === false) {
              updateData.status = true;
            }
            
            const { error: stockRestoreError } = await supabaseInstance
              .from('Menu_Item')
              .update(updateData)
              .eq('itemid', update.itemId);

            if (stockRestoreError) {
              console.error(`Stock restore failed for item ${update.itemId}:`, stockRestoreError);
            }
          }));
        }
      } catch (stockRestoreCatchError) {
        console.error('Error during stock restoration:', stockRestoreCatchError);
        // Do not block cancellation on stock restore issues
      }
    })();

    parallelOperations.push(stockRestorePromise);

    // Refund and status update in parallel
    const refundPromise = requestRefund(orderId);
    const statusUpdatePromise = updateOrderStatus(orderId, "-1");

    parallelOperations.push(refundPromise, statusUpdatePromise);

    // Wait for all operations to complete
    const [, , requestRefundResponse, updateOrderStatusResponse] = await Promise.all(parallelOperations);


    const io = req.app.get('io');
    if (io && io.emitOrderCancellationStatus) {
      io.emitOrderCancellationStatus(data.outletId, orderId, data.orderSequenceId, data.orderStatusId);
    }

    // send notification to outletUsers
    const fcmData = await getOutletUserFcmTokens({outletId: data.outletId, requiredFields: ['fcmToken', 'appName']});
    
    await sendOutletUserNotifications("Order Cancelled", `Order #${data.orderSequenceId} has been cancelled.`, fcmData, );

    res.status(200).json({
      success: true,
      data: {
        orderData: data,
        refundResponse: requestRefundResponse,
        updateOrderStatus: updateOrderStatusResponse,
      },
    });

  } catch (error) {
    console.error("Order Cancellation Error:", error);
    res.status(500).json({
      success: false,
      error: error.message || "Internal server error"
    });
  }
});

router.post("/checkPaymentMethod", async (req, res) => {
  const { campusId } = req.body;

  const campus = ["18a8a882-c521-49cb-bf96-1c86e87ed77d"]; // All the id's of campus which want different payment method

  try {
    const data = campus?.includes(campusId);

    if (data === true) {
      res.status(200).json({
        success: true,
        message: "Call PhonePe Payment API",
      });
    }

    if (data === false) {
      res.status(200).json({
        success: false,
        message: "Call Easebuzz Payment API",
      });
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error?.message || error });
  }
});

// storing fcm token for a user
router.post("/storeFcmToken", async (req, res) => {
  const { customerAuthUID, fcmToken, appName } = req.body;
  
  try {
    const { data, error } = await supabaseInstance
      .from('FCM_Tokens')
      .upsert(
        { customerAuthUID, appName, fcmToken },
        { onConflict: 'customerAuthUID,appName' }
      );

    if (error) throw error;
    
    return res.status(200).json({success:true, message: 'FCM token stored successfully' });
  } catch (error) {
    console.error('Error storing FCM token:', error);
    return res.status(500).json({ succes:false, error: 'Failed to store FCM token' });
  }
});

router.get("/getCustomerList/:outletId", async (req, res) => {
  const { outletId } = req.params;

  // Validate outletId
  if (!outletId || typeof outletId !== 'string') {
    return res.status(400).json({
      success: false,
      error: 'Invalid outlet ID provided'
    });
  }

  try {
    // check if outlet.hasMess is true
    const { data: outletData, error: outletError } = await supabaseInstance
      .from("Outlet")
      .select("hasMess")
      .eq("outletId", outletId)
      .maybeSingle();

    if (outletError) throw outletError;

    if (outletData?.hasMess === true) {
      const { data: messData, error: messError } = await supabaseInstance
        .from("Mess_Whitelist")
        .select("*, Customer!left (customerAuthUID,customerName, mobile, email) ")
        .eq("outletId", outletId);

      if (messError) throw messError;

      if (messData?.length > 0) {
        // formatting the data
        const formattedData = messData.map(item => ({
          ...item.Customer
        }));

        return res.status(200).json({
          success: true,
          data: formattedData,
          count: formattedData.length
        });
      }
    }

    // Query orders with customer information
    const { data: customerData, error: customerError } = await supabaseInstance
      .from("Order")
      .select(`
        customerAuthUID,
        Customer!left (
          customerName,
          mobile,
          email
        )
      `)
      .eq("outletId", outletId); // Ensure we only get orders with valid customer IDs

    // Handle database errors
    if (customerError) {
      console.error('Database error:', customerError);
      return res.status(500).json({
        success: false,
        error: 'Failed to fetch customer data'
      });
    }

    // Handle case when no data is found
    if (!customerData || customerData.length === 0) {
      return res.status(200).json({
        success: true,
        data: [],
        message: 'No customers found for this outlet'
      });
    }

    // Create a Map to store unique customer records
    const uniqueCustomersMap = new Map();
    
    // Process and transform customer data
    customerData.forEach(record => {
      if (record.customerAuthUID && record.Customer) {
        const customerInfo = {
          customerAuthUID: record.customerAuthUID,
          ...record.Customer
        };
        uniqueCustomersMap.set(record.customerAuthUID, customerInfo);
      }
    });

    // Convert Map values back to array
    const uniqueCustomers = Array.from(uniqueCustomersMap.values());

    return res.status(200).json({
      success: true,
      data: uniqueCustomers,
      count: uniqueCustomers.length
    });

  } catch (error) {
    console.error('Server error:', error);
    return res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error.message
    });
  }
});

// ================================================Customer Addresses================================================


/**
 * @description Fetches all addresses for a customer by their auth UID.
 * @route GET /getCustomerAddresses/:customerAuthUID
 * @param {string} customerAuthUID - The unique identifier for the customer.
 * @returns {Object} - List of customer addresses.
 */
router.get("/getCustomerAddresses/:customerAuthUID", async (req, res) => {
  const { customerAuthUID } = req.params;
  try {
    const { data, error } = await supabaseInstance
      .from("Customer_Addresses")
      .select(`
        customer_address_id,
        label,
        customerAuthUID,
        address_line_1,
        address_line_2,
        locality,
        city,
        state,
        pincode,
        delivery_instructions
        `)
      .eq("customerAuthUID", customerAuthUID);

    if (error) throw error;

    const response = data.map(address => ({
      customer_address_id: address.customer_address_id,
      label: address.label,
      customerAuthUID: address.customerAuthUID,
      address_line_1: address.address_line_1 || '',
      address_line_2: address.address_line_2 || undefined,
      locality: address.locality || '',
      city: address.city || '',
      state: address.state || '',
      pincode: address.pincode || '',
      delivery_instructions: address.delivery_instructions || undefined
    }));

    return res.status(200).json({
      success: true,
      data: response,
      message: "Customer addresses fetched successfully"
    });
  } catch (error) {
    console.error('Server error:', error);
    return res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error.message
    });
  }
});

/**
 * @description Get the details of a specific customer address by its ID.
 * @route GET /getCustomerAddress/:customer_address_id
 * @param {string} customer_address_id - The unique identifier for the customer address.
 * @return {Object} - single address details
 */
router.get("/getCustomerAddress/:customer_address_id", async (req, res) => {
  const { customer_address_id } = req.params;
  if(!customer_address_id) {
    return res.status(400).json({
      success: false,
      error: 'Missing required fields',
      message: `Missing ${!customer_address_id ? 'customer_address_id' : ''}`
    });
  }
  try {
    const { data, error } = await supabaseInstance
      .from("Customer_Addresses")
      .select("*")
      .eq("customer_address_id", customer_address_id)
      .single();

    if (error) throw error;

    return res.status(200).json({
      success: true,
      data: data,
      message: "Customer address fetched successfully"
    });
  } catch (error) {
    console.error('Server error:', error);
    return res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error.message
    });
  }
});

/**
 * @description Add a new address of a customer
 * @route POST /addCustomerAddress
 * @param {string} customerAuthUID - The unique identifier for the customer.
 * @param {string} label - Label for the address.
 * @param {string} address_line_1 - First line of the address.
 * @param {string} address_line_2 - Second line of the address (optional).
 * @param {string} locality - Locality of the address.
 * @param {string} city - City of the address.
 * @param {string} state - State of the address.
 * @param {string} pincode - Pincode of the address.
 * @param {string} delivery_instructions - Delivery instructions for the address.
 * @return {Object} - Success message or error message.
 */
router.post("/addCustomerAddress", async (req, res) => {
  const { customerAuthUID, label, address_line_1, address_line_2, locality, city, state, pincode, delivery_instructions } = req.body;
  
  if(!customerAuthUID || !label || !address_line_1 || !locality || !city || !state || !pincode ) {
    return res.status(400).json({
      success: false,
      error: 'Missing required fields',
      message: `Missing ${!customerAuthUID ? 'customerAuthUID' : ''}${!label ? 'label' : ''}${!address_line_1 ? 'address_line_1' : ''}${!locality ? 'locality' : ''}${!city ? 'city' : ''}${!state ? 'state' : ''}${!pincode ? 'pincode' : ''}}`
    });
  }
  try {
    const insertData = {
      customerAuthUID : customerAuthUID ? customerAuthUID : undefined,
      label : label ? label : undefined,
      address_line_1 : address_line_1 ? address_line_1 : undefined,
      address_line_2 : address_line_2 ? address_line_2 : undefined,
      locality : locality ? locality : undefined,
      city : city ? city : undefined,
      state : state ? state : undefined,
      pincode : pincode ? pincode : undefined,
      delivery_instructions : delivery_instructions ? delivery_instructions : undefined,
    };
    const { error } = await supabaseInstance
      .from("Customer_Addresses")
      .insert(insertData);

    if (error) throw error;

    return res.status(200).json({
      success: true,
      message: "Customer address added successfully"
    });
  } catch (error) {
    console.error('Server error:', error);
    return res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error.message
    });
  }
});

/**
 * @description Update an existing address of a customer
 * @route POST /updateCustomerAddress/:customer_address_id
 * @param {string} customer_address_id - The unique identifier for the customer address.
 * @param {string} label - Label for the address.
 * @param {string} address_line_1 - First line of the address.
 * @param {string} address_line_2 - Second line of the address.
 * @param {string} locality - Locality of the address.
 * @param {string} city - City of the address.
 * @param {string} state - State of the address.
 * @param {string} pincode - Pincode of the address.
 * @param {string} delivery_instructions - Delivery instructions for the address.
 * @return {Object} - Success message or error message.
 */
router.post("/updateCustomerAddress/:customer_address_id", async (req, res) => {
  const { customer_address_id } = req.params;
  const { label, address_line_1, address_line_2, locality, city, state, pincode, delivery_instructions } = req.body;
  if(!customerAuthUID || !label || !address_line_1 || !locality || !city || !state || !pincode ) {
    return res.status(400).json({
      success: false,
      error: 'Missing required fields',
      message: `Missing ${!customerAuthUID ? 'customerAuthUID' : ''}${!label ? 'label' : ''}${!address_line_1 ? 'address_line_1' : ''}${!locality ? 'locality' : ''}${!city ? 'city' : ''}${!state ? 'state' : ''}${!pincode ? 'pincode' : ''}}`
    });
  }
  try {
    const updateData = {
      label : label ? label : undefined,
      address_line_1: address_line_1 ? address_line_1 : undefined,
      address_line_2: address_line_2 ? address_line_2 : undefined,
      locality: locality ? locality : undefined,
      city: city ? city : undefined,
      state: state ? state : undefined,
      pincode: pincode ? pincode : undefined,
      delivery_instructions: delivery_instructions ? delivery_instructions : undefined
    };
    const { error } = await supabaseInstance
      .from("Customer_Addresses")
      .update(updateData)
      .eq("customer_address_id", customer_address_id);

    if (error) throw error;

    return res.status(200).json({
      success: true,
      message: "Customer address updated successfully"
    });
  } catch (error) {
    console.error('Server error:', error);
    return res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error.message
    });
  }
});

/**
 * @description Delete a customer address by its ID.
 * @route POST /deleteCustomerAddress
 * @param {string} customer_address_id - The unique identifier for the customer address.
 * @return {Object} - Success message or error message.
 */
router.post("/deleteCustomerAddress", async (req, res) => {
  const { customer_address_id } = req.body;
  if(!customer_address_id) {
    return res.status(400).json({
      success: false,
      error: 'Missing required fields',
      message: `Missing ${!customer_address_id ? 'customer_address_id' : ''}`
    });
  }
  try {
    const { error } = await supabaseInstance
      .from("Customer_Addresses")
      .delete()
      .eq("customer_address_id", customer_address_id);

    if (error) throw error;

    return res.status(200).json({
      success: true,
      message: "Customer address deleted successfully"
    });
  } catch (error) {
    console.error('Server error:', error);
    return res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error.message
    });
  }
});

module.exports = router;

// const applepubliKeyURL ='https://appleid.apple.com/auth/keys';

// axios.get(applepubliKeyURL).then((response) => {
//   const applePublicKeys = response.data.keys;
//   console.log("applePublicKeys=>",applePublicKeys);
//   const decodeToken = jwt.verify('hfsdgcdvdhs',applePublicKeys[0]);
//   console.log("decodeToken=>",decodeToken)

// })

// supabaseInstance.auth.admin.updateUserById('9cfb044a-28b1-4135-afdb-2f0877c78d31', { phone: '9999999992' }).then(res => {
//   console.log("res==>", res)
// })
