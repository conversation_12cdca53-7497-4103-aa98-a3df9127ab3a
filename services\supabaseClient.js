// import { createClient } from '@supabase/supabase-js'
const createClient = require("@supabase/supabase-js").createClient
require("dotenv").config();


const supabaseUrl = process.env.SUPABASE_URL ||"https://zyxwtaeuvipslarwyrbe.supabase.co";
const supabaseAnonKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp5eHd0YWV1dmlwc2xhcnd5cmJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE2OTU3MjQ4NzksImV4cCI6MjAxMTMwMDg3OX0.SA6Vpzca_RZJcWQ5UGiAeMIMAtWaNsMIpO0bvUS3TL0";
const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY || "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp5eHd0YWV1dmlwc2xhcnd5cmJlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTY5NTcyNDg3OSwiZXhwIjoyMDExMzAwODc5fQ.P6HXMfa7THwE6_w3OEUkkw8bE94Gz5Wg7YIyHD8Mx5o"
const otherOptions = {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
};

const supabaseInstance = createClient(supabaseUrl, serviceRoleKey, otherOptions);
const projectName = supabaseUrl.split('.')[0].split('://')[1];
console.log("Supabase client created for project: " + projectName);
exports.supabase = supabaseInstance;