var express = require("express");
const {
  saveOrderToPetpooja,
  updateOrderStatus,
} = require("../petpooja/pushMenu");
// const moment = require("../../services/momentService").momentIndianTimeZone;
const moment = require("moment-timezone");
const { sendMobileSMS, sendEmail } = require("../../services/msf91Service");
var router = express.Router();
var supabaseInstance = require("../../services/supabaseClient").supabase;
var msg91config = require("../../configs/msg91Config");
var { requestRefund } = require("../Payment/refund");

const sendStatusUpdateNotification = require("../firebase").sendStatusUpdateNotification;
const getFCMTokenOfUser = require("../firebase").getFCMTokenOfUser;
require("dotenv").config();
const devEnv = process.env.ENV === 'dev';

const  { validateGetCartItemsStock } = require('../../validators/orderValidator');

router.post("/createOrder", async (req, res) => {
  const {
    customerAuthUID,
    outletId,
    restaurantId,
    isDineIn,
    isPickUp,
    totalPrice,
    paymentId,
    items,
    pickupTime,
    orderPriceBreakDown,
    isScheduleNow,
    txnid,
    basePrice,
    isDelivery,
    address,
    additional_Instruction,
    isCashOrder,
    isMessOrder,
    appName,
    paymentType,
    isManualOrder
  } = req.body;
  console.log('req.body', req.body);

  if(totalPrice <= 0){
    return res.status(400).json({ success: false, error: "Total price of the order cannot be zero" });
  }
  let totalOrderPrice = 0;
  for (let item of items){
    if(item.price <= 0){
      return res.status(400).json({ success: false, error: "Price of the item cannot be zero" });
    }
    totalOrderPrice += item.price * item.qty;
  }
  
  try {
    let menuItems;
    // Stock validation for countable items
    const itemIds = items.map(item => item.id).filter(id => id);
    if (itemIds.length > 0) {
      const { data: menuItemsData, error: menuError } = await supabaseInstance
        .from("Menu_Item")
        .select("itemid, itemname, countable, quantity_in_stock, status")
        .in("itemid", itemIds);
      
      if (menuError) throw menuError;
      
      menuItems = menuItemsData || [];
      
      // Check if any countable items have insufficient stock
      for (const item of items) {
        const menuItem = menuItems.find(mi => mi.itemid === item.id);
        if (menuItem) {
          // Check if item is disabled
          if (!menuItem.status) {
            return res.status(400).json({ 
              success: false, 
              error: `Item "${menuItem.itemname}" is currently unavailable` 
            });
          }
          
          // Check stock for countable items
          /* Removing stock validation till we dont get solution of race condition
          if (menuItem.countable) {
            if (menuItem.quantity_in_stock < item.qty) {
              return res.status(400).json({ 
                success: false, 
                error: `Insufficient stock for "${menuItem.itemname}". Available: ${menuItem.quantity_in_stock}, Requested: ${item.qty}` 
              });
            }
          }
          */
        }
      }
    }

    // check for autoaccept order from outlet table
    const { data: outletData, error: outletError } = await supabaseInstance
      .from("Outlet")
      .select("autoAcceptOrder")
      .eq("outletId", outletId)
      .maybeSingle();
    
    if (devEnv) console.log('outletData', outletData);
    
    if (outletError) throw outletError;
    
    const orderStatusId = outletData.autoAcceptOrder ? 1 : isMessOrder ? 5 : 0;

    const orderOTP = generateOTP();
    const clearanceOtp = generateOTP();

    let clearanceRequested = false;
    let clearanceCompleted = false;
    let clearanceVerifiedAt = null;
    let clearanceRequestedAt = null;
    
    if(isManualOrder){
      clearanceRequested = true;
      clearanceCompleted = true;
      clearanceVerifiedAt = new Date().toISOString();
      clearanceRequestedAt = new Date().toISOString();
    }

    const { data, error } = await supabaseInstance
      .from("Order")
      .insert({
        customerAuthUID,
        outletId,
        isDineIn,
        isPickUp,
        totalPrice,
        paymentId,
        orderPriceBreakDown,
        orderOTP,
        clearanceOtp,
        clearanceRequested,
        clearanceCompleted,
        clearanceVerifiedAt,
        clearanceRequestedAt,
        isScheduleNow,
        txnid,
        basePrice,
        isDelivery,
        additional_Instruction,
        isCashOrder,
        isMessOrder,
        appName,
        paymentType,
        orderStatusId,
      })
      .select("*,outletId(outletName,logo,outletId), customerAuthUID(*)");

    if (data) {
      console.log("\n\n\n\n\n\n\n\n\n\n\ndata", data)
      const orderId = data[0]?.orderId;
      let orderData = [];
      for (let item of items) {
        let calculatedPrice = 0;
        
        if(item.extra && item.extra.length > 0){
          calculatedPrice = Number(item.extra[0].price);
        }
        else{
          calculatedPrice = Number(item.price);
        }

        console.log('cal price on 72 : ' , calculatedPrice);
        
        let addons = [];
        if(item.addons){
          for (const [key, value] of Object.entries(item.addons)) {
            console.log(value.selectedItemsAddonData.map((item) => item.price));
            // [ '40', '42' ] is the output of console.log
            calculatedPrice += Number(value.selectedItemsAddonData.reduce((a, c) => a + Number(c.price), 0));
            console.log('cal price on 78 : ' , calculatedPrice);

            
            addons.push({
              addonGroupId: key,
              selectedAddonGroupItems: value.selectedItemsAddonData,
              itemId: item.id? item.id : null,
              addonGroupName: value.addonGroupName ? value.addonGroupName : null,
            });
          }
        }
        item.price = calculatedPrice;
        calculatedPrice = calculatedPrice * Number(item.qty);
        console.log('\n\n\ncalculatedPrice : ', calculatedPrice);
        

        let orderitemData = await supabaseInstance
          .from("Order_Item")
          .insert({
            orderId: orderId,
            itemId: item.id,
            quantity: item.qty,
            itemPrice: item.price,
            calculatedPrice: calculatedPrice,
            variation: item.extra && item.extra.length > 0 ? item.extra[0].variationId : null,
            addons: addons?.length > 0 ? addons : null,
          })
          .select("*");
        orderData.push(orderitemData.data);
      }
      
      // Reduce stock for countable items after successful order creation
      try {
        for (const item of items) {
          const menuItem = menuItems.find(mi => mi.itemid === item.id);
          if (menuItem && menuItem.countable) {
            const newStock = menuItem.quantity_in_stock - item.qty;
            
            // Update stock and auto-disable if reaches 0
            const updateData = {
              quantity_in_stock: Math.max(0, newStock),
              status: newStock <= 0 ? false : true // Auto-disable if stock reaches 0
            };
            
            const { error: stockUpdateError } = await supabaseInstance
              .from("Menu_Item")
              .update(updateData)
              .eq("itemid", item.id);
            
            if (stockUpdateError) {
              console.error(`Failed to update stock for item ${item.id}:`, stockUpdateError);
              // Log error but don't fail the order since items are already created
            }
          }
        }
      } catch (stockError) {
        console.error('Error updating stock:', stockError);
        // Log error but don't fail the order since items are already created
      }
      
      const orderScheduleData = await supabaseInstance
        .from("Order_Schedule")
        .insert({
          orderId: orderId,
          scheduleDate: pickupTime.orderDate,
          scheduleTime: pickupTime.time,
        })
        .select("*");

      if (isDelivery === true) {
        const deliveryResponse = await supabaseInstance
          .from("DeliveryAddress")
          .insert({
            orderId: orderId,
            outletId,
            customerAuthUID,
            address: address,
          })
          .select("*");
      }

      // Handle RSVP registration for mess orders
      if (isMessOrder) {
        try {
          const mealTypeIds = items
            .filter(item => item.Menu_Item?.isMessItem)
            .map(item => {
              if (item.Menu_Item.itemname === "Breakfast") return 1;
              if (item.Menu_Item.itemname === "Lunch") return 2;
              if (item.Menu_Item.itemname === "High Tea") return 3;
              if (item.Menu_Item.itemname === "Dinner") return 4;
              return null;
            })
            .filter(id => id !== null);

          if (mealTypeIds.length > 0) {
            const rsvpResponse = await fetch(`https://api.mealpe.app:8055/mess/rsvp/cncRSVP`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                customerAuthUID,
                mealTypeIds,
                outletId,
                date: pickupTime.orderDate
              })
            });

            const rsvpData = await rsvpResponse.json();
            
            if (!rsvpData.success) {
              console.error('RSVP registration failed:', rsvpData);
              // Don't throw error here, just log it. The order is still valid
            }
          }
        } catch (rsvpError) {
          console.error('Error in RSVP registration:', rsvpError);
          // Don't throw error here, just log it. The order is still valid
        }
      }

      saveOrderToPetpooja(req, orderId)
        .then(async (saveOrderToPetpoojaResponse) => {
          // console.log('.then block ran: ', saveOrderToPetpoojaResponse.data);
          // const getOrderDetailsAfterTrigger = await supabaseInstance.from("Order").select("*").eq("orderId", data.orderId).maybeSingle();
          console.log({
            customerauthuid: customerAuthUID,
            targate_date: pickupTime.orderDate,
          });

          // // const sendMobileSMSResponse = await sendMobileSMS(data[0].mobile, msg91config.config.order_confirmation_template_id);
          // const sendMobileSMSResponse = await sendMobileSMS([{ mobiles: data[0]?.customerAuthUID?.mobile, name: data[0]?.customerAuthUID?.customerName, orderid: orderId }], msg91config.config.order_confirmation_template_id);
          // console.log("sendMobileSMSResponse => ", sendMobileSMSResponse);

          // const _email_to = [{name: 'Customer', email: data[0].customerAuthUID.email}];
          // const _email_cc =  []
          // const _email_bcc =  []
          // const _template_id =msg91config.config.email_otp_template_id

          // const sendEmailResponse = await sendEmail(_email_to, _email_cc, _email_bcc, {}, _template_id);
          // console.log("sendEmailResponse => ", sendEmailResponse);

          const getOrderDetailsAfterTrigger = await supabaseInstance
            .rpc("get_live_customer_orders", {
              customerauthuid: customerAuthUID,
              targate_date: pickupTime.orderDate,
            })
            .eq("orderid", orderId)
            .maybeSingle();

          console.log(
            "getOrderDetailsAfterTrigger",
            getOrderDetailsAfterTrigger
          );

          // assign the orderId to Wallet_Transaction table for wallet orders
          if(paymentType === "wallet"){
          await supabaseInstance.from("Wallet_Transaction").update({ orderId }).eq("walletTransactionId", txnid);
          }

          // Emit socket event for new order notification
            const io = req.app.get('io');
            console.log("io", io ? "true" : "io is not defined");
            console.log("io.emitNewOrder", io.emitNewOrder? "true" : "io.emitNewOrder is not defined");
          if (io && io.emitNewOrder) {
            let orderData = await supabaseInstance
              .rpc("get_orders_for_outlet", { outlet_uuid: outletId })
              .eq("order_id", orderId)
              .select("*")
              .maybeSingle();
            
            if (isMessOrder) {
              console.log(`Emitting ready order notification for outlet ${outletId}`);
              io.emitReadyOrder(outletId, orderData.data);
            } else {
              console.log(`Emitting new order notification for outlet ${outletId}`);
              io.emitNewOrder(outletId, orderData.data);
            }
            console.log(`New order notification emitted for outlet ${outletId}`);
          }
          res.status(200).json({
            success: true,
            // data: {
            //   data: getOrderDetailsAfterTrigger?.data || data,
            //   orderitemData: orderData,
            //   pickupTime: orderScheduleData.data,
            //   saveOrderToPetpooja: saveOrderToPetpoojaResponse.data
            // }
            data: getOrderDetailsAfterTrigger.data || {},
          });
        })
        .catch((err) => {
          console.error(
            "createOrder => saveOrderToPetpooja -> catch block ran: ",
            err
          );
          throw err;
        });
    } else {
      throw error;
    }
  } catch (error) {
    console.error(error);
    res.status(500).json({ success: false, error: error });
  }
});

router.get("/getAllOrder/:outletId", async (req, res) => {
  const { outletId } = req.params;
  const { page, perPage, orderType, orderSequenceId } = req.query;
  const pageNumber = parseInt(page) || 1;
  const itemsPerPage = parseInt(perPage) || 10;
  try {
    let query = supabaseInstance
      .rpc(
        "get_orders_for_outlet",
        { outlet_uuid: outletId },
        { count: "exact" }
      )
      .order("order_schedule_date", { ascending: false })
      .order("order_schedule_time", { ascending: false })
      .not("outlet_id", "is", null)
      .not("order_schedule_date", "is", null)
      .not("order_schedule_time", "is", null)
      .eq("outlet_id", outletId)
      .range((pageNumber - 1) * itemsPerPage, pageNumber * itemsPerPage - 1);

    if (orderType === "dinein") {
      query = query.eq("is_dine_in", true);
    } else if (orderType === "pickup") {
      query = query.eq("is_pick_up", true);
    } else if (orderType === "delivery") {
      query = query.eq("is_delivery", true);
    }

    if (orderSequenceId) {
      query = query.ilike("order_sequence_id", `%${orderSequenceId}%`);
    }

    const { data, error, count } = await query;

    if (data) {
      const totalPages = Math.ceil(count / itemsPerPage);
      res.status(200).json({
        success: true,
        data: data,
        data: data,
        meta: {
          page: pageNumber,
          perPage: itemsPerPage,
          totalPages,
          totalCount: count,
        },
      });
    } else {
      throw error;
    }
  } catch (error) {
    console.error(error);
    res.status(500).json({ success: false, error: error });
  }
});

// router.get("/getOrder/:orderId", async (req, res) => {
//   const { orderId } = req.params;
//   try {
//     const { data, error } = await supabaseInstance
//       .from("Order")
//       .select(
//         "*,customerAuthUID(*),outletId(outletId,outletName,logo,address),DeliveryAddress(address),Order_Item(*, itemId,Menu_Item(itemname,item_image_url),Variation(*)),Order_Schedule(*),orderStatusId(*),Transaction(txnid,convenienceTotalAmount,foodGST,itemTotalPrice,packagingCharge,deliveryCharge,isGSTApplied)"
//       )
//       .eq("orderId", orderId)
//       .maybeSingle();
//     if (data) {
//       res.status(200).json({
//         success: true,
//         data: data,
//       });
//     } else {
//       throw error;
//     }
//   } catch (error) {
//     console.error(error);
//     res.status(500).json({ success: false, error: error });
//   }
// });


// updated : 2024-10-29 : cash and carry orders details
router.get("/getOrder/:orderId", async (req, res) => {
  const { orderId } = req.params;
  try {
    let { data, error } = await supabaseInstance
      .from("Order")
      .select(
        "*,customerAuthUID(*),outletId(outletId,outletName,logo,address, mobile, enableClearanceOrder, hide_cancel_after),DeliveryAddress(address),Order_Item(*, itemId,Menu_Item(isMessItem, itemname,item_image_url),Variation(name,price,variationId)),Order_Schedule(*),orderStatusId(*),Transaction(txnid,convenienceTotalAmount,foodGST,itemTotalPrice,packagingCharge,deliveryCharge,isGSTApplied)"
      )
      .eq("orderId", orderId)
      .maybeSingle();
      
    if (data) {
      // Transform the addons array
      data.isMessOrder = false;
      data.isRegularOrder = false;
      data.Order_Item.forEach((item) => {
        item.addons = item?.addons?.flatMap((addon) => 
          addon?.selectedAddonGroupItems?.map((selected) => ({
            id: selected.id,
            name: selected.name,
            price: selected.price,
            addonGroupId: addon.addonGroupId,
          }))
        );

        if(!item.Menu_Item.isMessItem){
          data.isRegularOrder = true;
        }
        if(item.Menu_Item.isMessItem){
          data.isMessOrder = true;
          if(item.Menu_Item.itemname == "Breakfast"){
            data.mealTypeId = 1;
          }
          if(item.Menu_Item.itemname == "Lunch"){
            data.mealTypeId = 2;
          }
          if(item.Menu_Item.itemname == "High Tea"){
            data.mealTypeId = 3;
          }
          if(item.Menu_Item.itemname == "Dinner"){
            data.mealTypeId = 4;
          }
        }
      });

      res.status(200).json({
        success: true,
        data: data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    console.error(error);
    res.status(500).json({ success: false, error: error });
  }
});

// updated : 2024-11-11 : filter order for whitelabled apps
router.get("/getOrderByCustomerAuthId/:customerAuthUID", async (req, res) => {
  const { customerAuthUID } = req.params;
  const {tag} = req.query;
  
  try {
    let { data, error } = await supabaseInstance
      .from("Order")
      .select(
        "*, Order_Review!left(*),outletId(outletId,headerImage,outletName,logo,campusId),Order_Item(*,Menu_Item(minimumpreparationtime)),Order_Schedule(*),orderStatusId(*))"
      )
      .eq("customerAuthUID", customerAuthUID)
      .order("created_at", { ascending: false });
    if (data) {

      // if tag is present then filter the orders based on the campus tag
      if(tag) {
        const {data: campusData, error: campusError} = await supabaseInstance
        .from("Campus")
        .select("campusId,tag")
        .eq("tag", tag);

        if(campusError) throw campusError;

        let campusIds = campusData.map((campus) => campus.campusId);
        console.log('campusIds', campusIds);
        data = data.filter((order) => campusIds.includes(order.outletId.campusId));
        
      }

      res.status(200).json({
        success: true,
        data: data.map((m) => ({
          ...m,
          isReview: Boolean(m?.Order_Review?.length > 0),
          totalItems: m?.Order_Item?.reduce((a, c) => a + c.quantity, 0),
        })),
      });
    } else {
      throw error;
    }
  } catch (error) {
    console.error(error);
    res.status(500).json({ success: false, error: error });
  }
});

// v2 with pagination
router.get("/v2/getOrderByCustomerAuthId/:customerAuthUID", async (req, res) => {
  const { customerAuthUID } = req.params;
  const { tag, page = 1, perPage = 10 } = req.query;

  try {
    let query = supabaseInstance
      .from("Order")
      .select(`
        *,
        Order_Review!left(*),
        outletId!inner(
          outletId,
          headerImage,
          outletName,
          logo,
          campusId
        ),
        Order_Item(*,Menu_Item(minimumpreparationtime)),
        Order_Schedule(*),
        orderStatusId(*)
      `, { count: "exact" })
      .eq("customerAuthUID", customerAuthUID)
      .order("created_at", { ascending: false });

    // If tag is present, first get the campus IDs
    if (tag) {
      const { data: campusData, error: campusError } = await supabaseInstance
        .from("Campus")
        .select("campusId,tag")
        .eq("tag", tag);

      if (campusError) throw campusError;

      const campusIds = campusData.map((campus) => campus.campusId);
      
      // Add filter for campus IDs
      query = query.in('outletId.campusId', campusIds);
    }

    // Apply pagination after all filters
    const { data, error, count } = await query
      .range((page - 1) * perPage, page * perPage - 1);
    
    if(error) throw error;
    
    if (data) {
      const totalPages = Math.ceil(count / perPage);
      res.status(200).json({
        success: true,
        data: data.map((m) => ({
          ...m,
          isReview: Boolean(m?.Order_Review?.length > 0),
          totalItems: m?.Order_Item?.reduce((a, c) => a + c.quantity, 0),
        })),
        metadata: {
          pagination: {
            page: parseInt(page),
            perPage: parseInt(perPage),
            totalPages,
            totalCount: count
          }
        },
      });
    } else {
      throw error;
    }
  } catch (error) {
    console.error(error);
    res.status(500).json({ success: false, error: error });
  }
});

// router.get("/getUpcomingOrder/:outletId", async (req, res) => {
//   const { outletId } = req.params;
//   const { orderType, orderStatusId, orderSequenceId } = req.query;
//   const formattedTime = moment().format('HH:mm:ss');
//   const formattedTimeAdd2Hours = moment(formattedTime, 'HH:mm:ss').add(45, 'minute').format('HH:mm:ss');
//   console.log("formattedTimeAdd2Hours",formattedTimeAdd2Hours)

//   try {
//     // let currentDate = moment().toJSON().slice(0, 10);
//     let currentDate = moment().format('YYYY-MM-DD');
//     let query = supabaseInstance.rpc("get_orders_for_outlet", {outlet_uuid: outletId})
//     .gte("order_schedule_date", currentDate)
//     .gt("order_schedule_time", formattedTimeAdd2Hours)
//     .eq("order_status_id",0)
//     .order("order_schedule_date",{ascending:false})
//     .order("order_schedule_time",{ascending:false})
//     .order("order_schedule_time", { ascending: false })

//     if (orderType === "dinein") {
//       query = query.eq("is_dine_in", true)
//     } else if (orderType === "pickup") {
//         query = query.eq("is_pick_up", true)
//     } else if (orderType === "delivery") {
//       query = query.eq("is_delivery", true)
//     }

//     // if (orderStatusId) {
//     //   const _orderStatusId = orderStatusId.split(',');
//     //   if (_orderStatusId.length > 0) {
//     //     query = query.in('order_status_id', _orderStatusId);
//     //   }
//     // }

//     if (orderSequenceId) {
//       query = query.ilike("order_sequence_id", `%${orderSequenceId}%`);
//     }
//     const { data, error, } = await query;

//     if (data) {
//       res.status(200).json({
//         success: true,
//         data: data
//       });
//     } else {
//       throw error
//     }
//   } catch (error) {
//     console.log(error)
//     res.status(500).json({ success: false, error: error });
//   }
// });

// router.get("/getCurrentOrder/:outletId", async (req, res) => {
//   const { outletId } = req.params;
//   const { orderType, orderStatusId, orderSequenceId } = req.query;

//   const formattedTime = moment().format('HH:mm:ss');
//   const formattedTimeAdd2Hours = moment(formattedTime, 'HH:mm:ss').add(45, 'minute').format('HH:mm:ss');

//   try {
//     // let currentDate = moment().toJSON().slice(0, 10);
//     let currentDate = moment().format('YYYY-MM-DD');
//     console.log("currentDate",currentDate)
//     let query = supabaseInstance.rpc("get_orders_for_outlet", {outlet_uuid: outletId})
//       .eq("order_schedule_date", currentDate)
//       // .gte("order_schedule_time", formattedTime)
//       .lte("order_schedule_time", formattedTimeAdd2Hours)
//       .eq("order_status_id",0)
//       .order("order_schedule_date",{ascending:false})
//       .order("order_schedule_time",{ascending:false});

//     if (orderType === "dinein") {
//       query = query.eq("is_dine_in", true)
//     } else if (orderType === "pickup") {
//         query = query.eq("is_pick_up", true)
//     } else if (orderType === "delivery") {
//       query = query.eq("is_delivery", true)
//     }

//     // if (orderStatusId) {
//     //   const _orderStatusId = orderStatusId.split(',');
//     //   if (_orderStatusId.length > 0) {
//     //     query = query.in('order_status_id', _orderStatusId);
//     //   }
//     // }

//     if (orderSequenceId) {
//       query = query.ilike("order_sequence_id", `%${orderSequenceId}%`);
//     }

//     const { data, error, } = await query;

//     if (data) {
//       res.status(200).json({
//         success: true,
//         data: data
//       });
//     } else {
//       throw error
//     }
//   } catch (error) {
//     console.log(error)
//     res.status(500).json({ success: false, error: error });
//   }
// });

router.post("/getPendingOrderCountForOutlets", async (req, res) => {
  const { outletIds } = req.body;
  
  if (!outletIds) {
    return res.status(400).json({ success: false, error: "outletIds are required" });
  }

  try {
    const outletIdsArray = outletIds
    
    const results = await Promise.all(outletIdsArray?.map(async (outletId) => {
      const { data, error } = await supabaseInstance
        .rpc("get_orders_for_outlet", { outlet_uuid: outletId })
        .eq("order_status_id", 0)
        .order("order_schedule_date", { ascending: false })
        .order("order_schedule_time", { ascending: false });

      if (error) throw error;

        return {
          outletId,
          orders : data?.length
        };
      }
    ));

    res.status(200).json({
      success: true,
      data: results,
    });
    
  } catch (error) {
    console.error(error);
    return res.status(500).json({ success: false, error: error });
  }
});

router.post("/getPendingOrdersListForOutlets", async (req, res) => {
  const { outletIds } = req.body;
  if (!outletIds || !Array.isArray(outletIds) || outletIds?.length === 0) {
    return res.status(400).json({ success: false, error: "Valid outletIds array is required" });
  }

  try {
    const results = await Promise.all(outletIds?.map(async (outletId) => {
      const { data: outletData, error: outletError } = await supabaseInstance
        .from("Outlet")
        .select("outletName, campusId(campusName)")
        .eq("outletId", outletId)
        .single();

      if (outletError) throw outletError;

      const { data: orders, error: ordersError } = await supabaseInstance
        .rpc("get_orders_for_outlet", { outlet_uuid: outletId })
        .eq("order_status_id", 0)
        .order("order_schedule_date", { ascending: false })
        .order("order_schedule_time", { ascending: false });

      if (ordersError) throw ordersError;

      return orders.map(order => ({
        ...order,
        outletId,
        outletName: outletData?.outletName,
        campusName: outletData?.campusId?.campusName
      }));
    }));

    const allOrders = results.flat();

    // sort orders by order_schedule_date and order_schedule_time
    allOrders.sort((a, b) => {
      const dateA = new Date(a?.order_schedule_date);
      const dateB = new Date(b?.order_schedule_date);
      const timeA = new Date(a?.order_schedule_time);
      const timeB = new Date(b?.order_schedule_time);

      if (dateA > dateB) return -1;
      if (dateA < dateB) return 1;
      if (timeA > timeB) return -1;
      if (timeA < timeB) return 1;
      return 0;
    });

    res.status(200).json({
      success: true,
      data: allOrders
    });
  } catch (error) {
    console.error(error);
    return res.status(500).json({ success: false, error: error });
  }
});

router.get("/getPendingOrder/:outletId", async (req, res) => {
  const { outletId } = req.params;
  try {
    let query = supabaseInstance
      .rpc("get_orders_for_outlet", { outlet_uuid: outletId })
      .eq("order_status_id", 0)
      .order("order_schedule_date", { ascending: false })
      .order("order_schedule_time", { ascending: false });

    const { data, error } = await query;

    if (data) {
      // Restructure the menu_item array to transform addons
      const transformedData = data?.map((order) => ({
        ...order,
        menu_item: order.menu_item?.map((item) => ({
          ...item,
          addons: item?.addons?.map((addon) => 
            addon?.selectedAddonGroupItems?.map((addonItem) => ({
              id: addonItem?.id,
              name: addonItem?.name,
              price: addonItem?.price
            }))
          ).flat()
        }))
      }));

      const {data:outletData , error:outletError} = await supabaseInstance
      .from("Outlet")
      .select("outletName, campusId(campusName)")
      .eq("outletId", outletId)
      .single();

      if (outletError) throw outletError;

      const result = transformedData.map(order => ({
        ...order,
        outletName: outletData?.outletName,
        campusName: outletData?.campusId?.campusName
      }));

      // add the outletName and campusName to the response using rest operator
      res.status(200).json({
        success: true,
        data: result,
      });
    } else {
      throw error;
    }
  } catch (error) {
    console.error(error);
    res.status(500).json({ success: false, error: error });
  }
});


// router.get("/getPendingOrder/:outletId", async (req, res) => {
//   const { outletId } = req.params;
//   try {
//     let query = supabaseInstance
//       .rpc("get_orders_for_outlet_test", { outlet_uuid: outletId })
//       .eq("order_status_id", 0)
//       .order("order_schedule_date", { ascending: false })
//       .order("order_schedule_time", { ascending: false });

//     const { data, error } = await query;

//     if (data) {
//       res.status(200).json({
//         success: true,
//         data: data,
//       });
//     } else {
//       throw error;
//     }
//   } catch (error) {
//     console.error(error);
//     res.status(500).json({ success: false, error: error });
//   }
// });

router.get("/getCurrentOrder/:outletId", async (req, res) => {
  const { outletId } = req.params;

  try {
    let query = supabaseInstance
      .rpc("get_orders_for_outlet", { outlet_uuid: outletId })
      .in("order_status_id", [1, 2, 3])
      .order("order_schedule_date", { ascending: false })
      .order("order_schedule_time", { ascending: false });

    const { data, error } = await query;

    if (data) {
      // Restructure the menu_item array to transform addons
      const transformedData = data?.map((order) => ({
        ...order,
        menu_item: order.menu_item?.map((item) => ({
          ...item,
          addons: item?.addons
            ?.map((addon) =>
              addon?.selectedAddonGroupItems?.map((addonItem) => ({
                id: addonItem?.id,
                name: addonItem?.name,
                price: addonItem?.price,
              }))
            )
            .flat(),
        })),
      }));
      
      res.status(200).json({
        success: true,
        data: transformedData,
      });
    } else {
      throw error;
    }
  } catch (error) {
    console.log(error);
    res.status(500).json({ success: false, error: error });
  }
});

// router.get("/getLiveOrders/:outletId", async (req, res) => {
//   const { outletId } = req.params;
//   const { orderType, orderStatusId, orderSequenceId } = req.query;

//   // const now = new Date();
//   // const formattedTime = moment(now).format('HH:mm:ss');
//   // const formattedTimeAdd2Hours = moment(formattedTime, 'HH:mm:ss').add(45, 'minute').format('HH:mm:ss');

//   try {
//     // let currentDate = moment().toJSON().slice(0, 10);
//     let currentDate = moment().format('YYYY-MM-DD');

//     let query = supabaseInstance.rpc("get_orders_for_outlet", {outlet_uuid: outletId})
//       .eq("order_schedule_date", currentDate)
//       .in("order_status_id",[1,2,3])
//       .order("order_schedule_date",{ascending:false})
//       .order("order_schedule_time",{ascending:false});

//     if (orderType === "dinein") {
//       query = query.eq("is_dine_in", true)
//     } else if (orderType === "pickup") {
//         query = query.eq("is_pick_up", true)
//     } else if (orderType === "delivery") {
//       query = query.eq("is_delivery", true)
//     }

//     // if (orderStatusId) {
//     //   const _orderStatusId = orderStatusId.split(',');
//     //   if (_orderStatusId.length > 0) {
//     //     query = query.in('order_status_id', _orderStatusId);
//     //   }
//     // }

//     if (orderSequenceId) {
//       query = query.ilike("order_sequence_id", `%${orderSequenceId}%`);
//     }

//     const { data, error, } = await query;

//     if (data) {
//       res.status(200).json({
//         success: true,
//         data: data
//       });
//     } else {
//       throw error
//     }
//   } catch (error) {
//     console.log(error)
//     res.status(500).json({ success: false, error: error });
//   }
// });

// router.get("/getReadyOrders/:outletId", async (req, res) => {
//   const { outletId } = req.params;
//   const { orderType, orderStatusId, orderSequenceId } = req.query;

//   // const now = new Date();
//   // const formattedTime = moment(now).format('HH:mm:ss');
//   // const formattedTimeAdd2Hours = moment(formattedTime, 'HH:mm:ss').add(45, 'minute').format('HH:mm:ss');

//   try {
//     // let currentDate = moment().toJSON().slice(0, 10);
//     let currentDate = moment().format('YYYY-MM-DD');

//     let query = supabaseInstance.rpc("get_orders_for_outlet", {outlet_uuid: outletId})
//       .eq("order_schedule_date", currentDate)
//       .in("order_status_id",[4,5])
//       .order("order_schedule_date",{ascending:false})
//       .order("order_schedule_time",{ascending:false});

//     if (orderType === "dinein") {
//       query = query.eq("is_dine_in", true)
//     } else if (orderType === "pickup") {
//         query = query.eq("is_pick_up", true)
//     } else if (orderType === "delivery") {
//       query = query.eq("is_delivery", true)
//     }

//     // if (orderStatusId) {
//     //   const _orderStatusId = orderStatusId.split(',');
//     //   if (_orderStatusId.length > 0) {
//     //     query = query.in('order_status_id', _orderStatusId);
//     //   }
//     // }

//     if (orderSequenceId) {
//       query = query.ilike("order_sequence_id", `%${orderSequenceId}%`);
//     }

//     const { data, error, } = await query;

//     if (data) {
//       res.status(200).json({
//         success: true,
//         data: data
//       });
//     } else {
//       throw error
//     }
//   } catch (error) {
//     console.log(error)
//     res.status(500).json({ success: false, error: error });
//   }
// });

router.get("/getLiveOrders/:outletId", async (req, res) => {
  const { outletId } = req.params;
  try {
    let query = supabaseInstance
      .rpc("get_orders_for_outlet", { outlet_uuid: outletId })
      .eq("order_status_id", 4)
      .order("order_schedule_date", { ascending: false })
      .order("order_schedule_time", { ascending: false });

      const { data, error } = await query;

      if (data) {
        // Restructure the menu_item array to transform addons
        const transformedData = data?.map((order) => ({
          ...order,
          menu_item: order.menu_item?.map((item) => ({
            ...item,
            addons: item?.addons?.map((addon) => 
              addon?.selectedAddonGroupItems?.map((addonItem) => ({
                id: addonItem?.id,
                name: addonItem?.name,
                price: addonItem?.price
              }))
            ).flat()
          }))
        }));
  
        res.status(200).json({
          success: true,
          data: transformedData,
        });
      } else {
        throw error;
      }
  } catch (error) {
    console.log(error);
    res.status(500).json({ success: false, error: error });
  }
});

router.get("/getReadyOrders/:outletId", async (req, res) => {
  const { outletId } = req.params;

  try {
    let query = supabaseInstance
      .rpc("get_orders_for_outlet", { outlet_uuid: outletId })
      .eq("order_status_id", 5)
      .order("order_schedule_date", { ascending: false })
      .order("order_schedule_time", { ascending: false });

    let { data, error } = await query;

    if (data) {
// add the transaction details to the response. fetch from Transaction Table. get txnid from order table using order_id
      const response = await Promise.all(data.map(async (order) => {
        const { data: transactionData, error: transactionError } = await supabaseInstance
          .from("Order") 
          .select("txnid")
          .eq("orderId", order.order_id)
          .single();
        
        if (transactionError) {
          console.log(`Error fetching transaction data for order ${order.order_id}: ${JSON.stringify(transactionError, null, 2)}`);
        };
        
        if (transactionData) {
          return {
            ...order,
            txnid: transactionData.txnid
          }
        }
      }));

      res.status(200).json({
        success: true,
        data: response,
      });
    } else {
      throw error;
    }
  } catch (error) {
    console.log(error);
    res.status(500).json({ success: false, error: error });
  }
});

// router.get("/getHistoryOrders/:outletId", async (req, res) => {
//   const { outletId } = req.params;
//   const { orderType, orderStatusId, orderSequenceId, startDate, endDate,page, perPage  } = req.query;
//   const pageNumber = parseInt(page) || 1;
//   const itemsPerPage = parseInt(perPage) || 10;

//   try {
//     const formattedTime = moment().format('HH:mm:ss');
//     // let currentDate = moment().toJSON().slice(0, 10);
//     let currentDate = moment().format('YYYY-MM-DD');
//     let query = supabaseInstance.rpc("get_orders_for_outlet", {outlet_uuid: outletId},{count:"exact"})
//     // .lte("order_schedule_date", currentDate)
//     // .lt("order_schedule_time",formattedTime)
//     .order("order_schedule_date",{ascending:false})
//     .order("order_schedule_time",{ascending:false})
//     .or(`order_schedule_date.lte.${currentDate},and(order_schedule_date.eq.${currentDate},order_schedule_time.lte.${formattedTime})`)
//     .range((pageNumber - 1) * itemsPerPage, pageNumber * itemsPerPage - 1)

//     // "Order_Schedule"."scheduleDate" <= '2023-09-06' OR ("Order_Schedule"."scheduleDate" = '2023-09-06' AND "Order_Schedule"."scheduleTime" <= '12:03:00');

//     if (orderType === "dinein") {
//       query = query.eq("is_dine_in", true)
//     } else if (orderType === "pickup") {
//         query = query.eq("is_pick_up", true)
//     } else if (orderType === "delivery") {
//       query = query.eq("is_delivery", true)
//     }

//     if (orderStatusId) {
//       const _orderStatusId = orderStatusId.split(',');
//       if (_orderStatusId.length > 0) {
//         query = query.in('order_status_id', _orderStatusId);
//       }
//     }

//     if (orderSequenceId) {
//       query = query.ilike("order_sequence_id", `%${orderSequenceId}%`);
//     }

//     if (startDate && endDate ) {
//       query = query.gte("order_schedule_date",startDate).lte("order_schedule_date",endDate);
//     }

//     const { data, error,count } = await query;
//     if (data) {
//       const totalPages = Math.ceil(count / itemsPerPage);
//       res.status(200).json({
//         success: true,
//         data: data, meta: {
//           page: pageNumber,
//           perPage: itemsPerPage,
//           totalPages,
//           totalCount: count,
//         },
//       });
//     } else {
//       throw error
//     }
//   } catch (error) {
//     console.log(error)
//     res.status(500).json({ success: false, error: error });
//   }
// });

router.get("/getHistoryOrders/:outletId", async (req, res) => {
  const { outletId } = req.params;
  const {
    orderSequenceId,
    startDate,
    endDate,
    page,
    perPage,
    orderType,
    sortType,
    visibility,
customerName,
  } = req.query;
  const pageNumber = parseInt(page) || 1;
  const itemsPerPage = parseInt(perPage) || 10;

  try {
    let query = supabaseInstance
      .rpc(
        "get_outlet_orders_v2",
        { outlet_uuid: outletId },
        { count: "exact" }
      )
      .eq("order_status_id", 10)
      // .order("order_schedule_date",{ascending:false})
      // .order("order_schedule_time",{ascending:false})
      .range((pageNumber - 1) * itemsPerPage, pageNumber * itemsPerPage - 1);

    if (orderType === "dinein") {
      query = query.eq("is_dine_in", true);
    } else if (orderType === "pickup") {
      query = query.eq("is_pick_up", true);
    } else if (orderType === "delivery") {
      query = query.eq("is_delivery", true);
    }

    if (sortType === "ascending") {
      query = query
        .order("order_schedule_date", { ascending: true })
        .order("order_schedule_time", { ascending: true });
    } else if (sortType === "descending") {
      query = query
        .order("order_schedule_date", { ascending: false })
        .order("order_schedule_time", { ascending: false });
    } else {
      query = query
        .order("order_schedule_date", { ascending: false })
        .order("order_schedule_time", { ascending: false });
    }

    if (orderSequenceId) {
      query = query.ilike("order_sequence_id", `%${orderSequenceId}%`);
    }

    if (startDate && endDate) {
      query = query
        .gte("order_schedule_date", startDate)
        .lte("order_schedule_date", endDate);
    }

    if (!visibility) {
      query = query.eq("visibility", true);
    }

    if (customerName) {
      query = query.ilike("customer_name", `%${customerName}%`);
    }

    // order by latest updated_at
    query = query.order("updated_at", { ascending: false });

    let { data, error, count } = await query;
    if (data && data.length > 0) {
      if (data[0].outlet_data.enableClearanceOrder) {
        // if clearanceOrder is true then return only the orders with clearance_requested and clearance_completed = true
        data = data.filter((order) => order.clearance_requested && order.clearance_completed);
      }
      const totalPages = Math.ceil(count / itemsPerPage);
      res.status(200).json({
        success: true,
        data: data,
        meta: {
          page: pageNumber,
          perPage: itemsPerPage,
          totalPages,
          totalCount: count,
        },
      });
    } else {
      return res.status(200).json({
        success: true,
        data: [],
        meta: {
          page: pageNumber,
          perPage: itemsPerPage,
          totalPages: 0,
          totalCount: 0,
        },
      });
    }
  } catch (error) {
    console.log(error);
    res.status(500).json({ success: false, error: error });
  }
});

router.post("/getHistoryPetPoojaOrders/:outletId", async (req, res) => {
  const { outletId } = req.params;
  const { orderStatusId } = req.body;
  const {
    orderSequenceId,
    startDate,
    endDate,
    page,
    perPage,
    orderType,
    sortType,
  } = req.query;
  const pageNumber = parseInt(page) || 1;
  const itemsPerPage = parseInt(perPage) || 10;

  try {
    let query = supabaseInstance
      .rpc(
        "get_orders_for_outlet",
        { outlet_uuid: outletId },
        { count: "exact" }
      )
      .not("ordersavepetpoojaid", "is", null)
      // .order("order_schedule_date",{ascending:false})
      // .order("order_schedule_time",{ascending:false})
      .range((pageNumber - 1) * itemsPerPage, pageNumber * itemsPerPage - 1);

    if (orderType === "dinein") {
      query = query.eq("is_dine_in", true);
    } else if (orderType === "pickup") {
      query = query.eq("is_pick_up", true);
    } else if (orderType === "delivery") {
      query = query.eq("is_delivery", true);
    }

    if (sortType === "ascending") {
      query = query
        .order("order_schedule_date", { ascending: true })
        .order("order_schedule_time", { ascending: true });
    } else if (sortType === "descending") {
      query = query
        .order("order_schedule_date", { ascending: false })
        .order("order_schedule_time", { ascending: false });
    } else {
      query = query
        .order("order_schedule_date", { ascending: false })
        .order("order_schedule_time", { ascending: false });
    }

    if (orderSequenceId) {
      query = query.ilike("order_sequence_id", `%${orderSequenceId}%`);
    }

    if (startDate && endDate) {
      query = query
        .gte("order_schedule_date", startDate)
        .lte("order_schedule_date", endDate);
    }

    if (orderStatusId.length > 0) {
      query = query.in("order_status_id", orderStatusId);
    } else {
      query = query;
    }

    const { data, error, count } = await query;
    if (data) {
      const totalPages = Math.ceil(count / itemsPerPage);
      res.status(200).json({
        success: true,
        data: data,
        meta: {
          page: pageNumber,
          perPage: itemsPerPage,
          totalPages,
          totalCount: count,
        },
      });
    } else {
      throw error;
    }
  } catch (error) {
    console.log(error);
    res.status(500).json({ success: false, error: error });
  }
});
// router.get("/getCancelledOrders/:outletId", async (req, res) => {
//   const { outletId } = req.params;
//   const { page, perPage,orderType } = req.query; // Extract query parameters
//   const pageNumber = parseInt(page) || 1;
//   const itemsPerPage = parseInt(perPage) || 10;
//   try {
//   //  let query =  supabaseInstance
//   //   .from("Order")
//   //   .select("*,orderStatusId(*),customerAuthUID(*),Order_Schedule(*))",{ count: "exact" })

//     let query = supabaseInstance.rpc("get_orders_for_outlet", {outlet_uuid: outletId},{count:"exact"})
//     .in('order_status_id', ['-1', '-2'])
//     .order("order_schedule_date",{ascending:false})
//     .order("order_schedule_time",{ascending:false})
//     .range((pageNumber - 1) * itemsPerPage, pageNumber * itemsPerPage - 1)

//     if (orderType === "dinein") {
//       query = query.eq("is_dine_in", true)
//     } else if (orderType === "pickup") {
//         query = query.eq("is_pick_up", true)
//     } else if (orderType === "delivery") {
//       query = query.eq("is_delivery", true)
//     }

//     const { data, error,count } = await query;

//     if (data) {
//       const totalPages = Math.ceil(count / itemsPerPage);
//       res.status(200).json({
//         success: true,
//         data: data,
//         meta: {
//           page: pageNumber,
//           perPage: itemsPerPage,
//           totalPages,
//           totalCount: count,
//         },
//       });
//     } else {
//       throw error
//     }
//   } catch (error) {
//     console.log(error)
//     res.status(500).json({ success: false, error: error });
//   }
// });

router.get("/getCancelledOrders/:outletId", async (req, res) => {
  const { outletId } = req.params;
  const {
    page,
    perPage,
    startDate,
    endDate,
    orderSequenceId,
    orderType,
    sortType,
  } = req.query; // Extract query parameters
  const pageNumber = parseInt(page) || 1;
  const itemsPerPage = parseInt(perPage) || 10;
  try {
    let query = supabaseInstance
      .rpc(
        "get_orders_for_outlet",
        { outlet_uuid: outletId },
        { count: "exact" }
      )
      .in("order_status_id", [-1, -2])
      // .order("order_schedule_date",{ascending:false})
      // .order("order_schedule_time",{ascending:false})
      .range((pageNumber - 1) * itemsPerPage, pageNumber * itemsPerPage - 1);

    if (orderType === "dinein") {
      query = query.eq("is_dine_in", true);
    } else if (orderType === "pickup") {
      query = query.eq("is_pick_up", true);
    } else if (orderType === "delivery") {
      query = query.eq("is_delivery", true);
    }

    if (sortType === "ascending") {
      query = query
        .order("order_schedule_date", { ascending: true })
        .order("order_schedule_time", { ascending: true });
    } else if (sortType === "descending") {
      query = query
        .order("order_schedule_date", { ascending: false })
        .order("order_schedule_time", { ascending: false });
    } else {
      query = query
        .order("order_schedule_date", { ascending: false })
        .order("order_schedule_time", { ascending: false });
    }

    if (orderSequenceId) {
      query = query.ilike("order_sequence_id", `%${orderSequenceId}%`);
    }

    if (startDate && endDate) {
      query = query
        .gte("order_schedule_date", startDate)
        .lte("order_schedule_date", endDate);
    }

    const { data, error, count } = await query;

    if (data) {
      const totalPages = Math.ceil(count / itemsPerPage);
      res.status(200).json({
        success: true,
        data: data,
        meta: {
          page: pageNumber,
          perPage: itemsPerPage,
          totalPages,
          totalCount: count,
        },
      });
    } else {
      throw error;
    }
  } catch (error) {
    console.log(error);
    res.status(500).json({ success: false, error: error });
  }
});

router.get('/getClearanceOrders/:outletId', async (req, res) => { 
  const { outletId } = req.params;
  try {
    let query = supabaseInstance
      .rpc("get_outlet_orders", { outlet_uuid: outletId })
      .eq("order_status_id", 10)
      .eq("clearance_requested", false)

    const { data, error } = await query;

    if (data) {
      res.status(200).json({
        success: true,
        data: data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    console.log(error);
    res.status(500).json({ success: false, error: error });
  }
});

router.get('/getClearanceRequestOrders/:outletId', async (req, res) => { 
  const { outletId } = req.params;
  try {
    let query = supabaseInstance
      .rpc("get_outlet_orders", { outlet_uuid: outletId })
      .eq("order_status_id", 10)
      .eq("clearance_requested", true)
      .eq("clearance_completed", false)


    const { data, error } = await query;

    if (data) {
      res.status(200).json({
        success: true,
        data: data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    console.log(error);
    res.status(500).json({ success: false, error: error });
  }
});

router.post("/acceptOrder", async (req, res) => {
  const { orderIds } = req.body;
  if (!orderIds || !Array.isArray(orderIds) || orderIds.length === 0) {
    return res.status(400).json({ success: false, error: "Valid orderIds array is required" });
  }
  try {
    const results = await Promise.all(orderIds.map(async (orderId) => {
      const { data, error } = await supabaseInstance
        .from("Order")
        .update({ orderStatusId: 1 })
        .select("*")
        .eq("orderId", orderId)
        .maybeSingle();
      if (error) throw error;
      return data;
    }));

    const updatePromises = results.map(async (data) => {
      if (data) {
        const updateOrderStatusToPetpoojaResponse = await updateOrderStatus(data.orderId, "1");
        console.log("updateOrderStatus : ", updateOrderStatusToPetpoojaResponse);

        let fcmToken = await getFCMTokenOfUser(data?.customerAuthUID, data?.appName);
        let notificationTemplate = {
          title: "🎉 Your Order is Confirmed!",
          body: `Great news! Your order #${data?.orderSequenceId} has been accepted and is being prepared. We'll notify you when it's ready.`
        };
        await sendStatusUpdateNotification(fcmToken, notificationTemplate.title, notificationTemplate.body);

        return {
          orderData: data,
          saveOrderToPetpooja: updateOrderStatusToPetpoojaResponse
        };
      }
    });

    const updateResults = await Promise.all(updatePromises);

    res.status(200).json({
      success: true,
      data: updateResults
    });
  } catch (error) {
    console.log(error);
    res.status(500).json({ success: false, error: error });
  }
});

router.post("/rejectOrder/:orderId", async (req, res) => {
  const { orderId } = req.params;
  try {
    // First get order items to restore stock
    const { data: orderItems, error: orderItemsError } = await supabaseInstance
      .from("Order_Item")
      .select("itemId, quantity, Menu_Item(itemid, countable, quantity_in_stock)")
      .eq("orderId", orderId);
    
    if (orderItemsError) throw orderItemsError;
    
    const { data, error } = await supabaseInstance
      .from("Order")
      .update({ orderStatusId: -2 })
      .select("*,customerAuthUID(*)")
      .eq("orderId", orderId)
      .maybeSingle();
    if (data) {
      // Restore stock for countable items
      try {
        for (const orderItem of orderItems) {
          if (orderItem.Menu_Item && orderItem.Menu_Item.countable) {
            const newStock = orderItem.Menu_Item.quantity_in_stock + orderItem.quantity;
            
            const { error: stockUpdateError } = await supabaseInstance
              .from("Menu_Item")
              .update({
                quantity_in_stock: newStock,
                status: true // Re-enable item if it was disabled due to low stock
              })
              .eq("itemid", orderItem.itemId);
            
            if (stockUpdateError) {
              console.error(`Failed to restore stock for item ${orderItem.itemId}:`, stockUpdateError);
            }
          }
        }
      } catch (stockError) {
        console.error('Error restoring stock:', stockError);
        // Log error but don't fail the rejection
      }
      
      // const sendMobileSMSResponse = await sendMobileSMS([{ mobiles: data?.customerAuthUID?.mobile, name: data?.customerAuthUID?.customerName, orderid: orderId }], msg91config.config.order_cancellation_template_id);
      // console.log("sendMobileSMSResponse => ", sendMobileSMSResponse);
      const requestRefundResponse = await requestRefund(orderId);
      updateOrderStatus(orderId, "-2")
        .then(async (updateOrderStatusToPetpoojaResponse) => {
          console.log(
            "updateOrderStatus ran: ",
            updateOrderStatusToPetpoojaResponse
          );

          let fcmToken = await getFCMTokenOfUser(data?.customerAuthUID?.customerAuthUID, data?.appName);
          let notificationTemplate = {
            title: "Important Update About Your Order",
            body: `Your order #${data?.orderSequenceId} is rejected by the outlet.`
          }
          sendStatusUpdateNotification(fcmToken, notificationTemplate.title, notificationTemplate.body);

          res.status(200).json({
            success: true,
            data: {
              orderData: data,
              saveOrderToPetpooja: updateOrderStatusToPetpoojaResponse,
            },
          });
        })
        .catch((err) => {
          console.log(".catch block ran: ", err);
          throw err;
        });
    } else {
      throw error;
    }
  } catch (error) {
    console.log(error);
    res.status(500).json({ success: false, error: error });
  }
});

router.post("/dispatchOrder", async (req, res) => {
  const { orderIds } = req.body;
  if (!orderIds || !Array.isArray(orderIds) || orderIds.length === 0) {
    return res.status(400).json({ success: false, error: "Valid orderIds array is required" });
  }
  try {
    const results = await Promise.all(orderIds.map(async (orderId) => {
      const { data, error } = await supabaseInstance
        .from("Order")
        .update({ orderStatusId: 4 })
        .select("*")
        .eq("orderId", orderId)
        .maybeSingle();
      if (error) throw error;
      return data;
    }));

    const updatePromises = results.map(async (data) => {
      if (data) {
        const updateOrderStatusToPetpoojaResponse = await updateOrderStatus(data.orderId, "4");
        console.log("updateOrderStatus ran: ", updateOrderStatusToPetpoojaResponse);

        let fcmToken = await getFCMTokenOfUser(data?.customerAuthUID, data?.appName);
        let notificationTemplate = {
          title: "Your Food Journey Has Begun! 🛵",
          body: `Get ready! Your order #${data.orderSequenceId} is dispatched and heading your way.`
        };
        await sendStatusUpdateNotification(fcmToken, notificationTemplate.title, notificationTemplate.body);

        return {
          orderData: data,
          saveOrderToPetpooja: updateOrderStatusToPetpoojaResponse
        };
      }
    });

    const updateResults = await Promise.all(updatePromises);

    res.status(200).json({
      success: true,
      data: updateResults
    });
  } catch (error) {
    console.log(error);
    res.status(500).json({ success: false, error: error });
  }
});

router.post("/foodReadyOrder", async (req, res) => {
  const { orderIds } = req.body;
  if (!orderIds || !Array.isArray(orderIds) || orderIds.length === 0) {
    return res.status(400).json({ success: false, error: "Valid orderIds array is required" });
  }
  try {
    const results = await Promise.all(orderIds.map(async (orderId) => {
      const { data, error } = await supabaseInstance
        .from("Order")
        .update({ orderStatusId: 5 })
        .select("*")
        .eq("orderId", orderId)
        .maybeSingle();
      if (error) throw error;
      return data;
    }));

    const updatePromises = results.map(async (data) => {
      if (data) {
        const updateOrderStatusToPetpoojaResponse = await updateOrderStatus(data.orderId, "5");
        console.log("updateOrderStatus ran: ", updateOrderStatusToPetpoojaResponse);

        let fcmToken = await getFCMTokenOfUser(data?.customerAuthUID, data?.appName);
        let notificationTemplate = {
          title: "Your Order is ready! 🍽️",
          body: `Time to grab your food! Order #${data?.orderSequenceId} is ready and waiting for you.`
        };
        await sendStatusUpdateNotification(fcmToken, notificationTemplate.title, notificationTemplate.body);

        return {
          orderData: data,
          saveOrderToPetpooja: updateOrderStatusToPetpoojaResponse
        };
      }
    });

    const updateResults = await Promise.all(updatePromises);

    res.status(200).json({
      success: true,
      data: updateResults
    });
  } catch (error) {
    console.log(error);
    res.status(500).json({ success: false, error: error });
  }
});

router.post("/deliveredOrder", async (req, res) => {
  const { orderIds } = req.body;
  if (!orderIds || !Array.isArray(orderIds) || orderIds.length === 0) {
    return res.status(400).json({ success: false, error: "Valid orderIds array is required" });
  }
  try {
    const results = await Promise.all(orderIds.map(async (orderId) => {
      const { data, error } = await supabaseInstance
        .from("Order")
        .update({ orderStatusId: 10 })
        .select("*,customerAuthUID(*)")
        .eq("orderId", orderId)
        .maybeSingle();
      if (error) throw error;
      return data;
    }));

    const updatePromises = results.map(async (data) => {
      if (data) {
        const updateOrderStatusToPetpoojaResponse = await updateOrderStatus(data.orderId, "10");
        console.log("updateOrderStatus ran: ", updateOrderStatusToPetpoojaResponse);

        return {
          orderData: data,
          saveOrderToPetpooja: updateOrderStatusToPetpoojaResponse
        };
      }
    });

    const updateResults = await Promise.all(updatePromises);

    res.status(200).json({
      success: true,
      data: updateResults
    });
  } catch (error) {
    console.log(error);
    res.status(500).json({ success: false, error: error });
  }
});

router.post("/dashboardData", async (req, res) => {
  const { target_date, from_date, outlet_id, analyticalType, isAdmin } = req.body;
  try {
      const { data, error } = await supabaseInstance
        .rpc("get_vendor_dashboard_count_with_custom_range", {
          analyticaltype : analyticalType,
          from_date: from_date ? from_date : null,
          outlet_id,
          target_date,
        })
        .maybeSingle();
  
      if (data) {
        return res.status(200).json({
          success: true,
          data: data,
        });
      } else {
        throw error;
      }
    // } else {
    //   const { data, error } = await supabaseInstance
    //     .rpc("get_dashboard_count", {
    //       target_date,
    //       outlet_id,
    //       analyticaltype: analyticalType,
    //     })
    //     .maybeSingle();
  
    //   if (data) {
    //     res.status(200).json({
    //       success: true,
    //       data: data,
    //     });
    //   } else {
    //     throw error;
    //   }
    // }
  } catch (error) {
    return res.status(500).json({ success: false, error: error });
  }
});

// router.post("/orderPrice", async (req, res) => {
//   const { outlet_id,target_date,analyticalType } = req.body;
//   try {
//     const { data, error } = await supabaseInstance.rpc('get_total_price', { outlet_id,target_date,analyticaltype: analyticalType}).maybeSingle();

//     if (data) {
//       res.status(200).json({
//         success: true,
//         data: data,
//       });
//     } else {
//       throw error
//     }
//   } catch (error) {
//     console.log(error)
//     res.status(500).json({ success: false, error: error });
//   }
// });

router.post("/dineInPickUp", async (req, res) => {
  const { outlet_id, target_date, analyticalType } = req.body;
  try {
    const { data, error } = await supabaseInstance.rpc(
      "get_customer_order_count",
      { target_date, outlet_id, analyticaltype: analyticalType }
    );

    if (data) {
      res.status(200).json({
        success: true,
        data: data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error });
  }
});

router.post("/totalRevenue", async (req, res) => {
  const { outlet_id, target_date, analyticalType } = req.body;
  try {
    const { data, error } = await supabaseInstance.rpc(
      "get_total_revenue_count",
      { target_date, outlet_id, analyticaltype: analyticalType }
    );

    if (data) {
      res.status(200).json({
        success: true,
        data: data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error });
  }
});

router.post("/adminCardDashboard", async (req, res) => {
  const { outlet_id, target_date, analyticalType } = req.body;
  let currentTime = moment().tz("Asia/Kolkata").format("HH:mm:ss");
  const formated_time = moment(currentTime, "HH:mm:ss")
    .add(45, "minute")
    .format("HH:mm:ss");
  try {
    const { data, error } = await supabaseInstance
      .rpc("get_customer_dashboard_count", {
        target_date,
        outlet_id,
        analyticaltype: analyticalType,
        currenttime: currentTime,
        formated_time,
      })
      .maybeSingle();

    if (data) {
      res.status(200).json({
        success: true,
        data: { ...data, total_downloads: 0 },
      });
    } else {
      throw error;
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error });
  }
});

router.post("/adminOrderType", async (req, res) => {
  const { target_date, analyticalType } = req.body;
  try {
    const { data, error } = await supabaseInstance
      .rpc("get_customer_dinein_pickup__delivery_count", {
        target_date,
        analyticaltype: analyticalType,
      })
      .maybeSingle();

    if (data) {
      res.status(200).json({
        success: true,
        data: data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error });
  }
});

router.post("/adminSalesThroughApp", async (req, res) => {
  const { target_date, analyticalType } = req.body;
  try {
    const { data, error } = await supabaseInstance.rpc(
      "get_total_customer_revenue_count",
      { target_date, analyticaltype: analyticalType }
    );

    if (data) {
      res.status(200).json({
        success: true,
        data: data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error });
  }
});

router.get("/topFiveCustomer", async (req, res) => {
  try {
    const { data, error } = await supabaseInstance.rpc("get_top_five_customer");

    if (data) {
      res.status(200).json({
        success: true,
        data: data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error });
  }
});

router.get("/topFiveOutlets", async (req, res) => {
  try {
    const { data, error } = await supabaseInstance.rpc("get_top_five_outlets");

    if (data) {
      res.status(200).json({
        success: true,
        data: data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error });
  }
});

router.get("/topFiveMenuItem", async (req, res) => {
  try {
    const { data, error } = await supabaseInstance.rpc("get_top_five_menuitem");

    if (data) {
      res.status(200).json({
        success: true,
        data: data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error });
  }
});

router.post("/topThreeMenuItem", async (req, res) => {
  const { analyticaltype, outletId, targetDate } = req.body;
  try {
    const { data, error } = await supabaseInstance.rpc(
      "get_top_three_menuitem",
      {
        analyticaltype: analyticaltype,
        outlet_id: outletId,
        target_date: targetDate,
      }
    );

    if (data) {
      res.status(200).json({
        success: true,
        data: data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error });
  }
});

router.get("/realtimePendingOrder/:outletId", function (req, res) {
  const { outletId } = req.params;

  res.statusCode = 200;
  res.setHeader("Access-Control-Allow-Origin", "*");
  res.setHeader("Cache-Control", "no-cache");
  res.setHeader("connection", "keep-alive");
  res.setHeader("Content-Type", "text/event-stream");

  const channelName = `customer-update-channel-${outletId}-${Date.now()}`;

  supabaseInstance
    .channel(channelName)
    .on(
      "postgres_changes",
      {
        event: "INSERT",
        schema: "public",
        table: "Order",
        filter: `outletId=eq.${outletId}`,
      },
      async (payload) => {
        setTimeout(async () => {
          let orderData = await supabaseInstance
            .rpc("get_orders_for_outlet", { outlet_uuid: outletId })
            .eq("order_id", payload.new.orderId)
            .select("*")
            .maybeSingle();
          res.write("event: neworder\n"); //* new order Event
          res.write(`data: ${JSON.stringify(orderData?.data || null)}`);
          res.write("\n\n");
        }, 5000);
      }
    )
    .on(
      "postgres_changes",
      {
        event: "UPDATE",
        schema: "public",
        table: "Order",
        filter: `outletId=eq.${outletId}`,
      },
      async (payload) => {
        // console.log(payload);
        if (payload?.new && payload?.new?.orderStatusId === -1) {
          res.write("event: cancelorder\n"); //* new order Event
          res.write(`data: ${JSON.stringify(payload?.new || null)}`);
          res.write("\n\n");
        }
      }
    )
    .subscribe((status, error) => {
      if (status === "CHANNEL_ERROR") {
        console.error(`realtimePendingOrder/:outletId error => `, error);
      }
      console.log("subscribe status for outletId => ", outletId);
    });

  res.write("retry: 10000\n\n");
  req.on("close", () => {
    supabaseInstance
      .channel(channelName)
      .unsubscribe()
      // supabaseInstance.removeChannel(channelName)
      .then((res) => {
        console.log(".then => ", res);
      })
      .catch((err) => {
        console.log(".catch => ", err);
      })
      .finally(() => {
        console.log(`${channelName} Connection closed`);
      });
  });
});

router.get("/realtimeCurrentOrder/:outletId", function (req, res) {
  const { outletId } = req.params;

  res.statusCode = 200;
  res.setHeader("Access-Control-Allow-Origin", "*");
  res.setHeader("Cache-Control", "no-cache");
  res.setHeader("connection", "keep-alive");
  res.setHeader("Content-Type", "text/event-stream");

  const channelName = `customer-update-channel-${outletId}-${Date.now()}`;

  supabaseInstance
    .channel(channelName)
    .on(
      "postgres_changes",
      {
        event: "UPDATE",
        schema: "public",
        table: "Order",
        filter: `outletId=eq.${outletId}`,
      },
      async (payload) => {
        console.log("payload => ", payload);
        setTimeout(async () => {
          // let orderData = await supabaseInstance.rpc("get_orders_for_outlet",{outlet_uuid: outletId}).eq("order_id",payload.new.orderId).select("*");
          // res.write('event: order\n');  //* new order Event
          // res.write(`data: ${JSON.stringify(orderData?.data || null)}`);
          // res.write("\n\n");

          supabaseInstance
            .rpc("get_orders_for_outlet", { outlet_uuid: outletId })
            .in("order_status_id", [1, 2, 3])
            .order("order_schedule_date", { ascending: false })
            .order("order_schedule_time", { ascending: false })
            .then((orderResponse) => {
              if (orderResponse.data) {
                res.write("event: order\n"); //* new order Event
                res.write(
                  `data: ${JSON.stringify({ data: orderResponse.data || [] })}`
                );
                res.write("\n\n");
                // res.write(`data: ${JSON.stringify({ data: orderResponse.data || [] })}\n\n`);
              }
            });
        }, 1000);
      }
    )
    .subscribe((status, error) => {
      console.log(`customer-update-channel-${outletId} status => `, status);
      if (status === "CHANNEL_ERROR") {
        console.error(`realtimeCurrentOrder/:outletId error => `, error);
      }
      if (status === "SUBSCRIBED") {
        supabaseInstance
          .rpc("get_orders_for_outlet", { outlet_uuid: outletId })
          .in("order_status_id", [1, 2, 3])
          .order("order_schedule_date", { ascending: false })
          .order("order_schedule_time", { ascending: false })
          .then((orderResponse) => {
            if (orderResponse.data) {
              res.write("event: order\n"); //* new order Event
              res.write(
                `data: ${JSON.stringify({ data: orderResponse.data || [] })}`
              );
              res.write("\n\n");
              // res.write(`data: ${JSON.stringify({ data: orderResponse.data || [] })}\n\n`);
            } else {
              res.write("event: order\n"); //* new order Event
              res.write(`data: ${JSON.stringify({ data: [] })}`);
              res.write("\n\n");
              // res.write(`data: ${JSON.stringify({ data: [] || [] })}\n\n`);
            }
          });
      }
      console.log("subscribe status for outletId => ", outletId);
    });

  res.write("retry: 10000\n\n");
  req.on("close", () => {
    supabaseInstance
      .channel(channelName)
      .unsubscribe()
      .then((res) => {
        console.log(".then => ", res);
      })
      .catch((err) => {
        console.log(".catch => ", err);
      })
      .finally(() => {
        console.log(`${channelName} Connection closed`);
      });
  });
});

/**
 * @description This route is used to verify OTP for a single order
 * @param {string} otp - The OTP to verify
 * @param {string} orderId - The order ID
 * @param {string} outletId - The outlet ID
 */
router.post("/orderVerifyOTP", async (req, res) => {
  const { otp, orderId, outletId, otpType } = req.body;
  try {
    // Branch by otpType: default to 'delivery' for backward compatibility
    if ((otpType || 'delivery') === 'delivery') {
      const { data, error } = await supabaseInstance
        .from("Order")
        .select("*")
        .eq("orderOTP", otp)
        .eq("orderId", orderId)
        .eq("outletId", outletId)
        .maybeSingle();
      
      if (error) throw error;

      if (!data) {
        return res.status(400).json({ success: false, message: "Invalid OTP" });
      }

      const { error: updateErr } = await supabaseInstance
        .from("Order")
        .update({ orderStatusId: 10 })
        .eq("orderId", orderId);
      if (updateErr) throw updateErr;

      try {
        let fcmToken = await getFCMTokenOfUser(data?.customerAuthUID, data?.appName);
        let notificationTemplate = {
          title: "Mission Accomplished! 🎯",
          body: `Order #${data?.orderSequenceId} is delivered successfully! We hope it brings a smile to your face. Enjoy!`
        };
        await sendStatusUpdateNotification(fcmToken, notificationTemplate.title, notificationTemplate.body);
      } catch (notifyErr) {
        console.error('Delivery OTP notify error:', notifyErr);
      }

      return res.status(200).json({ success: true, message: "OTP Verified" });
    }

    // Clearance OTP flow
    const { data: order, error: orderError } = await supabaseInstance
      .from("Order")
      .select("orderId, outletId, appName, customerAuthUID, clearanceOtp, clearanceRequested, clearanceCompleted, orderSequenceId")
      .eq("orderId", orderId)
      .maybeSingle();

    if (orderError) throw orderError;
    if (!order) return res.status(404).json({ success: false, message: "Order not found" });
    if (order.outletId !== outletId) {
      return res.status(403).json({ success: false, message: "Unauthorized" });
    }
    if (!order.clearanceRequested || order.clearanceCompleted) {
      return res.status(400).json({ success: false, message: "Clearance not in requested state" });
    }
    /* skip clearance OTP verification for now
    if (String(otp) !== String(order.clearanceOtp)) {
      return res.status(400).json({ success: false, message: "Invalid OTP." });
    }
    */
    const { error: updateError } = await supabaseInstance
      .from("Order")
      .update({ clearanceCompleted: true, clearanceVerifiedAt: new Date().toISOString() })
      .eq("orderId", orderId);
    if (updateError) throw updateError;

    try {
      const io = req.app.get('io');
      if (io) {
        io.to(`new_orders_${order.outletId}`).emit('clearance_verified', {
          success: true,
          orderId: order.orderId,
          outletId: order.outletId,
          timestamp: new Date().toISOString()
        });
        io.to(`meal_status_${order.customerAuthUID}`).emit('clearance_verified', {
          success: true,
          orderId: order.orderId,
          outletId: order.outletId,
          timestamp: new Date().toISOString()
        });
      }
    } catch (socketError) {
      console.error('Socket emit error (clearance_verified):', socketError);
    }

    try {
      let fcmToken = await getFCMTokenOfUser(order?.customerAuthUID, order?.appName);
      let notificationTemplate = {
        title: "Plate Clearance Verified",
        body: `Clearance for your order#${order.orderSequenceId} is verified. Thank you!`
      };
      await sendStatusUpdateNotification(fcmToken, notificationTemplate.title, notificationTemplate.body);
    } catch (pushError) {
      console.error('Push notification error (clearance verified):', pushError);
    }

    return res.status(200).json({ success: true, message: "Clearance verified." });
  } catch (error) {
    console.log(error);
    res.status(500).json({ success: false, error: error });
  }
});

/**
 * @description This route is used to bypass OTP for multiple orders and send notification to the customer
 * @param {string[]} orderIds - The order IDs to bypass
 * @param {string} outletId - The outlet ID
 * @returns {Promise<Object>} - A promise that resolves to the response
 */
router.post("/orderBypassOTP", async (req, res) => {
  const { orderIds, outletId } = req.body;

  if (!Array.isArray(orderIds) || orderIds.length === 0) {
    return res.status(400).json({
      success: false,
      error: "Please provide valid order IDs array"
    });
  }

  try {
    const { data, error } = await supabaseInstance
      .from("Order")
      .select("*")
      .in("orderId", orderIds)
      .eq("outletId", outletId);

    if (error) throw error;

    if (!data || data.length === 0) {
      throw "No valid orders found";
    }

    // Update status for all orders
    const updatePromises = data.map(async (order) => {
      const { error: updateError } = await supabaseInstance
        .from("Order")
        .update({ orderStatusId: 10 })
        .eq("orderId", order.orderId);

      if (updateError) throw updateError;

      // Send notification for each order
      let fcmToken = await getFCMTokenOfUser(order.customerAuthUID, order.appName);
      let notificationTemplate = {
        title: "Mission Accomplished! 🎯",
        body: `Order #${order.orderSequenceId} is delivered successfully! We hope it brings a smile to your face. Enjoy!`
      }
      await sendStatusUpdateNotification(fcmToken, notificationTemplate.title, notificationTemplate.body);
    });

    await Promise.all(updatePromises);

    res.status(200).json({
      success: true,
      message: "Orders updated successfully",
      count: data.length
    });

  } catch (error) {
    console.error("Error in orderBypassOTP:", error);
    res.status(500).json({ 
      success: false, 
      error: error.message || error 
    });
  }
});

router.get("/lastFiveOrders/:outletId/:customerAuthUID", async (req, res) => {
  const { outletId, customerAuthUID } = req.params;

  try {
    const { data, error } = await supabaseInstance
      .from("Order")
      .select(
        "orderId,orderSequenceId,created_at,totalPrice,totalPrice,Order_Schedule!left(scheduleDate,scheduleTime),Order_Item(quantity,calculatedPrice,itemPrice,Menu_Item(itemname,itemdescription,item_image_url)),Review(message,star))"
      )
      .eq("outletId", outletId)
      .eq("customerAuthUID", customerAuthUID)
      .order("created_at", { ascending: false })
      .not("Order_Schedule.scheduleDate", "is", null)
      .limit(5);
    if (data) {
      res.status(200).json({
        success: true,
        data: data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error });
  }
});

router.get("/adminMonthlyChurn", async (req, res) => {
  try {
    const { data, error } = await supabaseInstance.rpc("get_admin_churn");
    if (data) {
      let isFirstIteration = true;

      for (let count of data) {
        if (isFirstIteration) {
          count.totalcustomer = 0;
          isFirstIteration = false;
        }
        let userdays =
          count?.totalcustomer * count?.daysinmonth +
          0.5 * count?.customercount * count?.daysinmonth;
        let churnPerDay = count?.daysinyear / userdays;
        let monthlyChurn = count?.daysinmonth * churnPerDay;
        count.churnPerDay = churnPerDay;
        count.monthlyChurn = monthlyChurn;
      }
      res.status(200).json({
        success: true,
        data: data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    console.log(error);
    res.status(500).json({ success: false, error: error });
  }
});

router.post("/peakTimeAnalyticsOutlet", async (req, res) => {
  const { analyticalType, outletId, target_date, from_date } = req.body;
  try {
    const { data, error } = await supabaseInstance.rpc(
      "outlet_peak_analytics_with_custom_range",
      { 
        analytical_type: analyticalType, 
        from_date,
        outlet_id: outletId, 
        target_date, 
      }
    );
    if (data) {
      res.status(200).json({
        success: true,
        data: data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    console.log(error);
    res.status(500).json({ success: false, error: error });
  }
});

router.post("/customerDineinPickupDeliveryOutlet", async (req, res) => {
  const { analyticalType, outletId, target_date, from_date } = req.body;

  try {
    const { data, error } = await supabaseInstance.rpc(
      "dinein_pickup_delivery_count_with_custom_range",
      {
        analyticaltype : analyticalType, 
        from_date : from_date ? from_date : null, 
        outlet_id:outletId, 
        target_date : target_date
      }
    );
    if (data) {
      res.status(200).json({
        success: true,
        data: data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    console.log(error);
    res.status(500).json({ success: false, error: error });
  }
});

router.post("/toggleVisibility", async (req, res) => {
  const { orderIdArray } = req.body;

  try {
    // Update all orders with visibility false to true
    await supabaseInstance
      .from("Order")
      .update({ visibility: true })
      .eq("visibility", false);

    // Update specific orders in orderIdArray to visibility false
    const updatePromises = orderIdArray.map((orderId) =>
      supabaseInstance
        .from("Order")
        .update({ visibility: false })
        .eq("orderId", orderId)
    );

    // Wait for all updates to finish
    const results = await Promise.all(updatePromises);

    // Check if all updates were successful
    const allSuccess = results.every(result => result.error === null);

    if (allSuccess) {
      res.status(200).json({ success: true, message: "Order Visibility Changed." });
    } else {
      res.status(500).json({ success: false, message: "Some orders failed to update visibility." });
    }
  } catch (error) {
    console.error(error);
    res.status(500).json({ success: false, error: error });
  }
});

router.post("/sendOrderToHistory", async (req, res) => {
  const {orderId}= req.body;
  try{
    const {error} = await supabaseInstance
    .from("Order")
    .update({orderStatusId: 10})
    .eq("orderId",orderId)
    .maybeSingle();

    if(error) throw error;
    
    return res.status(200).json({success:true});
  }
  catch(error){
    console.log(error);
    return res.status(500).json({success:false,error:error});
  }
});

/**
 * @description This route is used to get all the order counts of orders of all types for a particular outlet
 * @param {string} outletId - The outlet ID
 * @returns {Promise<Object>} - A promise that resolves to the response
 */
router.get("/getAllOrderCounts/:outletId", async (req, res) => { 
  const { outletId } = req.params;

  try {
    const { data, error } = await supabaseInstance
      .from("Order")
      .select("orderStatusId, orderSequenceId, Customer(customerName)")
      .eq("outletId", outletId)
      .in("orderStatusId", [0, 1, 2, 3, 4, 5]);    
        
    if (error) {
      console.log(`Error fetching order counts for outlet ${outletId}:`, error);
      throw error
    };
    
    const nameIdMap = {
      0: "pending",
      1: "current",
      2: "current",
      3: "current",
      4: "live",
      5: "ready"
    }

    const orderCounts = data.reduce((acc, curr) => {
      acc[nameIdMap[curr.orderStatusId]] = (acc[nameIdMap[curr.orderStatusId]] || 0) + 1;
      return acc;
    }, {});
    
    // Additional counts
    const [{ count: clearanceRequestedCount, error: crErr }, { count: unclearedCount, error: ucErr }] = await Promise.all([
      supabaseInstance
        .from("Order")
        .select("orderId", { count: "exact", head: true })
        .eq("outletId", outletId)
        .eq("clearanceRequested", true)
        .eq("clearanceCompleted", false),
      supabaseInstance
        .from("Order")
        .select("orderId", { count: "exact", head: true })
        .eq("outletId", outletId)
        .eq("orderStatusId", 10)
        .eq("clearanceCompleted", false)
    ]);

    if (crErr) {
      console.log(`Error fetching clearanceRequestedCount for outlet ${outletId}:`, crErr);
    }
    if (ucErr) {
      console.log(`Error fetching unclearedCount for outlet ${outletId}:`, ucErr);
    }

    const response = {
      ...orderCounts,
      clearanceRequested: clearanceRequestedCount || 0,
      delivered: unclearedCount || 0
    };

    return res.status(200).json({success:true,data:response});
  
  
  } catch (error) {
    console.log(error);
    return res.status(500).json({success:false,error:error});
  }
});

/**
 * @lastUpdate 2025-08-26
 * @description Get stock information for cart items
 * @param {string|string[]} itemId - Comma-separated string, JSON array string, or multiple query params with same name
 * @returns {Promise<Object>} - A promise that resolves to the response
 */
router.get("/getCartItemsStock", validateGetCartItemsStock, async (req, res) => {
  const { itemId } = req.query;

  try {
    // Parse itemId based on format - optimized parsing
    let parsedItemIds;
    if (Array.isArray(itemId)) {
      parsedItemIds = itemId.map(id => parseInt(id));
    } else {
      // Single string handling with fallback chain
      if (itemId.includes(',')) {
        parsedItemIds = itemId.split(',').map(id => parseInt(id.trim()));
      } else if (itemId.startsWith('[')) {
        parsedItemIds = JSON.parse(itemId).map(id => parseInt(id));
      } else {
        parsedItemIds = [parseInt(itemId)];
      }
    }

    // Single query for menu items stock
    const { data: menuItems, error } = await supabaseInstance
      .from('Menu_Item')
      .select('itemid, itemname, quantity_in_stock, countable, status')
      .in('itemid', parsedItemIds);

    if (error) {
      console.error('Supabase query error:', error);
      return res.status(500).json({
        success: false,
        message: "Database query failed",
        error: error.message
      });
    }

    // Create items map and process data in single loop
    const itemsMap = new Map();
    menuItems.forEach(item => {
      itemsMap.set(item.itemid, item);
    });

    // Build response data using actual database status
    const stockData = parsedItemIds.map(id => {
      const item = itemsMap.get(id);
      if (item) {
        return {
          itemid: item.itemid,
          itemname: item.itemname || 'unknown item',
          status: item.status,  // Use actual database status, not calculated
          quantity_in_stock: item.quantity_in_stock,
          countable: item.countable
        };
      } else {
        return {
          itemid: id,
          itemname: 'invalid item',
          status: false,
          quantity_in_stock: 0,
          countable: false,
        };
      }
    });

    return res.status(200).json({
      success: true,
      message: "Stock data retrieved successfully",
      data: {
        stockData,
      }
    });

  } catch (error) {
    console.error('Error in getCartItemsStock:', error);
    return res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message
    });
  }
});

/**
 * Clearance feature endpoints (boolean approach on Order table)
 */
router.post("/:orderId/clearance/request", async (req, res) => {
  const { orderId } = req.params;
  const { customerAuthUID } = req.body;
  try {
    const { data: order, error: orderError } = await supabaseInstance
      .from("Order")
      .select("orderId, outletId, customerAuthUID, orderStatusId, clearanceRequested, clearanceCompleted, outletId(enableClearanceOrder)")
      .eq("orderId", orderId)
      .maybeSingle();

    if (orderError) throw orderError;
    if (!order) return res.status(404).json({ success: false, message: "Order not found" });
    if (order.customerAuthUID !== customerAuthUID) {
      return res.status(403).json({ success: false, message: "Unauthorized" });
    }
    if (!order?.outletId?.enableClearanceOrder) {
      return res.status(400).json({ success: false, message: "Clearance not enabled for outlet" });
    }
    if (order.orderStatusId !== 10) {
      return res.status(400).json({ success: false, message: "Clearance allowed only after delivery" });
    }
    if (order.clearanceRequested || order.clearanceCompleted) {
      return res.status(400).json({ success: false, message: "Clearance already requested or completed" });
    }

    const { data: updated, error: updateError } = await supabaseInstance
      .from("Order")
      .update({ clearanceRequested: true, clearanceRequestedAt: new Date().toISOString() })
      .eq("orderId", orderId)
      .select("orderId, outletId, customerAuthUID, clearanceRequestedAt")
      .maybeSingle();
    if (updateError) throw updateError;

    try {
      const io = req.app.get('io');
      console.log("io", io ? "true" : "io is not defined");
      console.log("io.emitClearanceOrder", io.emitClearanceOrder ? "true" : "io.emitClearanceOrder is not defined");

      if (io && io.emitClearanceOrder) {
        let orderData = await supabaseInstance
          .rpc("get_outlet_orders", { outlet_uuid: updated.outletId })
          .eq("order_id", orderId)
          .select("*")
          .maybeSingle();

        io.emitClearanceOrder(updated.outletId, orderData.data, "requested");
      }
    } catch (socketError) {
      console.error('Socket emit error (clearance_requested):', socketError);
    }

    return res.status(200).json({ success: true, message: "Clearance requested." });
  } catch (error) {
    console.error("/order/:orderId/clearance/request error:", error);
    return res.status(500).json({ success: false, message: "Internal server error", error });
  }
});

/**
 * @description This route is used to set the back data for clearance orders (clearance_requested and clearance_completed)
 * @param {string} outletId - The outlet ID
 * @returns {Promise<Object>} - A promise that resolves to the response
 */
router.post("/dev/backDataForClearanceOrders/:outletId", async (req, res) => { 
  const { outletId } = req.params;
  // set the back data for clearance orders (clearance_requested and clearance_completed) to true
  try {
    const { error } = await supabaseInstance
      .from("Order")
      .update({ clearanceRequested: true, clearanceCompleted: true })
      .eq("outletId", outletId)
      .eq("orderStatusId", 10)
      .eq("clearanceRequested", false)
      .eq("clearanceCompleted", false);
    
    if (error) {
      console.log(`Error updating clearance data for outlet ${outletId}:`, error);
      throw error
    };

    return res.status(200).json({success:true,message:"Clearance data updated for delivered orders."});
  } catch (error) {
    logger.error("Error in backDataForClearanceOrders:", error);
    return res.status(500).json({success:false,error:error} );
  }
});

module.exports = router;

function generateOTP() {
  return Math.floor(1000 + Math.random() * 9000);
}