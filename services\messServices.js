// Imports
const supabaseInstance = require('./supabaseClient').supabase;
const logger = require('./logger');
const { getWeekInfo } = require('./dateTimeService');

/**
 * Get the current menu ID for a mess outlet based on current time.
 */
async function getCurrentMeal(outletId) {
    try {
        const current = new Date();
        const { weekNumber, dayOfWeek } = getWeekInfo(current);
        const currentTime = current.toLocaleTimeString('en-US', { hour12: false });
        const { data: mealTimingsData, error: mealTimingsError } = await supabaseInstance
            .from('Meal_Timings')
            .select('mealTypeId, startTime, endTime, enabled')
            .eq('outletId', outletId);
        if (mealTimingsError) throw mealTimingsError;
        const currentMealType = mealTimingsData.find(timing => {
            if (!timing.enabled) return false;
            const startTime = new Date(timing.startTime).toLocaleTimeString('en-US', { hour12: false });
            const endTime = new Date(timing.endTime).toLocaleTimeString('en-US', { hour12: false });
            return currentTime >= startTime && currentTime <= endTime;
        });
        if (!currentMealType) return null;
        const { data: menuData, error: menuError } = await supabaseInstance
            .from('Weekly_Menu')
            .select('menuId, mealTypeId, price')
            .eq('outletId', outletId)
            .eq('dayOfWeek', dayOfWeek)
            .eq('mealTypeId', currentMealType.mealTypeId)
            .eq('weekNumber', weekNumber)
            .single();
        if (menuError) throw menuError;

        const currentMeal = {
            menuId: menuData.menuId,
            price: menuData.price || 0, // Default to 0 if price is not set
            mealTypeId: currentMealType.mealTypeId,
            startTime: mealTimingsData.startTime,
            endTime: mealTimingsData.endTime
        }
        return currentMeal;
    } catch (error) {
        logger.error('getCurrentMeal:', error);
        throw new Error('Failed to retrieve current menu ID for mess.');
    }
}

/**
 * Get menuId for a given mealType, outlet, and date.
 */
async function getMenuIdForMealType(mealTypeId, outletId, date = new Date()) {
    try {
        const { weekNumber, dayOfWeek } = getWeekInfo(date);
        const { data, error } = await supabaseInstance
            .from('Weekly_Menu')
            .select('menuId')
            .eq('mealTypeId', mealTypeId)
            .eq('outletId', outletId)
            .eq('weekNumber', weekNumber)
            .eq('dayOfWeek', dayOfWeek)
            .single();
        if (error) throw error;
        return data.menuId;
    } catch (error) {
        logger.error('getMenuIdForMealType:', error);
        throw new Error('Failed to get menuId for mealType.');
    }
}

/**
 * Check if a meal is enabled for an outlet.
 */
async function isMealEnabledForOutlet(mealTypeId, outletId) {
    try {
        const { data, error } = await supabaseInstance
            .from('Meal_Timings')
            .select('enabled')
            .eq('outletId', outletId)
            .eq('mealTypeId', mealTypeId)
            .single();
        if (error) throw error;
        return !!data.enabled;
    } catch (error) {
        logger.error('isMealEnabledForOutlet:', error);
        throw new Error('Failed to check if meal is enabled.');
    }
}

/**
 * Get meal timings for an outlet and mealType.
 */
async function getMealTimingsForOutlet(mealTypeId, outletId) {
    try {
        const { data, error } = await supabaseInstance
            .from('Meal_Timings')
            .select('*')
            .eq('outletId', outletId)
            .eq('mealTypeId', mealTypeId)
            .single();
        if (error) throw error;
        return data;
    } catch (error) {
        logger.error('getMealTimingsForOutlet:', error);
        throw new Error('Failed to get meal timings.');
    }
}

/**
 * Check if a user has already RSVP'd for a menu/date.
 */
async function hasUserRSVPd(customerAuthUID, menuId, rsvpDate) {
    try {
        const { data, error } = await supabaseInstance
            .from('RSVP')
            .select('rsvpId')
            .eq('customerAuthUID', customerAuthUID)
            .eq('menuId', menuId)
            .eq('rsvpDate', rsvpDate)
            .maybeSingle();
        if (error) throw error;
        return !!data;
    } catch (error) {
        logger.error('hasUserRSVPd:', error);
        throw new Error('Failed to check RSVP status.');
    }
}

/**
 * Add or update RSVP record for a user.
 */
async function upsertRSVP({ customerAuthUID, menuId, rsvpDate, response, mealTypeId, outletId }) {
    try {
        // // Use Supabase's 'upsert' for an atomic and more efficient operation.
        // // This performs an INSERT or UPDATE in a single database round trip.
        // const { data, error } = await supabaseInstance
        //     .from('RSVP')
        //     .upsert({
        //         customerAuthUID,
        //         menuId,
        //         rsvpDate,
        //         response,
        //         mealTypeId,
        //         outletId,
        //     }, { onConflict: ['customerAuthUID', 'menuId', 'rsvpDate'] })
        //     .select('*')
        //     .single();

        // if (error) {
        //     logger.error('upsertRSVP: Error during upsert:', error);
        //     throw new Error('Failed to add or update RSVP.');
        // }

        //check if the rsvp record already exists
        let rsvpData = null;
        const { data, error } = await supabaseInstance
            .from('RSVP')
            .select('*')
            .eq('customerAuthUID', customerAuthUID)
            .eq('menuId', menuId)
            .eq('rsvpDate', rsvpDate)
            .maybeSingle();
        if (error) throw error;
        rsvpData = data;
        if (data) {
            //update the rsvp record
            const { error: updateError } = await supabaseInstance
                .from('RSVP')
                .update({ response })
                .eq('rsvpId', data.rsvpId);
            if (updateError) throw updateError;
            rsvpData = data;
        } else {
            //insert the rsvp record
            const { error } = await supabaseInstance
                .from('RSVP')
                .insert({ customerAuthUID, menuId, rsvpDate, response, mealTypeId, outletId });
            if (error) throw error;
            rsvpData = data;
        }

        logger.info('RSVP added/updated successfully:', rsvpData);
        return rsvpData;
    } catch (error) {
        // Catching potential errors from the try block, including the thrown error.
        logger.error('upsertRSVP:', error);
        throw new Error('Failed to add or update RSVP.');
    }
}

/**
 * Check if RSVP date is same as serving date.
 */
function isRSVPDateSameAsServingDate(rsvpDate, servingDate) {
    return new Date(rsvpDate).toISOString().split('T')[0] === new Date(servingDate).toISOString().split('T')[0];
}

/**
 * Get wallet balance for a user.
 */
async function getWalletBalance(customerAuthUID) {
    try {
        const { data, error } = await supabaseInstance
            .from('Wallet')
            .select('balance')
            .eq('customerAuthUID', customerAuthUID)
            .maybeSingle();
        if (error) throw error;
        return data ? Number(data.balance) : 0;
    } catch (error) {
        logger.error('getWalletBalance:', error);
        throw new Error('Failed to get wallet balance.');
    }
}

/**
 * Deduct wallet balance and log transaction.
 */
async function deductWalletBalance(customerAuthUID, amount, description, outletId = null, bearer = null) {
    try {
        // Start transaction (pseudo, as Supabase JS may not support SQL transactions directly)
        // 1. Get current balance
        const balance = await getWalletBalance(customerAuthUID);
        if (balance < amount) {
            throw new Error('Insufficient wallet balance.');
        }
        // 2. Deduct balance
        logger.info('Performing wallet deduction:', { customerAuthUID, amount, description });
        const { error: updateError } = await supabaseInstance
            .from('Wallet')
            .update({ balance: balance - amount })
            .eq('customerAuthUID', customerAuthUID);
        if (updateError) throw updateError;
        logger.info('Deduction successful:', { customerAuthUID, newBalance: balance - amount });

        // 3. Log transaction in Wallet_Transaction
        const { error: txnError } = await supabaseInstance
            .from('Wallet_Transaction')
            .insert({
                customerAuthUID,
                amount,
                description,
                outletId,
                transactionType: 'DEBIT',
                status: 'success',
                bearer: bearer || null, // Optional bearer token 
            });
        if (txnError) {
            logger.error('deductWalletBalance: Error logging transaction:', txnError);
            throw txnError;
        }
        // 4. Return success
        return { success: true, message: 'Wallet debited successfully.' };
    } catch (error) {
        logger.error('deductWalletBalance:', error);
        throw error;
    }
}

/**
 * Check if wallet transaction is required for a user/outlet/menu.
 * Now checks if menu has a price > 0.
 */
async function isWalletTransactionRequiredForMess(outletId) {
    try {
        // check the is_wallet_transaction_required_for_mess of Outlet table
        const { data: outletData, error: outletError } = await supabaseInstance
            .from('Outlet')
            .select('is_wallet_transaction_required_for_mess')
            .eq('outletId', outletId)
            .single();
        if (outletError) throw outletError;
        if (!outletData || !outletData.is_wallet_transaction_required_for_mess) {
            return false; // No wallet transaction required for this outlet
        }

        return true; // Wallet transaction required for this outlet

    } catch (error) {
        logger.error('isWalletTransactionRequired:', error);
        throw new Error('Failed to check wallet transaction requirement.');
    }
}

/**
 * Perform wallet transaction for a user.
 * Checks balance, deducts, and logs transaction.
 */
async function performWalletTransaction(customerAuthUID, amount, description, outletId) {
    try {
        // The balance check is handled within deductWalletBalance, so no need to check it here again.
        await deductWalletBalance(customerAuthUID, amount, description, outletId, 'CUSTOMER');


        logger.info('performWalletTransaction: Wallet transaction successful', { customerAuthUID, amount, description });
        return { success: true, message: 'Wallet transaction successful.' };
    } catch (error) {
        logger.error('performWalletTransaction:', error);
        throw error;
    }
}

/**
 * Handle wallet transaction error.
 */
function handleWalletTransactionError(error) {
    logger.error('Wallet transaction error:', error);
    // For production, consider adding more specific error handling or alerting mechanisms here.
}

/**
 * Check if meal is already served for a user/menu/date/outlet.
 */
async function hasMealAlreadyServed(customerAuthUID, menuId, servedDate, outletId) {
    try {
        const { data, error } = await supabaseInstance
            .from('Meals_Served')
            .select('mealServedId')
            .eq('customerAuthUID', customerAuthUID)
            .eq('menuId', menuId)
            .eq('served_date', servedDate)
            .eq('outletId', outletId)
            .maybeSingle();
        if (error) throw error;
        return !!data;
    } catch (error) {
        logger.error('hasMealAlreadyServed:', error);
        throw new Error('Failed to check if meal already served.');
    }
}

/**
 * Add a record to Meals_Served.
 */
async function addMealServedRecord({ customerAuthUID, menuId, mealTypeId, outletId, servedDate, servedTime }) {
    try {
        const { data, error } = await supabaseInstance
            .from('Meals_Served')
            .insert([{ customerAuthUID, menuId, mealTypeId, outletId, served_date: servedDate, served_time: servedTime }])
            .select('*')
            .single();
        if (error) throw error;
        return data;
    } catch (error) {
        logger.error('addMealServedRecord:', error);
        throw new Error('Failed to add meal served record.');
    }
}

/**
 * Emit meal status via socket.io (if io instance is available).
 */
function emitMealStatus(io, customerAuthUID, statusObj) {
    if (io && typeof io.emitMealStatus === 'function') {
        io.emitMealStatus(customerAuthUID, statusObj);
    } else {
        logger.error('emitMealStatus: IO instance or emitMealStatus not available');
    }
}

/**
 * Check if user is whitelisted for an outlet.
 */
async function isUserWhitelisted(customerAuthUID, outletId) {
    try {
        const { data, error } = await supabaseInstance
            .from('Mess_Whitelist')
            .select('customerAuthUID')
            .eq('customerAuthUID', customerAuthUID)
            .eq('outletId', outletId)
            .maybeSingle();
        if (error) throw error;
        return !!data;
    } catch (error) {
        logger.error('isUserWhitelisted:', error);
        throw new Error('Failed to check if user is whitelisted.');
    }
}

/**
 * Process mess order as per the new feature flow diagram.
 * Handles wallet, RSVP, and meal serving logic.
 */
async function processMessOrder(customerAuthUID, outletId, io = null) {
    logger.info('[processMessOrder] Called', { customerAuthUID, outletId });
    try {
        const currentMeal = await getCurrentMeal(outletId);
        logger.info('[processMessOrder] currentMeal', currentMeal);
        // 1. Get current menu ID
        const menuId = currentMeal ? currentMeal.menuId : null;
        if (!menuId) {
            logger.warn('[processMessOrder] No active menu found', { outletId });
            return { success: false, message: 'No active menu found.' };
        }
        // 2. Check if wallet transaction is required
        logger.info('[processMessOrder] Checking if wallet transaction is required', { customerAuthUID, outletId, menuId });
        const walletRequired = await isWalletTransactionRequiredForMess(outletId);
        logger.info('[processMessOrder] walletRequired', walletRequired);
        if (walletRequired) {
            // Get price from menu

            const amount = Number(currentMeal.price);
            logger.info('[processMessOrder] Wallet transaction required. Amount:', amount);
            if (isNaN(amount) || amount <= 0) {
                logger.warn('[processMessOrder] Invalid meal price', { price: currentMeal.price });
                return { success: false, message: 'Invalid meal price.' };
            }
            const rsvpDate = new Date().toISOString().split('T')[0];
            const servedDate = new Date().toISOString().split('T')[0];
            const servedTime = new Date().toISOString();
            const alreadyServed = await hasMealAlreadyServed(customerAuthUID, menuId, servedDate, outletId);
            logger.info('[processMessOrder] alreadyServed', alreadyServed);
            if (alreadyServed) {
                logger.warn('[processMessOrder] Meal already served for this menu and date', { customerAuthUID, menuId, servedDate });
                return { success: false, message: 'Meal already served for this menu and date.' };
            }

            // 3. Do wallet transaction
            try {
                logger.info('[processMessOrder] Performing wallet transaction', { customerAuthUID, amount, outletId });
                await performWalletTransaction(customerAuthUID, amount, `Mess meal order for menu ${menuId}`, outletId);
            } catch (err) {
                logger.error('[processMessOrder] Wallet transaction failed', err);
                handleWalletTransactionError(err);
                return { success: false, message: 'Wallet transaction failed: ' + err.message };
            }
            // Add or update RSVP to ensure it's marked as 'yes'.
            // The upsert handles both new and existing RSVPs.
            logger.info('[processMessOrder] Upserting RSVP', { customerAuthUID, menuId, rsvpDate });
            await upsertRSVP({ customerAuthUID, menuId, rsvpDate, response: true, mealTypeId: currentMeal.mealTypeId, outletId });

            // 4. Place order (add to Meals_Served)
            const mealTypeId = currentMeal.mealTypeId;
            logger.info('[processMessOrder] Adding meal served record', { customerAuthUID, menuId, mealTypeId, outletId, servedDate });

            const served = await addMealServedRecord({ customerAuthUID, menuId, mealTypeId, outletId, servedDate, servedTime });
            logger.info('[processMessOrder] Meal served after wallet transaction', served);
            // emitMealStatus(io, customerAuthUID, { success: true, message: 'Meal served after wallet transaction.', data: served });
            return { success: true, message: 'Meal served after wallet transaction.', data: served };
        } else {
            // Add or update RSVP to ensure it's marked as 'yes'.
            const rsvpDate = new Date().toISOString().split('T')[0];
            logger.info('[processMessOrder] Upserting RSVP', { customerAuthUID, menuId, rsvpDate });
            await upsertRSVP({ customerAuthUID, menuId, rsvpDate, response: true, mealTypeId: currentMeal.mealTypeId, outletId });

            // 6. Check if RSVP date is same as serving date
            if (isRSVPDateSameAsServingDate(rsvpDate, new Date())) {
                // 7. Add record to served meals
                const servedDate = new Date().toISOString().split('T')[0];
                const servedTime = new Date().toISOString();
                logger.info('[processMessOrder] Checking if meal already served', { customerAuthUID, menuId, servedDate, outletId });
                const alreadyServed = await hasMealAlreadyServed(customerAuthUID, menuId, servedDate, outletId);
                logger.info('[processMessOrder] alreadyServed', alreadyServed);
                if (alreadyServed) {
                    logger.warn('[processMessOrder] Meal already served for this menu and date', { customerAuthUID, menuId, servedDate });
                    return { success: false, message: 'Meal already served for this menu and date.' };
                }
                logger.info('[processMessOrder] Adding meal served record after RSVP', { customerAuthUID, menuId, mealTypeId: currentMeal.mealTypeId, outletId, servedDate });
                const served = await addMealServedRecord({ customerAuthUID, menuId, mealTypeId: currentMeal.mealTypeId, outletId, servedDate, servedTime });
                logger.info('[processMessOrder] Meal served after RSVP', served);
                // emitMealStatus(io, customerAuthUID, { success: true, message: 'Meal served after RSVP.', data: served });
                return { success: true, message: 'Meal served after RSVP.', data: served };
            } else {
                logger.info('[processMessOrder] RSVP added, but serving date does not match', { rsvpDate });
                return { success: true, message: 'RSVP added, but serving date does not match. Meal not served yet.' };
            }
        }
    } catch (error) {
        logger.error('[processMessOrder] Error:', error);
        return { success: false, message: 'Failed to process mess order.', error: error.message };
    }
}

module.exports = {
    getCurrentMeal,
    getMenuIdForMealType,
    isMealEnabledForOutlet,
    getMealTimingsForOutlet,
    hasUserRSVPd,
    upsertRSVP,
    isRSVPDateSameAsServingDate,
    isWalletTransactionRequiredForMess,
    performWalletTransaction,
    handleWalletTransactionError,
    hasMealAlreadyServed,
    addMealServedRecord,
    emitMealStatus,
    isUserWhitelisted,
    getWalletBalance,
    deductWalletBalance,
    processMessOrder,
};