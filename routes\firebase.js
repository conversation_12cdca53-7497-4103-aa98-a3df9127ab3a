const { initializeApp, cert } = require("firebase-admin/app");
const admin = require("firebase-admin");
const express = require("express");
const router = express.Router();
const path = require("path");
const supabaseInstance = require("../services/supabaseClient").supabase;


initializeApp({
  credential: cert(
    path.resolve(
      __dirname,
      "./mealpe-93b10-firebase-adminsdk-niiz7-ed0b736cc8.json"
    )
  ),
  projectId: "mealpe-93b10",
});

async function sendMessNotification(
  title,
  body,
  fcmToken,
  outletName,
  outletId,
  appName = "com.mealpe"
) {
  {
    const message = {
      data: {
        screenName: "MessScreen",
        outletName: outletName,
        outletId: outletId,
        appName: appName,
      },
      notification: {
        title,
        body,
      },
      token: fcmToken,
    };

    try {
      const response = await admin.messaging().send(message);
      console.log("Successfully sent message:", response);
      return { success: true, response };
    } catch (error) {
      console.error("Error sending message:", error);

      if (
        error.code === "messaging/invalid-argument" ||
        error.code === "messaging/registration-token-not-registered"
      ) {
        console.log("Invalid FCM token detected. Token:", fcmToken);
        await removeInvalidToken(fcmToken);
        return { success: false, error: "Invalid FCM token", code: error.code };
      }

      return { success: false, error: error.message, code: error.code };
    }
  }
}

async function sendNotification(
  fcmToken,
  title,
  body,
  outletName = "test name",
  outletId = "Test id",
  appName = "com.mealpe"
) {
  const message = {
    data: {
      screenName: "HomeScreen",
      outletName: outletName ? ,
      outletId: outletId,
      appName: appName,
    },
    notification: {
      title,
      body,
    },
    token: fcmToken,
  };

  try {
    const response = await admin.messaging().send(message);
    console.log("Successfully sent message:", response);
    return { success: true, response };
  } catch (error) {
    console.error("Error sending message:", error);

    if (
      error.code === "messaging/invalid-argument" ||
      error.code === "messaging/registration-token-not-registered"
    ) {
      console.log("Invalid FCM token detected. Token:", fcmToken);
      await removeInvalidToken(fcmToken);
      return { success: false, error: "Invalid FCM token", code: error.code };
    }

    return { success: false, error: error.message, code: error.code };
  }
}

async function sendStatusUpdateNotification(fcmToken, title, body) {
  const message = {
    notification: {
      title,
      body,
    },
    token: fcmToken,
  };

  try {
    const response = await admin.messaging().send(message);
    console.log("Successfully sent message:", response);
    return { success: true, response };
  } catch (error) {
    console.error("Error sending message:", error);

    if (
      error.code === "messaging/invalid-argument" ||
      error.code === "messaging/registration-token-not-registered"
    ) {
      console.log("Invalid FCM token detected. Token:", fcmToken);
      await removeInvalidToken(fcmToken);
      return { success: false, error: "Invalid FCM token", code: error.code };
    }

    return { success: false, error: error.message, code: error.code };
  }
}

async function removeInvalidToken(fcmToken) {
  try {
    const { data, error } = await supabaseInstance
      .from("FCM_Tokens")
      .delete()
      .match({ fcmToken });

    if (error) throw error;
    console.log(`Removed invalid token ${fcmToken}`);
  } catch (error) {
    console.error("Error removing invalid token:", error);
  }
}

router.get("/", (req, res) => {
  return res.status(200).json({
    success: true,
    message: "response from firebase.js",
  });
});

router.post("/send-broadcast", async (req, res) => {
  const { title, body, outletName, outletId, appName = "NBS" } = req.body;

  if (!title || !body || !appName) {
    return res.status(400).json({ error: "Missing required parameters" });
  }

  try {
    // Fetch all FCM tokens for the specified app
    const { data: tokens, error: tokenError } = await supabaseInstance
      .from("FCM_Tokens")
      .select("fcmToken")
      .eq("appName", appName);

    if (tokenError) throw tokenError;

    if (!tokens || tokens.length === 0) {
      return res
        .status(404)
        .json({ error: "No FCM tokens found for the specified app" });
    }

    const results = [];
    const invalidTokens = [];

    // Send notifications to all tokens
    for (const token of tokens) {
      const result = await sendNotification(
        token.fcmToken,
        title,
        body,
        outletName,
        outletId,
        appName
      );

      if (result.success) {
        results.push({ token: token.fcmToken, status: "success" });
      } else {
        if (
          result.code === "messaging/invalid-argument" ||
          result.code === "messaging/registration-token-not-registered"
        ) {
          invalidTokens.push(token.fcmToken);
        }
        results.push({
          token: token.fcmToken,
          status: "failed",
          error: result.error,
        });
      }
    }

    // Remove invalid tokens
    if (invalidTokens.length > 0) {
      const { error: deleteError } = await supabaseInstance
        .from("FCM_Tokens")
        .delete()
        .in("fcmToken", invalidTokens);

      if (deleteError) {
        console.error("Error removing invalid tokens:", deleteError);
      }
    }

    const successCount = results.filter((r) => r.status === "success").length;
    const failureCount = results.filter((r) => r.status === "failed").length;

    res.status(200).json({
      message: "Broadcast notification process completed",
      totalProcessed: tokens.length,
      successCount,
      failureCount,
      invalidTokensRemoved: invalidTokens.length,
      results,
    });
  } catch (error) {
    console.error("Error in /send-broadcast endpoint:", error);
    res
      .status(500)
      .json({ error: "Internal server error", details: error.message });
  }
});

/**
 * @description Store FCM token for an outlet user (outlet admin/staff)
 * @route POST /firebase/storeOutletFcmToken
 * @body { outletId, userAuthUID, userType, fcmToken, appName }
 * @returns { success: boolean, message?: string, error?: string }
 */
router.post("/storeOutletFcmToken", async (req, res) => {
  const { outletId, userAuthUID, userType, fcmToken, appName } = req.body;
  
  // Validate required fields
  if (!outletId || !userAuthUID || !userType || !fcmToken) {
    return res.status(400).json({ 
      success: false, 
      error: 'Missing required fields: outletId, userAuthUID, userType, fcmToken' 
    });
  }

  // Validate userType
  if (!['staff', 'admin'].includes(userType)) {
    return res.status(400).json({ 
      success: false, 
      error: 'userType must be either "staff" or "admin"' 
    });
  }
  
  try {
    const { error } = await supabaseInstance
      .from('FCM_Token_Outlet')
      .upsert(
        { outletId, userAuthUID, userType, appName, fcmToken },
        { onConflict: 'outletId,userAuthUID,appName' }
      );

    if (error) throw error;
    
    return res.status(200).json({
      success: true, 
      message: 'FCM token stored successfully' 
    });
  } catch (error) {
    console.error('Error storing outlet FCM token:', error);
    return res.status(500).json({ 
      success: false, 
      error: 'Failed to store FCM token' 
    });
  }
});

async function getFCMTokenOfUser(customerAuthUID, appName = "com.mealpe") {
  try {
    const { data, error } = await supabaseInstance
      .from("FCM_Tokens")
      .select("fcmToken")
      .eq("customerAuthUID", customerAuthUID)
      .eq("appName", appName);

    if (error) throw error;

    return data[0]?.fcmToken;
  } catch (error) {
    console.error("Error fetching FCM token:", error);
    return null;
  }
}

/**
 * send notifications to selected users from admin.
 * @param {string} title
 * @param {string} body
 * @param {string} appName
 * @param {string} fcmTokens
 *
 * steps :
 * - validate for missing parameters
 * - send the sns notifications to all the fcm tokens
 * - return the response of successful as well as failed notifications
 */
async function sendAdminNotifications(title, body, fcmTokens, appName) {
  try {
    if (!title || !body || !fcmTokens) {
      return { success: false, error: "Missing required parameters" };
    }

    const results = [];
    const invalidTokens = [];

    for (const token of fcmTokens) {
      const result = await sendNotification(token, title, body, appName);

      if (result.success) {
        results.push({ token, status: "success" });
      } else {
        if (
          result.code === "messaging/invalid-argument" ||
          result.code === "messaging/registration-token-not-registered"
        ) {
          invalidTokens.push(token);
        }
        results.push({ token, status: "failed", error: result.error });
      }
    }

    if (invalidTokens.length > 0) {
      const { error: deleteError } = await supabaseInstance
        .from("FCM_Tokens")
        .delete()
        .in("fcmToken", invalidTokens)
        .eq("appName", appName); // Add appName to deletion query

      if (deleteError) {
        console.error("Error removing invalid tokens:", deleteError);
      }
    }

    const successCount = results.filter((r) => r.status === "success").length;
    const failureCount = results.filter((r) => r.status === "failed").length;

    return {
      success: true,
      message: "Broadcast notification process completed",
      totalProcessed: fcmTokens.length,
      successCount,
      failureCount,
      invalidTokensRemoved: invalidTokens.length,
      results,
    };
  } catch (error) {
    console.error("Error sending admin notifications:", error);
    return { success: false, error };
  }
}

async function sendOutletNotifications(title, body, fcmTokens, appName, outletName) {
  try {
    if (!title || !body || !fcmTokens || !appName) {
      return { success: false, error: "Missing required parameters" };
    }

    const results = [];
    const invalidTokens = [];
    // title = `${outletName} - ${title}`;

    for (const token of fcmTokens) {
      const result = await sendNotification(token, title, body, appName, outletName);

      if (result.success) {
        results.push({ token, status: "success" });
      } else {
        if (
          result.code === "messaging/invalid-argument" ||
          result.code === "messaging/registration-token-not-registered"
        ) {
          invalidTokens.push(token);
        }
        results.push({ token, status: "failed", error: result.error });
      }
    }

    if (invalidTokens.length > 0) {
      const { error: deleteError } = await supabaseInstance
        .from("FCM_Tokens")
        .delete()
        .in("fcmToken", invalidTokens)
        .eq("appName", appName); // Add appName to deletion query

      if (deleteError) {
        console.error("Error removing invalid tokens:", deleteError);
      }
    }

    const successCount = results.filter((r) => r.status === "success").length;
    const failureCount = results.filter((r) => r.status === "failed").length;

    return {
      success: true,
      message: "Broadcast notification process completed",
      totalProcessed: fcmTokens.length,
      successCount,
      failureCount,
      invalidTokensRemoved: invalidTokens.length,
      results,
    };
  } catch (error) {
    console.error("Error sending admin notifications:", error);
    return { success: false, error };
  }
}

// Helper function to fetch FCM tokens for multiple apps and customers
async function fetchFCMTokens(customerAuthUIDs, appNames) {
  try {
    const tokens = {};
    for (const appName of appNames) {
      const { data, error } = await supabaseInstance
        .from("FCM_Tokens")
        .select("fcmToken, customerAuthUID")
        .in("customerAuthUID", customerAuthUIDs)
        .eq("appName", appName);

      if (error) throw error;
      tokens[appName] = data || [];
    }
    return { success: true, tokens };
  } catch (error) {
    console.error("Error fetching FCM tokens:", error);
    return { success: false, error };
  }
}

// get fcm tokens of all outlet users (staff/admin)
async function getOutletUserFcmTokens({outletId, userType=null, appName=null, requiredFields=["fcmToken", "userAuthUID", "userType", "appName"]}) {
  try {
    let query =  supabaseInstance
      .from("FCM_Token_Outlet")
      .select(requiredFields.join(", "))
      .eq("outletId", outletId);

    if (userType) {
      query = query.eq("userType", userType);
    }

    if (appName) {
      query = query.eq("appName", appName);
    }

    const { data, error } = await query;

    if (error) throw error;
    console.log(`Fetched ${data.length} FCM tokens for outletId: ${outletId}`);
    return data;
  } catch (error) {
    console.error("Error fetching FCM tokens:", error);
    return [];
  }
}   


async function sendOutletUserNotifications(title, body, fcmData) {
  try {
    console.log("[sendOutletUserNotifications] Params:", {
      title,
      body,
      fcmData,
    });

    /**
     * [sendOutletUserNotifications] Params: {
  title: 'Order Cancelled',
  body: 'Order #391 has been cancelled.',
  fcmData: [
    {
      fcmToken: 'eeeCku37FUZ8jpP8DDjmsS:APA91bFyfQ30_zgs-l7BEP_SPVgML1My2f9A9AP1mI1pBtwEwo5nak-Xc4n9ZaGy0hjDKS1DqdcGcGeSCOqW9_3Glucn-Nrlos-OS9WbkkKLk5NNhOFeQBA',
      appName: 'com.mealpe.food.vender'
    },
    {
      fcmToken: 'fkLC4xobSuedTu1IymsuSG:APA91bG_PBjERj1dDQsEz_9ToS7yHipODkLFmh9d6ZVe47o9xIrchFwC-KkCaUSIRkUtW4mVKxKuHTlqsfFhPuK-dRvJoj7VXUXWdMXNGGxGaWne24ZHzv0',
      appName: 'com.mealpe_foodvender_app'
    }
  ]
}
     */
    if (!title || !body || !fcmData) {
      console.error(
        "[sendOutletUserNotifications] Missing required parameters"
      );
      return { success: false, error: "Missing required parameters" };
    }

    const results = [];
    const invalidTokens = [];

    for (const { fcmToken, appName } of fcmData) {
      const result = await sendNotification(fcmToken, title, body, appName);
      if (result.success) {
        results.push({ token: fcmToken, status: "success" });
      } else {
        if (
          result.code === "messaging/invalid-argument" ||
          result.code === "messaging/registration-token-not-registered"
        ) {
          invalidTokens.push(fcmToken);
        }
        results.push({ token: fcmToken, status: "failed", error: result.error });
      }
    }

    if (invalidTokens.length > 0) {
      const { error: deleteError } = await supabaseInstance
        .from("FCM_Token_Outlet")
        .delete()
        .in("fcmToken", invalidTokens);

      if (deleteError) {
        console.error(
          "[sendOutletUserNotifications] Error removing invalid tokens:",
          deleteError
        );
      }
    }

    const successCount = results.filter((r) => r.status === "success").length;
    const failureCount = results.filter((r) => r.status === "failed").length;

    return {
      success: true,
      message: "Broadcast notification process completed",
      totalProcessed: fcmData.length,
      successCount,
      failureCount,
      invalidTokensRemoved: invalidTokens.length,
      results,
    };

  } catch (error) {
    console.error(
      "[sendOutletUserNotifications] Error sending outlet user notifications:",
      error
    );
    return { success: false, error };
  }
}
  
module.exports = router;
module.exports.sendNotification = sendNotification;
module.exports.sendStatusUpdateNotification = sendStatusUpdateNotification;
module.exports.getFCMTokenOfUser = getFCMTokenOfUser;
module.exports.sendAdminNotifications = sendAdminNotifications;
module.exports.sendMessNotification = sendMessNotification;
module.exports.sendOutletNotifications = sendOutletNotifications;
module.exports.fetchFCMTokens = fetchFCMTokens;
module.exports.getOutletUserFcmTokens = getOutletUserFcmTokens;
module.exports.sendOutletUserNotifications = sendOutletUserNotifications;