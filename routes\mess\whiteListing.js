let express = require("express");
let router = express.Router();
let supabaseInstance = require("../../services/supabaseClient.js").supabase;
let transporter = require("../../services/nodemailer.js");
let invite = require("../../emailTemplates/invite.html.js");
const logger = require("../../services/logger");
const { validateWhiteListUser, validateEditWhitelists, validateWhitelistUsers } = require("../../validators/mess/whitelistingValidator.js");

/* 
Mess_Whitelisting Table

whitelistingId	
bigint
number	

outletId	
uuid
string	

customerAuthUID	
uuid
string

*/

router.get("/", async (req, res) => {
  return res.status(200).json({
    success: true,
    message: "Response from mess/whiteListing.js",
  });
});

//optimized mail sending api 2024-11-26
router.post("/whiteListUsers", validateWhitelistUsers,  async (req, res) => {
  const { userObjs, outletId, mealTypeIds, validity } = req.body;

  const { validFrom, validTill } = validity;

  // validate validFrom and validTill
  if (new Date(validFrom) >= new Date(validTill)) {
    return res
      .status(400)
      .json({ success: false, message: "validFrom must be before validTill" });
  }

  try {
    // Fetch outlet data
    const { data: outletData, error: outletError } = await supabaseInstance
      .from("Outlet")
      .select("deepLink, canInviteUser, enableWhitelistDuration")
      .eq("outletId", outletId)
      .single();

    if (outletError || !outletData) {
      throw new Error("Error in fetching outlet data");
    }

    const { deepLink, canInviteUser, enableWhitelistDuration } = outletData;

    if (!canInviteUser) {
      return res.status(400).json({
        success: false,
        message: "This outlet does not allow user invitations"
      });
    }

    // Fetch all users in one query
    const emails = userObjs.map(user => user.email);
    const { data: users, error: usersError } = await supabaseInstance
      .from("Customer")
      .select("email, customerAuthUID")
      .in("email", emails)
      .eq("isDelete", false);

    if (usersError) {
      throw new Error("Error fetching user data");
    }

    // Create a map for quick lookup
    const userMap = new Map(users.map(user => [user.email, user.customerAuthUID]));

    // Fetch existing whitelist entries
    const { data: existingWhitelist, error: whitelistError } = await supabaseInstance
      .from("Mess_Whitelist")
      .select("customerAuthUID")
      .in("customerAuthUID", users.map(u => u.customerAuthUID))
      .eq("outletId", outletId);

    if (whitelistError) {
      logger.error("Error checking whitelist:", whitelistError);
      throw new Error("Error checking whitelist");
    }

    const whitelistedSet = new Set(existingWhitelist.map(w => w.customerAuthUID));

    const results = await Promise.all(userObjs.map(async ({ email }) => {
      const customerAuthUID = userMap.get(email);

      if (!customerAuthUID) {
        // User not found, send invitation email
        try {
          await transporter.sendMail({
            from: "MealPe <<EMAIL>>",
            to: email,
            subject: "Your Meal, Your Way – Join MealPe Mess Manager Today!",
            html: invite(deepLink),
          });
          return { email, status: "Invitation sent" };
        } catch (error) {
          return { email, status: "Error sending email invitation", error };
        }
      }

      if (whitelistedSet.has(customerAuthUID)) {
        return { email, status: "Already WhiteListed" };
      }

      // Prepare whitelist data
      let whitelistData = {
        customerAuthUID,
        outletId,
        mealTypeIds,
        validFrom,
        validTill
      };



      // Insert new whitelist entry
      const { error: insertError } = await supabaseInstance
        .from("Mess_Whitelist")
        .insert([whitelistData]);

      return insertError
        ? { email, status: "Error in inserting" }
        : { email, status: "WhiteListed" };
    }));

    const whiteListedUsers = results.filter(r => r.status === "WhiteListed");
    const notWhiteListedUsers = results.filter(r => r.status !== "WhiteListed");

    const invitedUsers = notWhiteListedUsers.filter(r => r.status === "Invitation sent");
    const alreadyWhitelistedUsers = notWhiteListedUsers.filter(r => r.status == "Already WhiteListed");

    return res.status(200).json({
      success: whiteListedUsers.length > 0,
      message: `${whiteListedUsers.length} users whitelisted. ${alreadyWhitelistedUsers.length} already whitelisted. ${invitedUsers.length} invitations sent.`,
      whiteListedUsers,
      invitedUsers,
      alreadyWhitelistedUsers,
    });
  } catch (error) {
    return res.status(500).json({ success: false, message: error.message });
  }
});

router.post("/dev/whiteListUsersFromXLSX", async (req, res) => {
  const { userObjs, outletId } = req.body;

  if (!userObjs || !outletId) {
    return res.status(400).json({ success: false, message: `Missing required fields : ${userObjs} or ${outletId}` });
  }

  try {
    // Fetch outlet data
    const { data: outletData, error: outletError } = await supabaseInstance
      .from("Outlet")
      .select("deepLink, canInviteUser")
      .eq("outletId", outletId)
      .single();

    if (outletError || !outletData) {
      throw new Error("Error in fetching outlet data");
    }

    const { canInviteUser } = outletData;

    if (!canInviteUser) {
      return res.status(400).json({ success: false, message: "This outlet does not allow user invitations" });
    }

    // Fetch all users in one query
    const emails = userObjs.map(user => user.email);
    const { data: users, error: usersError } = await supabaseInstance
      .from("Customer")
      .select("email, customerAuthUID, mobile")
      .in("email", emails)
      .eq("isDelete", false);

    if (usersError) {
      throw new Error("Error fetching user data");
    }

    // Create a map for quick lookup
    const userMap = new Map(users.map(user => [user.email, user.customerAuthUID]));

    // Fetch existing whitelist entries
    const { data: existingWhitelist, error: whitelistError } = await supabaseInstance
      .from("Mess_Whitelist")
      .select("customerAuthUID")
      .in("customerAuthUID", users.map(u => u.customerAuthUID))
      .eq("outletId", outletId);

    if (whitelistError) {
      throw new Error("Error checking whitelist");
    }

    const whitelistedSet = new Set(existingWhitelist.map(w => w.customerAuthUID));

    const results = await Promise.all(userObjs.map(async ({ email, phone, name }) => {
      let customerAuthUID = userMap.get(email);

      // If user doesn't exist, create new user
      if (!customerAuthUID) {
        try {
          // Check if user exists with same mobile
          if (phone) {
            const { data: existingUserMobile, error: existingUserMobileError } = await supabaseInstance
              .from("Customer")
              .select("*")
              .eq("mobile", phone)
              .eq("isDelete", false);

            if (existingUserMobileError && (existingUserMobileError.code !== "42P01" || existingUserMobileError.code !== "PGRST116")) {
              throw existingUserMobileError;
            }

            if (existingUserMobile?.length > 0) {
              return { email, status: "Mobile number already exists", error: "Mobile number already exists" };
            }
          }

          // Create user in Supabase Auth
          const { data: authData, error: authError } = await supabaseInstance.auth.admin.createUser({
            email,
            phone: phone ? phone.toString() : null,
            phone_confirm: true,
          });

          if (authError) {
            return { email, status: "Error creating user in auth", error: authError.message };
          }

          if (authData?.user) {
            customerAuthUID = authData.user.id;

            // Create user in Customer table
            const customerResponse = await supabaseInstance
              .from("Customer")
              .insert({
                email,
                mobile: phone,
                customerName: name,
                customerAuthUID,
              })
              .select("*")
              .maybeSingle();

            if (customerResponse.error) {
              return { email, status: "Error creating user in database", error: customerResponse.error.message };
            }
          }
        } catch (error) {
          return { email, status: "Error in user creation", error: error.message };
        }
      }

      if (!customerAuthUID) {
        return { email, status: "Failed to create or find user" };
      }

      if (whitelistedSet.has(customerAuthUID)) {
        return { email, status: "Already WhiteListed" };
      }

      // Insert new whitelist entry
      const { error: insertError } = await supabaseInstance
        .from("Mess_Whitelist")
        .insert([{ customerAuthUID, outletId }]);

      return insertError
        ? { email, status: "Error in inserting to whitelist", error: insertError.message }
        : { email, status: "WhiteListed" };
    }));

    const whiteListedUsers = results.filter(r => r.status === "WhiteListed");
    const notWhiteListedUsers = results.filter(r => r.status !== "WhiteListed");

    const alreadyWhitelistedUsers = notWhiteListedUsers.filter(r => r.status === "Already WhiteListed");
    const errorUsers = notWhiteListedUsers.filter(r => r.error);

    return res.status(200).json({
      success: whiteListedUsers.length > 0,
      message: `${whiteListedUsers.length} users whitelisted. ${alreadyWhitelistedUsers.length} already whitelisted. ${errorUsers.length} errors occurred.`,
      whiteListedUsers,
      alreadyWhitelistedUsers,
      errorUsers,
    });
  } catch (error) {
    logger.error("Error in whitelisting users from XLSX:", error);
    return res.status(500).json({ success: false, message: error.message });
  }
});



/*
// mailing api for whiteListing users {prod}
router.post("/whiteListUsers", async (req, res) => {
  const { userObjs, outletId } = req.body;

  if (!userObjs || !outletId) {
    return res
      .status(400)
      .json({ success: false, message: "Missing required fields" });
  }
  
  let whiteListedUsers = [];
  let notWhiteListedUsers = [];

  // Fetch outlet data once for all users
  const { data: outletData, error: outletError } = await supabaseInstance
    .from("Outlet")
    .select("deepLink, canInviteUser, hasMess")
    .eq("outletId", outletId)
    .single();

  if (outletError || !outletData) {
    return res
      .status(400)
      .json({ success: false, message: "Error in fetching outlet data" });
  }

  const { deepLink, canInviteUser } = outletData;

  if (!canInviteUser) {
    return res
      .status(400)
      .json({ success: false, message: "This outlet does not allow user invitations" });
  }

  for (let i = 0; i < userObjs.length; i++) {
    let { email } = userObjs[i];
    let { data: userData, error: userError } = await supabaseInstance
      .from("Customer")
      .select("customerAuthUID")
      .eq("email", email)
      .eq("isDelete", false)
      .single();

    if (userError && userError.code !== "PGRST116") {
      notWhiteListedUsers.push({ email, status: "Error in fetching user data" });
      continue;
    }

    if (!userData) {
      // User not found, send invitation email
      const mailOptions = {
        from: "MealPe <<EMAIL>>",
        to: email,
        subject: "Your Meal, Your Way – Join MealPe Mess Manager Today!",
        html: invite(deepLink),
      };

      try {
        // await transporter.sendMail(mailOptions);
        notWhiteListedUsers.push({ email, status: "Invitation sent" });
      } catch (error) {
        notWhiteListedUsers.push({ email, status: "Error sending invitation" });
      }
      continue;
    }

    let customerAuthUID = userData.customerAuthUID;

    let { data: whiteListData, error: whiteListError } = await supabaseInstance
      .from("Mess_Whitelist")
      .select("*")
      .eq("customerAuthUID", customerAuthUID)
      .eq("outletId", outletId)
      .single();

    if (whiteListError && whiteListError.code !== "PGRST116") {
      notWhiteListedUsers.push({ email, status: "Error checking whitelist" });
      continue;
    }

    if (whiteListData) {
      notWhiteListedUsers.push({ email, status: "Already WhiteListed" });
      continue;
    } else {
      let { error: insertError } = await supabaseInstance
        .from("Mess_Whitelist")
        .insert([{ customerAuthUID, outletId }]);

      if (insertError) {
        notWhiteListedUsers.push({
          email,
          status: "Error in inserting",
        });
        continue;
      }

      whiteListedUsers.push({ email, status: "WhiteListed" });
    }
  }

  return res.status(200).json({
    success: whiteListedUsers.length > 0,
    message: `${whiteListedUsers.length} users whitelisted successfully. ${notWhiteListedUsers.length} users failed or were already whitelisted.`,
    whiteListedUsers,
    notWhiteListedUsers,
  });
});
*/

/**
 * 
router.post("/whiteListUsers", async (req, res) => {
  const { userObjs } = req.body;
  
  if (!userObjs) {
    
    return res
      .status(400)
      .json({ success: false, message: "Missing required fields" });
  }
  
  let whiteListedUsers = [];
  let notWhiteListedUsers = [];

  for (let i = 0; i < userObjs.length; i++) {
    let email = userObjs[i].email;
    let { data: userData, error: userError } = await supabaseInstance
      .from("Customer")
      .select("customerAuthUID")
      .eq("email", email)
      .eq("isDelete", false)
      .single();

    if (userError) {
      notWhiteListedUsers.push({ email: email, status: "Error" });
      continue;
    }

    let customerAuthUID = userData.customerAuthUID;
    let outletId = req.body.outletId;

    let { data: whiteListData, error: whiteListError } = await supabaseInstance
      .from("Mess_Whitelist")
      .select("*")
      .eq("customerAuthUID", customerAuthUID)
      .eq("outletId", outletId)
      .single();

    if (whiteListError && whiteListError.code !== "PGRST116") {
      notWhiteListedUsers.push({ email: email, status: "Not Found" });
      continue;
    }

    if (whiteListData) {
      notWhiteListedUsers.push({ email: email, status: "Already WhiteListed" });
      continue;
    } else {
      let { error: insertError } = await supabaseInstance
        .from("Mess_Whitelist")
        .insert([{ customerAuthUID, outletId }]);

      if (insertError) {
        notWhiteListedUsers.push({
          email: email,
          status: "Error in inserting",
        });
        continue;
      }

      whiteListedUsers.push({ email: email, status: "WhiteListed" });
    }
  }

  if (notWhiteListedUsers.length === 0) {
    return res
      .status(200)
      .json({
        success: true,
        message: `Users whiteListed successfully!`,
        whiteListedUsers,
        length: notWhiteListedUsers.length,
      });
  } else {
    return res
      .status(200)
      .json({
        success: false,
        message: `Users whiteListed successfully! ${notWhiteListedUsers.length} users failed!`,
        whiteListedUsers,
        notWhiteListedUsers,
        length: notWhiteListedUsers.length,
      });
  }
});
*/

router.post("/whiteListUser", validateWhiteListUser , async (req, res) => {
  const { email, outletId, mealTypeIds, validity } = req.body;

  // Validate required fields
  if (!email || !outletId || !mealTypeIds || !validity) {
    return res
      .status(400)
      .json({ success: false, message: "Missing required fields" });
  }

  const { validFrom, validTill } = validity;

  // validate validFrom and validTill
  if (new Date(validFrom) >= new Date(validTill)) {
    return res
      .status(400)
      .json({ success: false, message: "validFrom must be before validTill" });
  }

  try {
    // Check if the user exists in the Customer table
    const { data: userData, error: userError } = await supabaseInstance
      .from("Customer")
      .select("customerAuthUID")
      .eq("email", email)
      .eq("isDelete", false)
      .single();

    if (userError && userError.code !== "PGRST116") {
      return res
        .status(400)
        .json({ success: false, message: "Error in getting user data" });
    }

    if (!userData) {
      // User not found, fetch deepLink and send invitation email
      const { data: ouetletData, error: outletError } = await supabaseInstance
        .from("Outlet")
        .select("deepLink, canInviteUser, hasMess")
        .eq("outletId", outletId)
        .single();

      if (outletError || !ouetletData) {
        return res
          .status(400)
          .json({ success: false, message: "Error in fetching deepLink" });
      }

      const deepLink = ouetletData.deepLink;
      const canInviteUser = ouetletData.canInviteUser;

      if (!canInviteUser) {
        return res
          .status(400)
          .json({ success: false, message: "No user found with this mail" });
      }

      // Send email invitation
      const mailOptions = {
        from: "MealPe <<EMAIL>>",
        to: email,
        subject: "Your Meal, Your Way – Join MealPe Mess Manager Today!",
        html: invite(deepLink),
      };

      // Use a promise to handle async email sending and capture errors
      transporter.sendMail(mailOptions, function (error, info) {
        if (error) {
          return res.status(500).json({
            success: false,
            message: "Error in sending invitation email.",
            error,
          });
        } else {
          return res.status(200).json({
            success: false,
            message: "User not found. Invitation sent successfully.",
          });
        }
      });

      // Exit the function early since email sending response is handled
      return;
    }

    // User found, check if already whitelisted
    const customerAuthUID = userData.customerAuthUID;

    const { data: whiteListData, error: whiteListError } = await supabaseInstance
      .from("Mess_Whitelist")
      .select("*")
      .eq("customerAuthUID", customerAuthUID)
      .eq("outletId", outletId)
      .single();

    if (whiteListError && whiteListError.code !== "PGRST116") {
      return res
        .status(400)
        .json({ success: false, message: "Error in fetching whitelist data" });
    }

    if (whiteListData) {
      // User is already whitelisted
      return res
        .status(400)
        .json({
          success: false,
          message: "User already whitelisted. You can edit the record.",
          // existingWhitelist: whiteListData
        });
    }

    // Prepare whitelist data
    let whitelistData = {
      customerAuthUID,
      outletId,
      mealTypeIds: mealTypeIds || [1, 2, 3, 4], // Default to all meals if not specified
      validFrom,
      validTill
    };

    // Insert new whitelist entry
    const { error: insertError } = await supabaseInstance
      .from("Mess_Whitelist")
      .insert([whitelistData]);

    if (insertError) {
      return res.status(400).json({
        success: false,
        message: "Error in inserting whitelist data",
      });
    }

    return res
      .status(200)
      .json({ success: true, message: "User whitelisted successfully!" });

  } catch (error) {
    logger.error("Error in whitelisting user:", error);
    return res
      .status(500)
      .json({ success: false, message: "Internal server error", error: error.message });
  }
});


// router.post("/whiteListUser", async (req, res) => {
//   const { email, outletId, phone } = req.body;

//   if (!email || !outletId ) {
//     return res
//       .status(400)
//       .json({ success: false, message: "Missing required fields" });
//   }

//   let { data: userData, error: userError } = await supabaseInstance
//     .from("Customer")
//     .select("customerAuthUID")
//     .eq("email", email)
//     .eq("isDelete", false)
//     .single();

//   console.log("userData", userData);

//   if (userError && userError.code !== "PGRST116") {
//     return res
//       .status(400)
//       .json({ success: false, message: "Error in getting user data" });
//   }

//   if (!userData) {

//     const {data:deepLinkData, error:deepLinkError} = await supabaseInstance
//     .from("Outlet")
//     .select("deepLink")
//     .eq("outletId", outletId)
//     .single();

//     if(deepLinkError) {
//       return res.status(400).json({ success: false, message: "Error in getting deepLink" });
//     }

//     if(!deepLinkData) {
//       return res.status(400).json({ success: false, message: "DeepLink not found" });
//     }

//     let deepLink = deepLinkData.deepLink;

//     let mailOptions = {
//       from: "<EMAIL>",
//       to: email,
//       subject: "Invitation to WhiteList",
//       html: invite(deepLink),
//     };

//     transporter.sendMail(mailOptions, function (error, info) {
//       if (error) {
//         console.log("error in sending mail", error);
//       } else {
//         console.log("Email sent: " + info.response);
//       }
//     });

    
//     return res
//       .status(200)
//       .json({ success: false, message: "User not found with email" });
//   }

//   let customerAuthUID = userData.customerAuthUID;

//   // check in db if the user is already whiteListed or not.
//   let { data: whiteListData, error: whiteListError } = await supabaseInstance
//     .from("Mess_Whitelist")
//     .select("*")
//     .eq("customerAuthUID", customerAuthUID)
//     .eq("outletId", outletId)
//     .single();

//   if (whiteListError && whiteListError.code !== "PGRST116") {
//     return res
//       .status(400)
//       .json({ success: false, message: "Error in getting whiteList data" });
//   }

//   if (whiteListData) {
//     return res
//       .status(200)
//       .json({ success: false, message: "User is already whiteListed" });
//   }

//   let { error: insertError } = await supabaseInstance
//     .from("Mess_Whitelist")
//     .insert([{ customerAuthUID, outletId }]);

//   if (insertError) {
//     return res.status(400).json({
//       success: false,
//       message: "Error in inserting whiteList data",
//     });
//   }

//   return res
//     .status(200)
//     .json({ success: true, message: "User whiteListed successfully !" });
// });

router.get("/getWhitelistedUsers/:outletId", async (req, res) => {
  const { outletId } = req.params;
  let {startDateTime, endDateTime} = req.query;

  if (!outletId) {
    return res
      .status(400)
      .json({ success: false, message: "Missing outletId" });
  }

  try {
    let { data: whiteListData, error: whiteListError } = await supabaseInstance
      .from("Mess_Whitelist")
      .select("*, Customer(email, mobile, customerName, rfid)")
      .eq("outletId", outletId);

    if (whiteListError) throw whiteListError;

    let rsvpQuery = supabaseInstance
      .from("RSVP")
      .select("customerAuthUID, mealTypeId")
      .eq("outletId", outletId);

    let actualQuery = supabaseInstance
      .from("Meals_Served")
      .select("customerAuthUID, mealTypeId")
      .eq("outletId", outletId);


    if (startDateTime && endDateTime) {
      //convert to timestampz
      startDateTime = new Date(startDateTime).toISOString();
      
      // make enddate time to end of the day
      endDateTime = new Date(endDateTime);
      endDateTime.setHours(23, 59, 59, 999);
      endDateTime = endDateTime.toISOString();
      rsvpQuery = rsvpQuery
        .gte("created_at", startDateTime)
        .lte("created_at", endDateTime);

      actualQuery = actualQuery
        .gte("created_at", startDateTime)
        .lte("created_at", endDateTime);
    }

    let { data: rsvpData, error: rsvpError } = await rsvpQuery;
    if(rsvpError) throw rsvpError;

    let { data: actualData, error: actualError } = await actualQuery;
    if(actualError) throw actualError;

    whiteListData = whiteListData.map((data) => {
      let rsvpCount = rsvpData?.filter(
        (rsvp) => rsvp.customerAuthUID === data.customerAuthUID
      );
      let actualCount = actualData?.filter(
        (actual) => actual?.customerAuthUID === data?.customerAuthUID
      );

      let rsvpCountObj = {
        "1" : 0,
        "2" : 0,
        "3" : 0,
        "4" : 0,
      };
      let actualCountObj = {
        "1" : 0,
        "2" : 0,
        "3" : 0,
        "4" : 0,
      };

      rsvpCount.forEach((rsvp) => {
        rsvpCountObj[rsvp.mealTypeId] = rsvpCountObj[rsvp.mealTypeId]
          ? rsvpCountObj[rsvp.mealTypeId] + 1
          : 1;
      });

      actualCount.forEach((actual) => {
        actualCountObj[actual.mealTypeId] = actualCountObj[actual.mealTypeId]
          ? actualCountObj[actual.mealTypeId] + 1
          : 1;
      });

      let differenceObj = {};

      Object.keys(rsvpCountObj).forEach((mealType) => {
        differenceObj[mealType] =
          (rsvpCountObj[mealType] - actualCountObj[mealType]) || rsvpCountObj[mealType];
      });

      return {
        whitelistingId: data.whitelistingId,
        created_at: data.created_at,
        outletId: data.outletId,
        customerAuthUID: data.customerAuthUID,
        mealTypeIds: data.mealTypeIds,
        validity: {
          validFrom: data.validFrom,
          validTill: data.validTill,
        },
        subscription_active: data.subscription_active,
        Customer: {
          email: data.Customer.email,
          mobile: data.Customer.mobile,
          rfid: data.Customer.rfid,
          customerName: data.Customer.customerName,
          rsvpCount: rsvpCountObj,
          actualCount: actualCountObj,
          difference: differenceObj,
        },
      };
    });
    return res.status(200).json({ success: true, data: whiteListData });
  } catch (error) {
    return res.status(500).json({ success: false, error: error.message });
  }
});

router.post("/deleteWhitelistedUser", async (req, res) => {
  const customerAuthUID = req.query.cuid;
  const { outletId } = req.query;

  if (!customerAuthUID || !outletId) {
    return res
      .status(400)
      .json({ success: false, message: "Missing required fields" });
  }

  try {
    let { error: deleteError } = await supabaseInstance
      .from("Mess_Whitelist")
      .delete()
      .eq("customerAuthUID", customerAuthUID)
      .eq("outletId", outletId);

    if (deleteError) throw deleteError;

    return res
      .status(200)
      .json({ success: true, message: "User removed from whiteList" });
  } catch (error) {
    return res.status(500).json({ success: false, error: error.message });
  }
});

router.get("/getWhitelistedUsersExcel/:outletId", async (req, res) => {
  const { outletId } = req.params;

  if (!outletId) {
    return res
      .status(400)
      .json({ success: false, message: "Missing outletId" });
  }

  try {
    let { data: whiteListData, error: whiteListError } = await supabaseInstance
      .from("Mess_Whitelist")
      .select("*,Customer(email, mobile, customerName)")
      .eq("outletId", outletId);

    if (whiteListError) throw whiteListError;

    let { data: rsvpData, error: rsvpError } = await supabaseInstance
      .from("RSVP")
      .select("customerAuthUID, mealTypeId")
      .eq("outletId", outletId);

    if (rsvpError) throw rsvpError;

    let { data: actualData, error: actualError } = await supabaseInstance
      .from("Meals_Served")
      .select("customerAuthUID, mealTypeId")
      .eq("outletId", outletId);

    if (actualError) throw actualError;

    // also add the rsvp and actual counts for that user
    whiteListData = whiteListData.map((data) => {
      let rsvpCount = {"1" : 0, "2" : 0, "3" : 0, "4" : 0};
      let actualCount = {"1" : 0, "2" : 0, "3" : 0, "4" : 0};
      let difference = {"1" : 0, "2" : 0, "3" : 0, "4" : 0};

      rsvpData.forEach((rsvp) => {
        if (rsvp.customerAuthUID === data.customerAuthUID) {
          rsvpCount[rsvp.mealTypeId] = rsvpCount[rsvp.mealTypeId]
            ? rsvpCount[rsvp.mealTypeId] + 1
            : 1;
        }
      });

      actualData.forEach((actual) => {
        if (actual.customerAuthUID === data.customerAuthUID) {
          actualCount[actual.mealTypeId] = actualCount[actual.mealTypeId]
            ? actualCount[actual.mealTypeId] + 1
            : 1;
        }
      });

      Object.keys(rsvpCount).forEach((mealType) => {
        difference[mealType] = rsvpCount[mealType] - actualCount[mealType];
      });


      return {
        email: data.Customer.email,
        mobile: data.Customer.mobile,
        customerName: data.Customer.customerName,
        mealTypeIds: data.mealTypeIds,
        validity: {
          validFrom: data.validFrom,
          validTill: data.validTill,
        },
        subscription_active: data.subscription_active,
        rsvpCount,
        actualCount,
        difference,
      };
    });

    return res.status(200).json({ success: true, data: whiteListData });
  } catch (error) {
    return res.status(500).json({ success: false, error: error.message });
  }
});

// post /mess/whiteListing/editWhitelists (bulk)
router.post('/editWhitelists', validateEditWhitelists, async (req, res) => {
  const { customerAuthUIDs, mealTypeIds, validity, outletId } = req.body;

  // Validate mealTypeIds values
  const allowedMeals = [1, 2, 3, 4];
  const invalidMeals = mealTypeIds.filter((m) => !allowedMeals.includes(m));
  if (invalidMeals.length > 0) {
    return res.status(400).json({ success: false, message: `Invalid mealTypeIds: ${invalidMeals.join(', ')}` });
  }

  try {

    // Fetch whitelist rows that belong to this outlet
    const { data: rows, error: rowsErr } = await supabaseInstance
      .from('Mess_Whitelist')
      .select('whitelistingId, customerAuthUID')
      .in('customerAuthUID', customerAuthUIDs)
      .eq('outletId', outletId);

    if (rowsErr) return res.status(400).json({ success: false, message: 'Error fetching whitelist records' });

    const foundIds = new Set((rows || []).map((r) => r.whitelistingId));
    
    // calculate subscription_active using current date between validFrom and validTill
    const currentDate = new Date();
    const subscription_active = validity && validity.validFrom && validity.validTill
      ? (new Date(validity.validFrom) <= currentDate && new Date(validity.validTill) >= currentDate)
      : false;

    const updates = [];
    for (const id of foundIds) {
      const payload = { mealTypeIds, validFrom: validity?.validFrom, validTill: validity?.validTill, subscription_active };
      updates.push(
        supabaseInstance
          .from('Mess_Whitelist')
          .update(payload)
          .eq('whitelistingId', id)
          .select('*')
          .single()
      );
    }

    const settled = await Promise.allSettled(updates);
    const updated = [];
    const errors = [];

    settled.forEach((result, idx) => {
      const id = Array.from(foundIds)[idx];
      if (result.status === 'fulfilled') {
        const { data } = result.value;
        updated.push({
          whitelistingId: data.whitelistingId,
          outletId: data.outletId,
          customerAuthUID: data.customerAuthUID,
          mealTypeIds: data.mealTypeIds,
          validity: { validFrom: data.validFrom, validTill: data.validTill },
          subscription_active: data.subscription_active,
        });
      } else {
        errors.push({ whitelistingId: id, message: result.reason?.message || 'Failed to update whitelist' });
      }
    });

    return res.status(200).json({
      success: updated.length > 0,
      message: `Update Successful. ${errors.length > 0 ? `Updated: ${updated.length}, Errors in ${errors.length}` : ''}`,
    });

  } catch (error) {
    logger.error('editWhitelists error:', error);
    return res.status(500).json({ success: false, message: 'Internal server error' });
  }
});


module.exports = router;
