var express = require("express");
var router = express.Router();
var surePassConfig = require("../configs/surepassConfig");
const { default: axios } = require("axios");
const logger = require("../services/logger");

router.get("/", function (req, res, next) {
    res.send({ success: true, message: "respond send from surepass.js" });
});

/**
 * @description This route is used to verify the GST number using SurePass API.
 * @route POST /surepass/gstVerification
 * @body {string} gstNumber - The GST number to be verified.
 * @return {object} - Returns a success message and the verification data if successful.
 * @return {object} - Returns an error message if the verification fails.
 */
router.post("/gstVerification", async (req, res) => {
    const { gstNumber } = req.body;
    try {
        const response = await axios.post(surePassConfig.config.surepass_gst_api, { "id_number": gstNumber, "filing_status_get": true }, {
            headers: { Authorization: `Bearer ${surePassConfig.config.TOKEN}` }
        })

        if (response?.data) {
            res.status(200).json({
                success: true,
                message: "GST Verfied succesfully",
                data: response.data,
            });
        } else {
            throw response.error
        }
    } catch (error) {
        logger.error("Error in surepass/bankDetailsVerification", error);
        res.status(500).json({ success: false, error: error.message });
    }
});

/**
 * @description This route is used to verify the bank details using SurePass API.
 * @route POST /surepass/bankDetailsVerification
 * @body {string} accountNo - The bank account number to be verified.
 * @body {string} IFSC_Code - The IFSC code of the bank to be verified.
 * @return {object} - Returns a success message and the verification data if successful.
 */
router.post("/bankDetailsVerification", async (req, res) => {
    const { accountNo, IFSC_Code } = req.body;
    try {

        let postBody = {
            id_number: accountNo,
            ifsc: IFSC_Code,
        };
        const response = await axios.post(surePassConfig.config.surepass_bankdatails_api, postBody, {
            headers: { Authorization: `Bearer ${surePassConfig.config.TOKEN}` }
        })

        if (response?.data) {
            res.status(200).json({
                success: true,
                message: "BankDetails Verfied succesfully",
                data: response.data,
            });
        } else {
            throw response.error
        }
    } catch (error) {
        logger.error("Error in surepass/bankDetailsVerification", error);
        res.status(500).json({ success: false, error: error.message });
    }
});

/**
 * @description This route is used to verify the PAN card using SurePass API.
 * @route POST /surepass/panCardVerification
 * @body {string} panCardNumber - The PAN card number to be verified.
 * @return {object} - Returns a success message and the verification data if successful.
 */
router.post("/panCardVerification", async (req, res) => {
    const { panCardNumber } = req.body;
    try {
        const response = await axios.post(surePassConfig.config.surepass_pancard_api, { "id_number": panCardNumber }, {
            headers: { Authorization: `Bearer ${surePassConfig.config.TOKEN}` }
        })

        if (response?.data) {
            res.status(200).json({
                success: true,
                message: "PanCard Verfied succesfully",
                data: response.data,
            });
        } else {
            throw response.error
        }
    } catch (error) {
        logger.error("Error in surepass/panCardVerification", error);
        res.status(500).json({ success: false, error: error.message });
    }
});


module.exports = router;