const { handleValidationErrorsMiddleware } = require('../middleware/validationErrorMiddleware');
const { body } = require('express-validator');

//============================ serveMealFromRfid ============================
const serveMealFromRfidValidationRules = [
//rfid, outletId
    body('rfid')
        .exists({ checkFalsy: true }).withMessage('RFID is required.')
        .isString().withMessage('RFID must be a string.'),
    body('outletId')
        .exists({ checkFalsy: true }).withMessage('Outlet ID is required.')
        .isUUID().withMessage('Outlet ID must be a valid UUID.')
];

const validateServeMealFromRfid = [
    ...serveMealFromRfidValidationRules,
    handleValidationErrorsMiddleware
];
//============================ /serveMealFromRfid ============================


//============================ serveMealFromStaticQR ============================
const serveMealFromStaticQRValidationRules = [
    body('customerAuthUID')
        .exists({ checkFalsy: true }).withMessage('Customer Auth UID is required.')
        .isString().withMessage('Customer Auth UID must be a string.'),
    body('outletId')
        .exists({ checkFalsy: true }).withMessage('Outlet ID is required.')
        .isUUID().withMessage('Outlet ID must be a valid UUID.')
];

const validateServeMealFromStaticQR = [
    ...serveMealFromStaticQRValidationRules,
    handleValidationErrorsMiddleware
];
//============================ /serveMealFromStaticQR ============================


// Export validators and error handler using concise ES6 syntax
module.exports = {
    validateServeMealFromRfid,
    validateServeMealFromStaticQR
};