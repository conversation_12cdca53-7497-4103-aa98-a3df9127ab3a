const express = require("express");
const router = express.Router();
const messWeeklyMenuRouter = require("./weeklyMenu");
const rsvpRouter = require("./rsvp");
const dashboardRouter = require("./dashboard");
const whitelistRouter = require("./whiteListing");
const messNotificationsRouter = require("./messNotifications");
const guestMealsRouter = require("./guestMeals");
const supabaseInstance = require("../../services/supabaseClient").supabase;
const logger = require("../../services/logger");


router.use("/weeklyMenu", messWeeklyMenuRouter);
router.use("/rsvp", rsvpRouter);
router.use("/dashboard", dashboardRouter);
router.use("/mwl", whitelistRouter);
router.use("/messNotifications", messNotificationsRouter);
router.use("/guestMeals", guestMealsRouter);

router.get('/', (req, res, next) => {
    res.send({ success: true, message: "Response from mess/index.js" });
})

router.get('/getMessConfig/:outletId', async(req, res, next) => {
    const {outletId} = req.params;

    try{
        const {data,error} = await supabaseInstance
        .from("Outlet")
        .select("outletId, hasMess, hasScanner, rsvpCompulsion, isVisibleToAll, messNotifications, cashAndCarry, feedbackCompulsary, reviewCompulsary")
        .eq("outletId", outletId)
        .maybeSingle();

        if(error) throw error;

        if(data.length === 0){
            return res.status(404).send({success: false, message: "Outlet not found"});
        }

        return res.status(200).send({success: true, data });
    }
    catch(err){
        logger.error(err);
        return res.status(500).send({success: false, message: "Internal Server Error", error: err});
    }
})


module.exports = router;