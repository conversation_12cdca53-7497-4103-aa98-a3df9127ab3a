const { handleValidationErrorsMiddleware } = require('../middleware/validationErrorMiddleware');
const { body, param } = require('express-validator');

//============================ toggleCategoryStatus ============================
const toggleCategoryStatusValidationRules = [
    body('status')
        .exists({ checkFalsy: true }).withMessage('Status is required.')
        .isBoolean().withMessage('Status must be a boolean.'),
    param('categoryid')
        .exists({ checkFalsy: true }).withMessage('Category ID is required.')
        .isInt().withMessage('Category ID must be an integer.'),
];
//============================ /toggleCategoryStatus ============================

//============================ toggleParentCategoryStatus ============================
const validateToggleCategoryStatus = [
    ...toggleCategoryStatusValidationRules,
    handleValidationErrorsMiddleware
];

const toggleParentCategoryStatusValidationRules = [
    body('status')
        .exists({ checkFalsy: true }).withMessage('Status is required.')
        .isBoolean().withMessage('Status must be a boolean.'),
    param('parent_category_id')
        .exists({ checkFalsy: true }).withMessage('Parent Category ID is required.')
        .isInt().withMessage('Parent Category ID must be an integer.'),
];

const validateToggleParentCategoryStatus = [
    ...toggleParentCategoryStatusValidationRules,
    handleValidationErrorsMiddleware
];
//============================ /toggleParentCategoryStatus ============================

// Export validators and error handler using concise ES6 syntax
module.exports = {
    validateToggleCategoryStatus,
    validateToggleParentCategoryStatus
};