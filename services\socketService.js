const supabaseInstance = require("./supabaseClient").supabase;

require('dotenv').config();
const isDevMode = process.env.ENV === 'dev';
console.log('Running in dev mode:', isDevMode);

// Store active connections
const activeConnections = new Map();

/**
 * Helper to subscribe a socket to a room with validation, clean-up, and confirmation.
 * @param {Object} params
 * @param {Object} params.socket - The socket instance.
 * @param {string} params.roomName - The room to join.
 * @param {Object} params.confirmationPayload - Payload to emit on success.
 * @param {Function} [params.validate] - Optional validation function.
 * @returns {boolean} - True if subscribed, false otherwise.
 */
function subscribeToRoom({ socket, roomName, confirmationPayload, validate }) {
  try {
    if (validate && !validate()) {
      socket.emit('error', { success: false, message: 'Validation failed' });
      return false;
    }
    socket.join(roomName);
    socket.emit('subscription_confirmed', { success: true, ...confirmationPayload });
    return true;
  } catch (error) {
    console.error('Room subscription error:', error);
    socket.emit('error', { success: false, message: 'Subscription failed' });
    return false;
  }
}

/**
 * Helper to unsubscribe a socket from a room.
 * @param {Object} params
 * @param {Object} params.socket - The socket instance.
 * @param {string} params.roomName - The room to leave.
 */
function unsubscribeFromRoom({ socket, roomName }) {
  try {
    if (socket.rooms.has(roomName)) {
      socket.leave(roomName);
      socket.emit('unsubscribed', { success: true, room: roomName, message: `Unsubscribed from ${roomName}` });
    } else {
      socket.emit('unsubscribed', { success: false, room: roomName, message: `Not a member of ${roomName}` });
    }
  } catch (error) {
    console.error('Room unsubscription error:', error);
    socket.emit('error', { success: false, message: 'Unsubscription failed' });
  }
}

/**
 * Helper to emit an event to all sockets in a room, with acknowledgment and logging.
 * @param {Object} params
 * @param {Object} params.io - The socket.io server instance.
 * @param {string} params.roomName - The room to emit to.
 * @param {string} params.event - The event name.
 * @param {Object} params.payload - The payload to emit.
 * @returns {Promise<boolean>} - True if at least one client acknowledged, false otherwise.
 */
async function emitToRoom({ io, roomName, event, payload }) {
  try {
    const room = io.sockets.adapter.rooms.get(roomName);
    if (!room) {
      console.log(`[Socket Emit] No clients in room: ${roomName}`);
      return false;
    }

    // Emit with acknowledgment to all sockets in the room
    const emitPromises = Array.from(room).map(socketId => {
      return new Promise((resolve) => {
        const socket = io.sockets.sockets.get(socketId);
        if (socket) {
          socket.emit(event, payload, (ack) => {
            resolve(!!ack);
          });
        } else {
          resolve(false);
        }
      });
    });

    const results = await Promise.all(emitPromises);
    const successCount = results.filter(Boolean).length;

    console.log(`[Socket Emit] Event '${event}' emitted to ${successCount}/${room.size} clients in room '${roomName}'`);
    return successCount > 0;
  } catch (error) {
    console.error(`[Socket Emit] Error emitting event '${event}' to room '${roomName}':`, error);
    return false;
  }
}

module.exports = function(io) {
  // Error handling middleware
  io.use((socket, next) => {
    try {
      next();
    } catch (err) {
      console.error('Socket middleware error:', err);
      next(new Error('Internal server error'));
    }
  });

  io.on('connection', (socket) => {
    console.log('Client connected:', socket.id);

    // Add connection error handling
    socket.on('connect_error', (error) => {
      console.error('Connection error:', error);
    });

    // Handle client registration with user ID
    socket.on('register', (userId) => {
      try {
        if (userId) {
          activeConnections.set(userId, socket.id);
          socket.userId = userId;
          console.log(`User ${userId} registered with socket ${socket.id}`);
        }
      } catch (error) {
        console.error('Register error:', error);
      }
    });

    // Subscribe to meal status updates
    socket.on('subscribe_meal_status', (customerAuthUID) => {
      const customerId = typeof customerAuthUID === 'object' ? customerAuthUID.data : customerAuthUID;
      subscribeToRoom({
        socket,
        roomName: `meal_status_${customerId}`,
        confirmationPayload: {
          message: 'Successfully subscribed to meal status updates',
          customerAuthUID: customerId
        },
        validate: () => !!customerId
      });

    });

    // Subscribe to new order notifications for outlet
    socket.on('subscribe_new_orders', (outlet) => {
      const outletId = outlet?.outletId;
      subscribeToRoom({
        socket,
        roomName: `new_orders_${outletId}`,
        confirmationPayload: {
          message: 'Successfully subscribed to new order notifications',
          outletId,
          room: `new_orders_${outletId}`
        },
        validate: () => !!outletId
      });
    });

    // Subscribe to order cancellation status updates
    socket.on('subscribe_order_cancellation_status', (outlet) => {
      const outletId = outlet?.outletId;
      subscribeToRoom({
        socket,
        roomName: `order_cancellation_status_${outletId}`,
        confirmationPayload: {
          message: 'Successfully subscribed to order cancellation status updates',
          outletId,
          room: `order_cancellation_status_${outletId}`
        },
        validate: () => !!outletId
      });
    });

    socket.on('subscribe_clearance_orders', (outlet) => { 
      const outletId = outlet?.outletId;
      subscribeToRoom({
        socket,
        roomName: `clearance_orders_${outletId}`,
        confirmationPayload: {
          message: 'Successfully subscribed to clearance order notifications',
          outletId,
          room: `clearance_orders_${outletId}`
        },
        validate: () => !!outletId
      });
    });

    // Explicit unsubscribe event
    socket.on('unsubscribe', (roomName) => {
      unsubscribeFromRoom({ socket, roomName });
    });

    // Handle disconnection (no need to manually leave rooms)
    socket.on('disconnect', () => {
      try {
        console.log('Client disconnected:', socket.id);
        if (socket.userId) {
          activeConnections.delete(socket.userId);
        }
        // No need to manually leave rooms; Socket.IO handles this.
      } catch (error) {
        console.error('Disconnect error:', error);
      }
    });

    // Error handling
    socket.on('error', (error) => {
      console.error('Socket error:', error);
    });
  });

  // Emit meal status update to a customer
  io.emitMealStatus = async (customerAuthUID, data) => {
    if (!customerAuthUID) {
      console.error('[Socket Emit] Invalid customerAuthUID provided');
      return false;
    }
    return emitToRoom({
      io,
      roomName: `meal_status_${customerAuthUID}`,
      event: 'meal_status_update',
      payload: { success: true, ...data }
    });
  };

  // Emit new order notification to an outlet
  io.emitNewOrder = async (outletId, orderData) => {
    if (!outletId) {
      console.error('[Socket Emit] Invalid outletId provided');
      return false;
    }
    return emitToRoom({
      io,
      roomName: `new_orders_${outletId}`,
      event: 'new_order_notification',
      payload: {
        success: true,
        orderData,
        outletId,
        timestamp: new Date().toISOString()
      }
    });
  };

  // Emit order cancellation status to an outlet
  io.emitOrderCancellationStatus = async (outletId, orderId, orderSequenceId, currentOrderStatusId) => {
    if (!outletId) {
      console.error('[Socket Emit] Invalid outletId provided');
      return false;
    }
    return emitToRoom({
      io,
      roomName: `order_cancellation_status_${outletId}`,
      event: 'order_cancellation_status',
      payload: {
        success: true,
        orderId,
        orderSequenceId,
        currentOrderStatusId,
        timestamp: new Date().toISOString(),
        outletId
      }
    });
  };

  io.emitReadyOrder = async (outletId, orderData) => {
    console.log(`[Socket Emit] Emitting ready order notification for outlet ${outletId}`);
    if (!outletId) {
      console.error('[Socket Emit] Invalid outletId provided');
      return false;
    }
    return emitToRoom({
      io,
      roomName: `new_orders_${outletId}`,
      event: 'ready_order_notification',
      payload: {
        success: true,
        orderData,
        outletId,
        timestamp: new Date().toISOString()
      }
    });
  };

  io.emitClearanceOrder = async (outletId, orderData, status) => {
    console.log(`[Socket Emit] Emitting clearance order notification for outlet ${outletId}`);
    if (!outletId) {
      console.error('[Socket Emit] Invalid outletId provided');
      return false;
    }
    if (status == "requested") {
      return emitToRoom({
        io,
        roomName: `clearance_orders_${outletId}`,
        event: 'clearance_requested',
        payload: {
          success: true,
          orderData,
          outletId,
          timestamp: new Date().toISOString()
        }
      });
    } else {
      return emitToRoom({
        io,
        roomName: `clearance_orders_${outletId}`,
        event: 'clearance_cleared',
        payload: {
          success: true,
          orderData,
          outletId,
          timestamp: new Date().toISOString()
        }
      });
    }
  }
  

  // Add heartbeat to check connection health
  setInterval(() => {
    io.sockets.sockets.forEach((socket) => {
      socket.emit('ping');
    });
  }, 30000); // Every 30 seconds

  return io;
};
