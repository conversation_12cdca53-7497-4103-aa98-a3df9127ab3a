var express = require("express");
var router = express.Router();
var supabaseInstance = require("../services/supabaseClient").supabase;
const logger = require("../services/logger");

router.get("/", function (req, res, next) {
  res.send({ success: true, message: "respond send from campus.js" });
});

router.post("/createCampus", async (req, res) => {
  const { campusName, address, cityId, tag, latitude, longitude, organizationCode } = req.body;
  try {
    if(organizationCode){
      var organizationId = null;
      var { data: organizationData, error: organizationError } = await supabaseInstance
      .from("Organization")
      .select("organizationId")
      .eq("organizationCode", organizationCode)
      .maybeSingle();

      if (organizationError) {
        throw organizationError;
      }

      if(!organizationData){
        // add the organization
        var { data: organizationData, error: organizationError } = await supabaseInstance
        .from("Organization")
        .insert({organizationCode})
        .select("*")
        .maybeSingle();

        if (organizationError) {
          throw organizationError;
        }
      }
      organizationId = organizationData.organizationId;
    }
    
    
    const { data, error } = await supabaseInstance
      .from("Campus")
      .insert({ campusName, address, cityId, tag, latitude, longitude, organizationId })
      .select("*")
      .maybeSingle();

    if (data) {
      res.send({
        success: true,
        message: "Campus created successfully",
        data: data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    logger.error("Error in createCampus", error);
    res.status(500).json({ success: false, error: error.message || error });
  }
});

// =================== white label =======================================================
router.post("/wl/getCampusTag", async (req, res) => {
  const { campusName } = req.body;

  try {
    const { data, error } = await supabaseInstance
      .from("Campus")
      .select("tag")
      .eq("campusName", campusName);


    if (error) {
      throw error;
    }

    // if (data.length === 0) {
    //   res.status(404).json({
    //     success: false,
    //     message: "No campus found with the given name",
    //   });
    // } else if (data.length > 1) {
    //   res.status(400).json({
    //     success: false,
    //     message: "Multiple campuses found with the same name",
    //   });
    // } else {
    res.send({
      success: true,
      tag: data[0].tag,
    });
    // }
  } catch (error) {
    logger.error("Error in getCampusTag", error);
    res.status(500).json({ success: false, error: error.message || error });
  }
});

router.get("/wl/getCampusList", async (req, res) => {
  const { page, perPage } = req.query;
  const pageNumber = parseInt(page) || 1;
  const itemsPerPage = parseInt(perPage) || 10;
  const tag = req.body.tag;

  try {
    const { data, error, count } = await supabaseInstance
      .from("Campus")
      .select("*,cityId(*)", { count: "exact" })
      .eq("tag", tag)
      .range((pageNumber - 1) * itemsPerPage, pageNumber * itemsPerPage - 1)
      .order("updated_at", { ascending: false });

    if (data) {
      const totalPages = Math.ceil(count / itemsPerPage);
      res.status(200).json({
        success: true,
        data,
        meta: {
          page: pageNumber,
          perPage: itemsPerPage,
          totalPages,
          totalCount: count,
        },
      });
    } else {
      throw error;
    }
  } catch (error) {
    logger.error("Error in getCampusList", error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// =================== white label =======================================================

router.get("/getCampusList", async (req, res) => {
  const { page, perPage } = req.query;
  const pageNumber = parseInt(page) || 1;
  const itemsPerPage = parseInt(perPage) || 10;

  try {
    const { data, error, count } = await supabaseInstance
      .from("Campus")
      .select("*,cityId(*)", { count: "exact" })
      .range((pageNumber - 1) * itemsPerPage, pageNumber * itemsPerPage - 1)
      .order("updated_at", { ascending: false });

    if (data) {
      const totalPages = Math.ceil(count / itemsPerPage);
      res.status(200).json({
        success: true,
        data,
        meta: {
          page: pageNumber,
          perPage: itemsPerPage,
          totalPages,
          totalCount: count,
        },
      });
    } else {
      throw error;
    }
  } catch (error) {
    logger.error("Error in getCampusList", error);
    res.status(500).json({ success: false, error: error.message });
  }
});

//======================= white label ==================================================
router.post("/wl/getCampus/:cityId", async (req, res) => {
  const { cityId } = req.params;
  const { tag, organizationCode } = req.body;

  // Validate required tag
  if (!tag) {
    return res.status(400).json({
      success: false,
      error: "Tag is required"
    });
  }

  try {
    // First, resolve the cityId if it's a city name instead of UUID
    let resolvedCityId = cityId;
    if (!cityId.includes("-")) {
      const { data: cityData, error: cityError } = await supabaseInstance
        .from("City")
        .select("cityId")
        .ilike("city", cityId)
        .maybeSingle();

      if (cityError) throw cityError;
      if (!cityData) {
        return res.status(404).json({
          success: false,
          error: "City not found. Please select manually."
        });
      }
      resolvedCityId = cityData.cityId;
    }

    // Build the base query
    let query = supabaseInstance
      .from("Campus")
      .select("*, cityId(city)")
      .eq("isDelete", false)
      .eq("isActive", true)
      .eq("cityId", resolvedCityId)
      .eq("tag", tag);

    // If organizationCode is provided, add organization filter
    if (organizationCode) {
      const { data: orgData, error: orgError } = await supabaseInstance
        .from("Organization")
        .select("organizationId")
        .eq("organizationCode", organizationCode)
        .maybeSingle();

      if (orgError) throw orgError;
      if (orgData) {
        query = query.eq("organizationId", orgData.organizationId);
      }
    }

    // Execute the final query
    const { data, error } = await query;
    if (error) throw error;

    return res.status(200).json({
        success: true,
      data: data || []
      });

  } catch (error) {
    logger.error("Error in getCampusList", error);
    return res.status(500).json({
      success: false,
      error
    });
  }
});

//======================= white label ==================================================

router.get("/getCampus/:cityId", async (req, res) => {
  const { cityId } = req.params;
  const { organizationCode } = req.query;
  try {
    let query = supabaseInstance
      .from("Campus")
      .select("*, cityId(city)")
      .eq("isDelete", false)
      .eq("isActive", true);


    if(organizationCode){
      var { data: organizationData, error: organizationError } = await supabaseInstance
      .from("Organization")
      .select("organizationId")
      .eq("organizationCode", organizationCode)
      .maybeSingle();

      if (organizationError) {
        throw organizationError;
      }

      if(organizationData){
        var organizationId = organizationData.organizationId;
      }
    }

    if (cityId?.includes("-")) {
      query = query.eq("cityId", cityId);
    } else {
      const cityResponse = await supabaseInstance
        .from("City")
        .select("cityId, city")
        .ilike("city", cityId)
        .maybeSingle();

      if (cityResponse?.data?.cityId) {
        query = query.eq("cityId", cityResponse?.data?.cityId);
      } else {
        return res.status(500).json({
          success: false,
          error: "City not found please select manually.",
        });
      }
    }

    let { data, error } = await query;

    if (data) {
        if(organizationId){
        data = data.filter(campus => campus.organizationId === organizationId);
        }
      res.status(200).json({
        success: true,
        data
      });
    } else {
      throw error;
    }
  } catch (error) {
    logger.error("Error in getCampus", error);
    res.status(500).json({ success: false, error: error.message });
  }
});

//=============================== white label ==========================================
router.post("/wl/updateCampus/:campusId", async (req, res) => {
  const { campusId } = req.params;
  const { campusName, address, isDelete, cityId, tag, latitude, longitude } = req.body;
  // const { name, email, mobile, role } = req.body;

  try {
    const { data, error } = await supabaseInstance
      .from("Campus")
      .update({ campusName, address, isDelete, cityId, tag, latitude, longitude })
      .eq("campusId", campusId)
      .select("*")
      .maybeSingle();

    if (data) {
      res.status(200).json({
        success: true,
        message: "Data updated succesfully",
        data: data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    logger.error("Error in updateCampus", error);
    res.status(500).json({ success: false, error: error.message });
  }
});

//=============================== white label ==========================================

router.post("/updateCampus/:campusId", async (req, res) => {
  const { campusId } = req.params;
  var { campusName, address, isDelete, cityId, tag, latitude, longitude, organizationId, organizationCode, isActive } = req.body;
  // const { name, email, mobile, role } = req.body;

  try {
    // check if organizationCode exists
    if(organizationCode !== null && organizationCode !== ""){
      var { data: organizationData, error: organizationError } = await supabaseInstance
      .from("Organization")
      .select("organizationId")
      .eq("organizationCode", organizationCode)
      .maybeSingle();

      if (organizationError) {
        throw organizationError;
      }

      if(!organizationData){
        // add the organization
        var { data: organizationData, error: organizationError } = await supabaseInstance
        .from("Organization")
        .insert({organizationCode})
        .select("*")
        .maybeSingle();

        if (organizationError) {
          throw organizationError;
        }
      }
      organizationId = organizationData.organizationId;
    }

    if(organizationId === ""){organizationId = null;}
    const { data, error } = await supabaseInstance
      .from("Campus")
      .update({ campusName, address, isDelete, cityId, tag, latitude, longitude, organizationId, isActive })
      .eq("campusId", campusId)
      .select("*")
      .maybeSingle();

    if (data) {
      res.status(200).json({
        success: true,
        message: "Data updated succesfully",
        data: data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    logger.error("Error in deleteCampus", error);
    res.status(500).json({ success: false, error: error.message });
  }
});

router.post("/deleteCampus/:id", async (req, res) => {
  const { id } = req.params;
  try {
    const { data, error } = await supabaseInstance
      .from("Campus")
      .update({ isDelete: true })
      .eq("campusId", id)
      .select();

    if (data) {
      res.status(200).json({
        success: true,
        message: "Data deleted succesfully",
        data: data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    logger.error("Error in getAllCampusList", error);
    res.status(500).json({ success: false, error: error.message });
  }
});

router.get("/getAllCampusList", async (req, res) => {
  try {
    const { data, error } = await supabaseInstance
      .from("Campus")
      .select("*")
      .order("created_at", { ascending: false });
    if (data) {
      res.status(200).json({
        success: true,
        message: "Data fetch succesfully",
        data: data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    logger.error("Error in getAllCampusList", error);
    res.status(500).json({ success: false, error: error.message });
  }
});

//================================= white label =====================================
router.get("/wl/getCityCampusList", async (req, res) => {
  const { cityId } = req.query;
  const tag = req.body.tag;
  // const tag = "abcd";
  try {
    if (!tag) throw new Error("Tag is required");
    const { data, error } = await supabaseInstance
      .from("Campus")
      .select("*")
      .eq("cityId", cityId)
      .eq("tag", tag);
    if (data) {
      res.status(200).json({
        success: true,
        message: "Data fetch succesfully",
        data: data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    logger.error("Error in getCityCampusList", error);
    res.status(500).json({ success: false, error: error.message });
  }
});

//============================== /white label =================================

router.get("/getCityCampusList", async (req, res) => {
  const { cityId } = req.query;
  try {
    const { data, error } = await supabaseInstance
      .from("Campus")
      .select("*")
      .eq("cityId", cityId)
      .order("campusName", { ascending: true });
    if (data) {
      res.status(200).json({
        success: true,
        message: "Data fetch succesfully",
        data: data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    logger.error("Error in getCityCampusList", error);
    res.status(500).json({ success: false, error: error.message });
  }
});

router.get("/getCampusLocations/:cityName", async (req, res) => {
  let { cityName } = req.params;

  // make the first letter of the city name capital
  cityName = cityName.charAt(0).toUpperCase() + cityName.slice(1).toLowerCase();
  try {
    // get cityId from city name
    const { data: cityData, error: cityError } = await supabaseInstance
      .from("City")
      .select("cityId")
      .eq("city", cityName)
      .maybeSingle();

    if (cityError) {
      throw cityError;
    }

    if (!cityData) {
      return res.status(404).json({
        success: false,
        message: "City not found",
      });
    }

    // get campus locations from cityId
    const { data, error } = await supabaseInstance
      .from("Campus")
      .select("campusId, campusName, address, latitude, longitude")
      .eq("cityId", cityData.cityId)
      .eq("isDelete", false);

    if (error) {
      throw error;
    }

    return res.status(200).json({
      success: true,
      data,
      cityId : cityData.cityId
    });
  } catch (error) {
    logger.error("Error in getCityAndCampusId", error);
    return res.status(500).json({ success: false, error: error.message });
  }
});

router.get("/getCityAndCampusId" , async(req,res) => {
  let {city, campus} = req.query;
  // make the first letter of the city name capital
  city = city.charAt(0).toUpperCase() + city.slice(1).toLowerCase();

  // replace '-' with '%20' in campus name
  campus = campus.replace(/-/g, ' ');
  campus = campus.charAt(0).toUpperCase() + campus.slice(1).toLowerCase();

  try {
    // get cityId from city name
    const { data: cityData, error: cityError } = await supabaseInstance
      .from("City")
      .select("cityId, city")
      .eq("city", city)
      .maybeSingle();

    if (cityError) {
      throw cityError;
    }

    if (!cityData) {
      return res.status(404).json({
        success: false,
        message: "City not found",
      });
    }

    // get campus locations from cityId
    const { data : campusData, error } = await supabaseInstance
      .from("Campus")
      .select("campusId, campusName")
      .eq("cityId", cityData.cityId)
      .ilike("campusName", campus)
      .eq("isDelete", false).maybeSingle() ;

    if (error) {
      throw error;
    }

    return res.status(200).json({
      success: true,
      campusId : campusData.campusId,
      campusName : campusData.campusName,
      cityId : cityData.cityId,
      cityName : cityData.city
    });
  } catch (error) {
    logger.error("Error in getCityAndCampusId", error);
    return res.status(500).json({ success: false, error: error.message });
  }
  
});

module.exports = router;
