name: Deploy Backend GCP

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Deploy to EC2
      uses: appleboy/scp-action@master
      with:
        host: ${{ secrets.GCP_SSH_HOST }}
        username: ${{ secrets.GCP_SSH_USER }}
        key: ${{ secrets.GCP_SSH_PRIVATE_KEY }}
        source: "."
        target: "~/apps/backend"
        exclude: "node_modules/"
    
    - name: Execute remote commands
      uses: appleboy/ssh-action@master
      with:
        host: ${{ secrets.GCP_SSH_HOST }}
        username: ${{ secrets.GCP_SSH_USER }}
        key: ${{ secrets.GCP_SSH_PRIVATE_KEY }}
        script: |
          cd ~/apps/backend
          npm install
          pm2 delete backend || true
          pm2 start ./bin/www --name backend
          pm2 save
