const { createValidator } = require('../../middleware/validationErrorMiddleware');
const { body } = require('express-validator');

const validationRules = {
    //===================== whiteListUser ============================
    validateWhiteListUser: [
    body('email')
        .exists({ checkFalsy: true }).withMessage('Email is required.').bail()
        .isEmail().withMessage('Invalid email address.').bail(),

    body('outletId')
        .exists({ checkFalsy: true }).withMessage('Outlet ID is required.').bail()
        .isUUID().withMessage('Outlet ID must be a valid UUID.'),

    body('mealTypeIds')
        .exists({ checkFalsy: true }).withMessage('Meal Type IDs are required.').bail()
        .isArray().withMessage('Meal Type IDs must be an array.').bail()
        .custom((value) => {
            if (value.length === 0) {
                throw new Error('Meal Type IDs cannot be empty.');
            }
            return true
        }),

    body('mealTypeIds.*')
        .isInt().withMessage('Each Meal Type ID must be a valid integer.').bail()
        .toInt(),

    body('validity') //it is a object in which there will be validFrom and validTill
        .exists({ checkFalsy: true }).withMessage('Validity is required.').bail()
        .isObject().withMessage('Validity must be an object.'),

    body('validity.validFrom') // date in yyyy-mm-dd format
        .exists({ checkFalsy: true }).withMessage('Valid From date is required.').bail()
        .isISO8601({ strict: true, strictSeparator: true }).withMessage('Valid From date must be in yyyy-mm-dd format.'),

    body('validity.validTill') // date in yyyy-mm-dd format
        .exists({ checkFalsy: true }).withMessage('Valid Till date is required.').bail()
        .isISO8601({ strict: true, strictSeparator: true }).withMessage('Valid Till date must be in yyyy-mm-dd format.')
    ],
    //===================== /whiteListUser ============================

    //===================== whiteListUsers ============================
    validateWhitelistUsers: [
        body('userObjs')
            .exists({ checkFalsy: true }).withMessage('User objects are required.').bail()
            .isArray().withMessage('User objects must be an array.').bail()
            .custom((value) => {
                if (value.length === 0) {
                    throw new Error('User objects cannot be empty.');
                }
                return true;
            }),

        body('userObjs.*.email')
            .exists({ checkFalsy: true }).withMessage('Email is required.').bail()
            .isEmail().withMessage('Invalid email address.').bail(),

        body('outletId')
            .exists({ checkFalsy: true }).withMessage('Outlet ID is required.').bail()
            .isUUID().withMessage('Outlet ID must be a valid UUID.'),

        body('mealTypeIds')
            .exists({ checkFalsy: true }).withMessage('Meal Type IDs are required.').bail()
            .isArray().withMessage('Meal Type IDs must be an array.').bail()
            .custom((value) => {
                if (value.length === 0) {
                    throw new Error('Meal Type IDs cannot be empty.');
                }
                return true;
            }),

        body('mealTypeIds.*')
            .isInt().withMessage('Each Meal Type ID must be a valid integer.').bail()
            .toInt(),

        body('validity') //it is a object in which there will be validFrom and validTill
            .exists({ checkFalsy: true }).withMessage('Validity is required.').bail()
            .isObject().withMessage('Validity must be an object.'),

        body('validity.validFrom') // date in yyyy-mm-dd format
            .exists({ checkFalsy: true }).withMessage('Valid From date is required.').bail()
            .isISO8601({ strict: true, strictSeparator: true }).withMessage('Valid From date must be in yyyy-mm-dd format.'),

        body('validity.validTill') // date in yyyy-mm-dd format
            .exists({ checkFalsy: true }).withMessage('Valid Till date is required.').bail()
            .isISO8601({ strict: true, strictSeparator: true }).withMessage('Valid Till date must be in yyyy-mm-dd format.')
    ],
    //===================== /whiteListUsers ============================

    //===================== editWhitelists ============================
    // whitelistingIds, mealTypeIds, validity, outletId
    validateEditWhitelists: [
        body('customerAuthUIDs')
            .exists({ checkFalsy: true }).withMessage('customerAuthUIDs are required.').bail()
            .isArray().withMessage('Whitelisting IDs must be an array.').bail()
            .custom((value) => {
                if (value.length === 0) {
                    throw new Error('Whitelisting IDs cannot be empty.');
                }
                return true;
            }),
        
        body('customerAuthUIDs.*')
            .isUUID().withMessage('Each customerAuthUID must be a valid uuid.'),
        
        body('mealTypeIds')
            .exists({ checkFalsy: true }).withMessage('Meal Type IDs are required.')
            .isArray().withMessage('Meal Type IDs must be an array.')
            .custom((value) => {
                if (value.length === 0) {
                    throw new Error('Meal Type IDs cannot be empty.');
                }
                return true;
            }),
        
        body('mealTypeIds.*')
            .isInt().withMessage('Each Meal Type ID must be a valid integer.'),
        
        body('validity') //it is a object in which there will be validFrom and validTill
            .exists({ checkFalsy: true }).withMessage('Validity is required.')
            .isObject().withMessage('Validity must be an object.'),
        
        body('validity.validFrom') // date in yyyy-mm-dd format
            .exists({ checkFalsy: true }).withMessage('Valid From date is required.')
            .isISO8601().withMessage('Valid From date must be in yyyy-mm-dd format.'),
        
        body('validity.validTill') // date in yyyy-mm-dd format
            .exists({ checkFalsy: true }).withMessage('Valid Till date is required.')
            .isISO8601().withMessage('Valid Till date must be in yyyy-mm-dd format.'),
        
        body('outletId')
            .exists({ checkFalsy: true }).withMessage('Outlet ID is required.')
            .isUUID().withMessage('Outlet ID must be a valid UUID.')
    ],
    //====================== /editWhitelist ============================
};

// Build validators dynamically to avoid repetition
const validators = Object.fromEntries(
    Object.entries(validationRules).map(([key, rules]) => [key, createValidator(rules)])
);


module.exports = validators;

