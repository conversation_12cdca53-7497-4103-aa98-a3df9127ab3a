const supabaseInstance = require("../../services/supabaseClient").supabase;

const whiteListingMiddleware = async (req, res, next) => {
  let { outletId } = req.params;
  let { customerAuthUID } = req.userData;

  // check in db if the user is already whiteListed or not.
  let { data: whiteListData, error: whiteListError } = await supabaseInstance
    .from("Mess_Whitelist")
    .select("*")
    .eq("customerAuthUID", customerAuthUID)
    .eq("outletId", outletId)
    .single();

  if (whiteListError && whiteListError.code !== "PGRST116") {
    return res
      .status(400)
      .json({ success: false, message: "Error in getting whiteList data" });
  }

  if (!whiteListData) {
    return res
      .status(400)
      .json({ success: false, message: "User is not whiteListed" });
  }

  next();
};

// function of return true if the user is whiteListed for that outlet else false
async function isWhiteListed(customerAuthUID, outletId) {
  let { data: whiteListData, error: whiteListError } = await supabaseInstance
    .from("Mess_Whitelist")
    .select("*")
    .eq("customerAuthUID", customerAuthUID)
    .eq("outletId", outletId)
    .single();

  if (whiteListError && whiteListError.code !== "PGRST116") {
    return false;
  }

  if (!whiteListData) {
    return false;
  }

  return true;
};

module.exports = {isWhiteListed, whiteListingMiddleware};
