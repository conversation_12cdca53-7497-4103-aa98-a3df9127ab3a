var express = require("express");
var router = express.Router();
var supabaseInstance = require("../../services/supabaseClient").supabase;
const logger = require("../../services/logger");

const multer = require("multer");
const upload = multer();

router.get('/', async (req, res) => { 
  res.status(200).json({ success: true, message: "Rating API is working" });
});

router.post("/createRating", async (req, res) => {

    const { customerAuthUID, outletId, message, star, orderId  } = req.body;
    try {
    
      const { data, error } = await supabaseInstance
        .from("Review")
        .insert({customerAuthUID, outletId, message, star, orderId})
        .select("*")
  
      if (data) {
        res.status(200).json({
          success: true,
          data:data
        });
      } else {
        throw error;
      }
     
    } catch (error) {
      logger.error("Error in rating/createRating", error);
      if (error.code === "23505") {
        res.send({ success: false, message: "Rating Already Taken" });
      }else{
        res.status(500).json({ success: false, error: error.message });
      }
    }
  });
  
router.get("/getRating/:outletId", async (req, res) => {
    const { outletId } = req.params;
    try {
        const { data, error } = await supabaseInstance
            .from("Review")
            .select("*")
            .eq("outletId", outletId)

        if (data) {
            res.status(200).json({
                success: true,
                data: data,
            });
        } else {
            throw error
        }
    } catch (error) {
      logger.error("Error in rating/getRating", error);
      res.status(500).json({ success: false, error: error.message });
    }
});

router.get("/getRatingByCustomer/:customerAuthUID", async (req, res) => {
  const { customerAuthUID } = req.params;
  try {
      const { data, error } = await supabaseInstance
          .from("Review")
          .select("*")
          .eq("customerAuthUID", customerAuthUID)

      if (data) {
          res.status(200).json({
              success: true,
              data: data,
          });
      } else {
          throw error
      }
  } catch (error) {
      logger.error("Error in rating/getRatingByCustomer", error);
      res.status(500).json({ success: false, error: error.message });
  }
});

router.delete("/deleteRating/:reviewId", async (req, res) => {
  const { reviewId } = req.params;
  try {
    const { data, error } = await supabaseInstance
      .from("Review")
      .delete()
      .eq("reviewId",reviewId)
      .select("*")

    if (data) {
      res.status(200).json({
        success: true,
        message:"Rating Deleted"
      });
    } else {
      throw error
    }
  } catch (error) {
    logger.error("Error in rating/deleteRating", error);
    res.status(500).json({ success: false, error: error });
  }
});

/**
 * @description Upload review images
 * @route POST /rating/uploadReviewImages
 * @param {string} orderId - The ID of the review to associate the images with
 * @param {Array} images - The images to upload (max 3)
 * @returns {Object} - JSON response with success status and image URLs
 */
router.post('/uploadReviewImages', upload.array('images', 3), async (req, res) => {
  const { orderId } = req.body;
  if (!orderId) {
    return res.status(400).json({ success: false, message: 'orderId is required' });
  }
  if (!req.files || req.files.length === 0) {
    return res.status(400).json({ success: false, message: 'At least one image is required' });
  }
  if (req.files.length > 3) {
    return res.status(400).json({ success: false, message: 'Maximum 3 images allowed' });
  }

  try {
    const publicUrls = [];
    for (let i = 0; i < req.files.length; i++) {
      const file = req.files[i];
      const fileName = `${orderId}-${String(i + 1).padStart(2, '0')}.webp`;
      const { error } = await supabaseInstance.storage
        .from('review-images')
        .upload(fileName, file.buffer, {
          cacheControl: '3600',
          upsert: true,
          contentType: 'image/webp',
        });
      if (error) {
        logger.error('Error uploading review image', error);
        return res.status(500).json({ success: false, message: 'Error uploading image', error: error.message });
      }
      // Get public URL
      const publicUrlResponse = await supabaseInstance.storage
        .from('review-images')
        .getPublicUrl(fileName);
      if (publicUrlResponse?.data?.publicUrl) {
        publicUrls.push(publicUrlResponse.data.publicUrl);
      } else {
        logger.error('Error getting public URL for review image', publicUrlResponse.error);
        return res.status(500).json({ success: false, message: 'Error getting public URL', error: publicUrlResponse.error });
      }
    }
    return res.status(200).json({ success: true, message: 'Images uploaded successfully', imageUrls: publicUrls });
  } catch (error) {
    logger.error('Error in rating/uploadReviewImages', error);
    return res.status(500).json({ success: false, message: 'Internal server error', error: error.message });
  }
});


router.post('/addOrderReview', async (req, res) => { 
  const { orderId, customerAuthUID, outletId, message, star } = req.body;
  
  if(!orderId || !customerAuthUID || !outletId || !star) {
    return res.status(400).json({ success: false, message: `Missing: ${orderId ? "" : "orderId"} ${customerAuthUID ? "" : "customerAuthUID"} ${star ? "" :"star"}` });
  }

  try {
    //upsert the review for unique orderId and customerAuthUID
    const { error } = await supabaseInstance
      .from("Order_Review")
      .upsert({ orderId, customerAuthUID, outletId, message, star }, { onConflict: ['customerAuthUID', 'orderId'] });
    
    if (error) {
      logger.error("Error in rating/addOrderReview", error);
      throw error
    }

    
    res.status(200).json({ success: true, message: "Review added successfully" });
    
  } catch (error) {
    logger.error("Error in rating/addOrderReview", error);
    return res.status(500).json({ success: false, message: "Internal server error", error: JSON.stringify(error, null, 2) });
  }
});

router.get('/getOrderReview/:orderId', async (req, res) => {
  const { orderId } = req.params;

  if (!orderId) {
    return res.status(400).json({ success: false, message: 'orderId is required' });
  }

  try {
    const { data, error } = await supabaseInstance
      .from("Order_Review")
      .select("*")
      .eq("orderId", orderId)
      .maybeSingle();

    if (error) {
      logger.error("Error in rating/getOrderReview", error);
      throw error;
    }

    // Handle image fetching if review data exists
    if (data) {
      try {
        // Get list of files that start with the orderId to check which images exist
        const { data: fileList, error: listError } = await supabaseInstance.storage
          .from('review-images')
          .list('', {
            search: `${orderId}-`
          });

        if (listError) {
          logger.warn("Error listing review images:", listError);
          data.images = [];
        } else {
          // Create a set of existing filenames for quick lookup
          const existingFiles = new Set(fileList.map(file => file.name));

          // Generate URLs only for files that exist
          const images = [];
          for (let j = 1; j <= 3; j++) {
            const fileName = `${orderId}-${String(j).padStart(2, '0')}.webp`;

            if (existingFiles.has(fileName)) {
              const { data: urlData, error: urlError } = supabaseInstance.storage
                .from('review-images')
                .getPublicUrl(fileName);

              if (!urlError && urlData.publicUrl) {
                images.push(urlData.publicUrl);
              }
            }
          }

          data.images = images;
        }

      } catch (imageError) {
        logger.error("Error processing review images:", imageError);
        // Set empty array if image processing fails
        data.images = [];
      }
    }

    res.status(200).json({ success: true, data: data });
  } catch (error) {
    logger.error("Error in rating/getOrderReview", error);
    return res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message
    });
  }
});

module.exports = router;