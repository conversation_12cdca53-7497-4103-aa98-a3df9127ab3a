var express = require("express");
var router = express.Router();
var easebuzzConfig = require("../../configs/easebuzzConfig").config;
var supabaseInstance = require("../../services/supabaseClient").supabase;
const SHA512 = require("crypto-js").SHA512;
const axios = require("axios");
const razorpayConfig = require("../../configs/razorpayConfig").config;
const Razorpay = require("razorpay");
const logger = require("../../services/logger");

// Initialize Razorpay
const razorpay = new Razorpay({
  key_id: razorpayConfig.key_id,
  key_secret: razorpayConfig.key_secret
});

router.get("/", (req, res) => {
  return res
    .status(200)
    .json({ success: true, message: "Response from wallet/userWallet" });
});


const generateHash = (hashstring) => {
  return SHA512(hashstring).toString();
};


router.post("/initiate-wallet-topup", async (req, res) => {
  const { amount, description, customerAuthUID, firstname, phone, email } = req.body;

  if (!amount || !description || !customerAuthUID || !phone || !email || !firstname) {
    return res.status(400).json({
      success: false,
      message: "Missing required fields"
    });
  }

  try {
    // Get or create wallet
    let { data: walletData, error: walletError } = await supabaseInstance
      .from("Wallet")
      .select("*")
      .eq("customerAuthUID", customerAuthUID)
      .maybeSingle();

    if (walletError) throw walletError;

    if (!walletData) {
      const walletInsertResponse = await supabaseInstance
        .from("Wallet")
        .insert({
          customerAuthUID,
          balance: 0,
        })
        .select("*")
        .maybeSingle();
      walletData = walletInsertResponse?.data;
    }

    // Create wallet transaction
    const transactionResponse = await supabaseInstance
      .from("Wallet_Transaction")
      .insert({
        bearer: "CUSTOMER",
        transactionType: "CREDIT",
        description,
        customerAuthUID,
        amount,
        walletId: walletData?.walletId,
        status: "pending"
      })
      .select("*")
      .maybeSingle();

    if (transactionResponse?.error) throw transactionResponse.error;

    if (!transactionResponse?.data) {
      throw new Error("Failed to create transaction");
    }

    // Create Razorpay order
    const orderOptions = {
      amount: amount * 100, // Razorpay expects amount in paise
      currency: "INR",
      receipt: transactionResponse.data.walletTransactionId,
      payment_capture: 1,
      notes: {
        description,
        customerAuthUID,
        walletTransactionId: transactionResponse.data.walletTransactionId
      }
    };

    const order = await razorpay.orders.create(orderOptions);

    // Update transaction with Razorpay order details
    const transactionUpdateResponse = await supabaseInstance
      .from("Wallet_Transaction")
      .update({
        razorpay_order_id: order.id,
        razorpay_order_details: order
      })
      .eq("walletTransactionId", transactionResponse.data.walletTransactionId)
      .select("*")
      .maybeSingle();

    if (transactionUpdateResponse?.error) throw transactionUpdateResponse.error;

    return res.status(200).json({
      success: true,
      response: {
        orderId: order.id,
        amount: order.amount,
        currency: order.currency
      },
      walletTransactionId: transactionResponse.data.walletTransactionId
    });

  } catch (error) {
    logger.error("Error in wallet/initiate-wallet-topup", error);
    return res.status(500).json({
      success: false,
      message: "Failed to initiate wallet topup",
      error: error.message
    });
  }
});

router.post("/wallet-topup-response", async (req, res) => {
  const { razorpay_payment_id, razorpay_order_id, razorpay_signature } = req.body;

  try {
    // Verify payment signature
    const crypto = require('crypto');
    const body = razorpay_order_id + "|" + razorpay_payment_id;
    const expectedSignature = crypto
      .createHmac("sha256", razorpayConfig.key_secret)
      .update(body.toString())
      .digest("hex");

    if (expectedSignature !== razorpay_signature) {
      return res.status(400).json({
        success: false,
        message: "Invalid payment signature"
      });
    }

    // Get transaction details
    const { data: transactionData, error: transactionError } = await supabaseInstance
      .from("Wallet_Transaction")
      .select("*")
      .eq("razorpay_order_id", razorpay_order_id)
      .maybeSingle();

    if (transactionError) throw transactionError;

    if (!transactionData) {
      return res.status(404).json({
        success: false,
        message: "Transaction not found"
      });
    }

    // Update transaction status
    const { data: updatedTransaction, error: updateError } = await supabaseInstance
      .from("Wallet_Transaction")
      .update({
        status: "success",
        razorpay_payment_id,
        razorpay_signature
      })
      .eq("walletTransactionId", transactionData.walletTransactionId)
      .select("*")
      .maybeSingle();

    if (updateError) throw updateError;

    // Update wallet balance
    const { data: walletData, error: walletError } = await supabaseInstance
      .from("Wallet")
      .select("*")
      .eq("customerAuthUID", transactionData.customerAuthUID)
      .maybeSingle();

    if (walletError) throw walletError;

    const updatedBalance = walletData.balance + transactionData.amount;
    const { data: updatedWallet, error: walletUpdateError } = await supabaseInstance
      .from("Wallet")
      .update({ balance: updatedBalance })
      .eq("customerAuthUID", transactionData.customerAuthUID)
      .select("*")
      .maybeSingle();

    if (walletUpdateError) throw walletUpdateError;

    return res.status(200).json({
      success: true,
      message: "Wallet topup successful",
      transaction: updatedTransaction,
      wallet: updatedWallet
    });

  } catch (error) {
    logger.error("Error in wallet/wallet-topup-response", error);
    return res.status(500).json({
      success: false,
      message: "Failed to process wallet topup response",
      error: error.message
    });
  }
});


// get all transactions for a customer
router.get("/getWalletTransactions/:customerAuthUID", async (req, res) => {
  const { customerAuthUID } = req.params;
  const { from, to, limit = 10, page = 1 } = req.query;

  // Input validation
  if (!customerAuthUID?.trim()) {
    return res.status(400).json({
      success: false,
      message: "Customer Auth UID is required"
    });
  }

  try {
    // Validate and sanitize pagination parameters
    const pageNum = Math.max(1, parseInt(page) || 1);
    const limitNum = Math.min(100, Math.max(1, parseInt(limit) || 10));
    const offset = (pageNum - 1) * limitNum;

    // Validate date range if provided
    let dateRange = null;
    if (from && to) {
      const fromDate = new Date(from);
      const toDate = new Date(to);

      if (isNaN(fromDate.getTime()) || isNaN(toDate.getTime())) {
        return res.status(400).json({
          success: false,
          message: "Invalid date format. Please use YYYY-MM-DD format"
        });
      }

      if (fromDate > toDate) {
        return res.status(400).json({
          success: false,
          message: "From date cannot be later than to date"
        });
      }

      // Set end of day for 'to' date
      toDate.setHours(23, 59, 59, 999);

      dateRange = {
        from: fromDate.toISOString(),
        to: toDate.toISOString()
      };
    }

    // Build single query with both data and count
    let query = supabaseInstance
      .from("Wallet_Transaction")
      .select(`
        walletTransactionId,
        amount,
        bearer,
        transactionType,
        description,
        created_at,
        outletId,
        status,
        customerAuthUID,
        orderId
      `, { count: "exact" })
      .eq("customerAuthUID", customerAuthUID)
      .order("created_at", { ascending: false });

    // Apply date filters if provided
    if (dateRange) {
      query = query
        .gte("created_at", dateRange.from)
        .lte("created_at", dateRange.to);
    }

    // Execute query with pagination
    const { data: walletTransactions, count: totalCount, error } = await query
      .range(offset, offset + limitNum - 1);

    if (error) throw error;

    // Calculate pagination metadata
    const totalPages = Math.ceil(totalCount / limitNum);

    return res.status(200).json({
      success: true,
      response: walletTransactions || [],
      metadata: {
      pagination: {
        currentPage: pageNum,
        limit: limitNum,
        totalRecords: totalCount,
        totalPages,
        hasNextPage: pageNum < totalPages,
        hasPrevPage: pageNum > 1,
        nextPage: pageNum < totalPages ? pageNum + 1 : null,
        prevPage: pageNum > 1 ? pageNum - 1 : null
      },
      ...(dateRange && {
        dateRange: {
          from: dateRange.from,
            to: dateRange.to
          }
        })
      }
    });

  } catch (error) {
    logger.error("Error in wallet/getWalletTransactions", error);

    return res.status(500).json({
      success: false,
      message: "Failed to fetch wallet transactions",
      ...(process.env.NODE_ENV === 'development' && { details: error.message })
    });
  }
});

// get wallet data
router.get("/getWalletData/:customerAuthUID", async (req, res) => {
  const { customerAuthUID } = req.params;

  if(!customerAuthUID) {
    return res.status(400).json({ success: false, message: "Customerauthid is required" });
  }
  try{
    const {data : walletData, error: walletError} = await supabaseInstance
    .from("Wallet")
    .select("*")
    .eq("customerAuthUID", customerAuthUID)
    .maybeSingle();

    if(walletError) throw walletError;

    return res.status(200).json({ success: true, response: walletData });
  }
  catch (error) {
    logger.error("Error in wallet/getWalletData", error);
    return res.status(500).json({ success: false, error });
  }
});

// get wallet transactions for multiple outlets
router.post("/getWalletTransactionsForOutlets", async (req, res) => {
  const { outletIds } = req.body;
  let { fromDate, toDate } = req.body;

  try {
    let query = supabaseInstance
      .from("Wallet_Transaction")
      .select("*")
      .order("created_at", { ascending: false });

    if(outletIds && outletIds.length > 0) {
      query = query.in("outletId", outletIds);
    }

    // Only apply date filters if both from and to are provided (yyyy-mm-dd)
    if (fromDate && toDate) {
      let from_date = new Date(fromDate);
      let to_date = new Date(toDate);
      to_date.setHours(23, 59, 59);

      // Validate dates
      if (isNaN(from_date.getTime()) || isNaN(to_date.getTime())) {
        return res.status(400).json({ 
          success: false, 
          message: "Invalid date format. Please use ISO 8601 format (e.g., 2024-11-22T00:00:00Z)" 
        });
      }

      // Format dates to ISO string and handle timezone consistently (yyyy-mm-dd)
      const formattedFromDate = from_date.toISOString();
      const formattedToDate = to_date.toISOString();

      // Add date range filters
      query = query
        .lte("created_at", formattedToDate)
        .gte("created_at", formattedFromDate);
    }

    const { data: walletTransactions, error: walletTransactionsError } = await query;

    if (walletTransactionsError) throw walletTransactionsError;

    return res.status(200).json({
      success: true,
      response: walletTransactions,
      metadata: {
        dateRange: fromDate && toDate ? {
          from: new Date(fromDate).toISOString(),
          to: new Date(toDate).toISOString()
        } : "all",
        outlets : outletIds ? outletIds : "all"
      }
    });
  } catch (error) {
    logger.error("Error in wallet/getWalletTransactionsForOutlets", error);
    return res.status(500).json({ 
      success: false, 
      error: {
        message: "Failed to fetch wallet transactions",
        details: error.message
      }
    });
  }
});

module.exports = router;


