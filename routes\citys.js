var express = require("express");
var router = express.Router();

var supabaseInstance = require("../services/supabaseClient").supabase;
const logger = require("../services/logger");

/* GET users listing. */
router.get("/", function (req, res, next) {
  res.send({ success: true, message: "respond send from admin.js" });
});

//============================= white label ====================================

// router.post("/wl/getCities", async (req, res) => {
//   try {
//     const {tag, organizationCode} = req.body;
    
//     // const tag = "abcd";
//     let { data, error } = await supabaseInstance
//       .from("Campus")
//       .select("*")
//       .eq("tag", tag);

//     let cityId = data[0].cityId;
//     let { data: cityData, error: cityError } = await supabaseInstance
//       .from("City")
//       .select("*, Campus!left(campusName,isDelete)")
//       .eq("cityId", cityId);

//       if(cityError) throw cityError;

//     if (cityData) {
//       res.status(200).json({
//         success: true,
//         message: "Data fetch succesfully",
//         data: cityData,
//       });
//     } else {
//       throw error;
//     }
//   } catch (error) {
//     res.status(500).json({ success: false, error: error.message });
//   }
// });

router.post("/wl/getCities", async (req, res) => {
  try {
    const { tag, organizationCode } = req.body;

    // Validate required tag parameter
    if (!tag) {
      return res.status(400).json({
        success: false,
        message: "tag is required",
      });
    }

    // Build the query with all necessary relations
    const { data, error } = await supabaseInstance
      .from("City")
      .select(`
        *,
        Campus!left(
          campusName,
          isDelete,
          tag,
          Organization!left(
            organizationName,
            organizationCode
          )
        )
      `);

    // Handle Supabase errors
    if (error) {
      logger.error("Error in whitelisted getCities", error);
      return res.status(400).json({
        success: false,
        message: "Failed to fetch data",
        error: error.message
      });
    }

    // If no data is found, return empty array
    if (!data || !Array.isArray(data)) {
      return res.status(200).json({
        success: true,
        message: "No data found",
        data: []
      });
    }

    // Filter cities based on tag and optional organizationCode
    const filteredData = data
      .map(city => ({
        ...city,
        Campus: city.Campus.filter(campus => {
          // Base conditions: check tag and isDelete
          const matchesTag = campus?.tag === tag;
          const isNotDeleted = !campus.isDelete;

          // If organizationCode is provided, include it in filtering
          if (organizationCode) {
            return matchesTag && 
                   isNotDeleted && 
                   campus?.Organization?.organizationCode === organizationCode;
          }

          // If no organizationCode, only filter by tag and isDelete
          return matchesTag && isNotDeleted;
        })
      }))
      .filter(city => city.Campus.length > 0);

    return res.status(200).json({
      success: true,
      message: "Data fetched successfully",
      data: filteredData
    });

  } catch (error) {
    logger.error('Error in whitelisted getCities:', error);
    return res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message
    });
  }
});

// ============================= white label =====================================

router.get("/getCities", async (req, res) => {
  try {
    const {organizationCode} = req.query;

    let { data, error } = await supabaseInstance
      .from("City")
      .select("*, Campus!left(campusName,isDelete, Organization!left(organizationName, organizationCode))");

    if (data) {
      
      if (organizationCode) {
        
        data = data.filter((city) => {
          let campus = city.Campus.filter((campus) => {
            return campus?.Organization?.organizationCode == organizationCode;
          });

          if (campus.length > 0) {
            city.Campus = campus;
            return city;
          }
        });
      }

      res.status(200).json({
        success: true,
        message: "Data fetch succesfully",
        data: data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    logger.error("Error in getCities", error);
    res.status(500).json({ success: false, error: error.message });
  }
});

module.exports = router;
