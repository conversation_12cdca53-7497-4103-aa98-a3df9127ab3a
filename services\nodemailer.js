const nodemailer = require("nodemailer");
const nodemailerConfig = require("../configs/nodemailerConfig").config;

/*
// configure nodemailer for zoho smtp
const transporter = nodemailer.createTransport({
  host: nodemailerConfig.host,
  port: nodemailerConfig.port,
  secure: nodemailerConfig.secure,
  auth: {
    user: nodemailerConfig.user,
    pass: nodemailerConfig.pass,
  },
});
*/

const transporter = nodemailer.createTransport({
  service: 'gmail',
  auth: {
      user: nodemailerConfig.user,
      pass: nodemailerConfig.password,
  },
});

transporter.verify(function (error, success) {
  if (error) {
    console.log("error in nodemailer", error);
  } else if(success) {
    console.log("Server is ready to take our messages");
  } else {
    console.log("Server is not ready to take our messages");
  }
});

module.exports = transporter;
