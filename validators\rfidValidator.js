const { handleValidationErrorsMiddleware } = require('../middleware/validationErrorMiddleware');
const { body } = require('express-validator');

//============================ upsertRfidData ============================
const upsertRfidDataValidationRules = [
  body('rfid')
    .exists({ checkFalsy: true }).withMessage('RFID is required.')
    .isString().withMessage('RFID must be a string.'),
  body('customerAuthUID')
    .exists({ checkFalsy: true }).withMessage('customerAuthUID is required.')
    .isUUID().withMessage('customerAuthUID must be a valid UUID.')
];
// Grouped middleware for upsert RFID data validation and error handling
const validateUpsertRfidData = [
  ...upsertRfidDataValidationRules,
  handleValidationErrorsMiddleware
];
//============================ /upsertRfidData ============================

//============================ deleteRfidData ============================
const deleteRfidDataValidationRules = [
  body('customerAuthUID')
    .exists({ checkFalsy: true }).withMessage('CustomerAuthUID is required.')
    .isUUID().withMessage('CustomerAuthUID must be a UUID.')
];
// Grouped middleware for delete RFID data validation and error handling
const validateDeleteRfidData = [
  ...deleteRfidDataValidationRules,
  handleValidationErrorsMiddleware
];
//============================ /deleteRfidData ============================

// Export validators and error handler using concise ES6 syntax
module.exports = {
  validateUpsertRfidData,
  validateDeleteRfidData,
};