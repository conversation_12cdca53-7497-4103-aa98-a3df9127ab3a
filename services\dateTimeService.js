const moment = require("moment-timezone");

function formatTime(inputTime) {
    const [hours, minutes, seconds] = inputTime.split('.').map(Number);
  let formattedTime;
  formattedTime = Number(`${hours}.${minutes}`);
  return formattedTime;
}

function isTimeInRange(__time, __beforeTime, __afterTime) {
    let time = moment(__time).format('HH:mm:ss')
    let beforeTime = moment(__beforeTime).format('HH:mm:ss')
    let afterTime = moment(__afterTime).format('HH:mm:ss')
    let _selectedTime = formatTime(
        time.replace(/:/g, '.'),
    );
    let _startTime = formatTime(beforeTime.replace(/:/g, '.'));
    let _closeTime = formatTime(afterTime.replace(/:/g, '.'));
  let temp = null;

  _startTime = _startTime == 0 ? 24 : _startTime;
  _closeTime = _closeTime == 0 ? 24 : _closeTime;


  if (_closeTime < _startTime) {
    temp = _startTime;
    _startTime = _closeTime;
    _closeTime = temp;
  }

  const isBetween = _selectedTime >= _startTime && _selectedTime <= _closeTime;
  return isBetween;
}

function getWeekInfo(date = new Date()) {
  const currentDate = new Date(date);

  // Function to get the week number of a given date
  function getWeekNumber(date) {
    const startOfMonth = new Date(date.getFullYear(), date.getMonth(), 1);
    const dayOfWeekStartOfMonth = startOfMonth.getDay() || 7;
    const dayOfMonth = date.getDate();
    return Math.ceil((dayOfMonth + dayOfWeekStartOfMonth - 1) / 7);
  }

  // Function to get the day of the week (1 for Monday, ..., 7 for Sunday)
  function getDayOfWeek(date) {
    const day = date.getDay();
    return day === 0 ? 7 : day;
  }

  // Get current week number and day of week
  const weekNumber = getWeekNumber(currentDate);
  const dayOfWeek = getDayOfWeek(currentDate);

  // Calculate tomorrow's date
  const tomorrow = new Date(currentDate);
  tomorrow.setDate(currentDate.getDate() + 1);

  // Get tomorrow's week number and day of week
  const tomorrowWeekNumber = getWeekNumber(tomorrow);
  const tomorrowDayOfWeek = getDayOfWeek(tomorrow);

  return {
    weekNumber,
    dayOfWeek,
    tomorrowDayOfWeek,
    tomorrowWeekNumber,
  };
}

function getDayNumber(dateString) {
  const date = new Date(dateString);
  const day = date.getDay(); // getDay returns 0 for Sunday, 1 for Monday, etc.

  // Convert day to 1 for Monday, 2 for Tuesday, ..., 7 for Sunday
  const dayNumber = day === 0 ? 7 : day;

  return dayNumber;
}

function getDayNameFromNumber(dayNumber) {
  switch (dayNumber) {
    case 1:
      return "Monday";
    case 2:
      return "Tuesday";
    case 3:
      return "Wednesday";
    case 4:
      return "Thursday";
    case 5:
      return "Friday";
    case 6:
      return "Saturday";
    case 7:
      return "Sunday";
    default:
      return "Invalid day number";
  }
}

function getMaximumWeekNumberForMonth(month = new Date().getMonth(), year = new Date().getFullYear()) { 
  const endDate = new Date(year, month + 1, 0);
  const { weekNumber } = getWeekInfo(endDate);
  return weekNumber;
}

module.exports = {
  isTimeInRange,
  getWeekInfo,
  getDayNumber,
  getDayNameFromNumber,
  getMaximumWeekNumberForMonth,
};
